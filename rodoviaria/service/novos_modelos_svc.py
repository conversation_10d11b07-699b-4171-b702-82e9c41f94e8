import re

from constance import config as const
from constance import config as constance_config

from rodoviaria.api.forms import ServicoForm
from rodoviaria.api.totalbus.models import PadraoBuscarServicoOutput, ServicoTrechoClasse
from rodoviaria.models import Company

VIACAO_ADAMANTINA_INTERNAL_ID = 426
CLASSES_SEMI_LEITO_VIACAO_ADAMANTINA = {"CONVENCIONAL", "SEMILEITO MASTER", "SEMILEITO"}


def get_map_rotinas_integradas():
    """
    Retorna um mapa com as rotinas integradas por empresa, no formato:
    {empresa.internal_id: list[int] # ids de onibus_rotina_id }
    """
    rotina_roderotas = const.ROTINAS_INTEGRADAS_RODEROTAS
    return {"313": [int(r) for r in rotina_roderotas.split(",") if r]}


def get_company_internal_id_integrado_por_rotina(company_internal_id, rotina_onibus_id):
    """
    Essa solução é provisória, mas serve para permitir que determinadas rotinas sejam
    integradas por empresas diferentes das empresas escaladas. É um MVP para permitir a
    integração de grupos RodeRotas híbrido.
    """
    map_rotinas_integradas = get_map_rotinas_integradas()
    for company_id, rotinas in map_rotinas_integradas.items():
        if rotina_onibus_id in rotinas:
            company_internal_id = int(company_id)
            break
    return company_internal_id


def filter_servicos_novos_modelos(
    servicos_response: list[ServicoTrechoClasse] | list[ServicoForm] | list[PadraoBuscarServicoOutput],
    company_internal_id: int,
    modelo_venda: Company.ModeloVenda,
):
    """Viagens de novos modelos estão sendo criadas com serviço iniciando em 9999"""
    pattern = re.compile(r"^9999\d*$")
    servicos = servicos_response

    roderotas_internal_id = 313
    is_rode_rotas = company_internal_id == roderotas_internal_id
    if is_rode_rotas:
        if modelo_venda == Company.ModeloVenda.HIBRIDO:
            servicos = [c for c in servicos_response if pattern.match(str(c.external_id))]
        elif modelo_venda == Company.ModeloVenda.MARKETPLACE:
            servicos = [c for c in servicos_response if not pattern.match(str(c.external_id))]
        return servicos
    is_viacao_adamantina = company_internal_id == VIACAO_ADAMANTINA_INTERNAL_ID
    if is_viacao_adamantina:
        servicos_viacao_adamantina = get_lista_servicos_venda_poltronas_adamantina()
        servicos = [servico for servico in servicos_response if str(servico.external_id) in servicos_viacao_adamantina]
    return servicos


def get_lista_servicos_venda_poltronas_adamantina():
    servicos_compra_poltronas = constance_config.SERVICOS_COMPRA_POLTRONAS_ADAMANTINA
    return [servico for servico in servicos_compra_poltronas.split(",") if servico]


def is_compra_antecipada_poltronas(company_internal_id: int) -> bool:
    return company_internal_id == VIACAO_ADAMANTINA_INTERNAL_ID
