import collections
import json
import logging
from collections import defaultdict
from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal as D

from constance import config as constance_config
from django.db.models import Q
from django.utils import timezone
from pydantic import parse_obj_as

import rodoviaria.api.vexado.endpoints as endpoints
import rodoviaria.api.vexado.models as forms
from commons.dateutils import now, to_default_tz, to_tz
from commons.django_utils import error_str
from commons.redis import lock
from commons.taggit_utils import Tags
from rodoviaria.api.forms import BuscarServicoForm, RetornoConsultarBilheteForm, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.api.vexado import descobrir_operacao, models, services
from rodoviaria.api.vexado.exceptions import VexadoAPIError, VexadoEfetuarReservaError
from rodoviaria.api.vexado.memcache import VexadoMC
from rodoviaria.api.vexado.models import SimplifiedViagensForm
from rodoviaria.api.vexado.utils import fill_duracao, format_preco
from rodoviaria.data.transbrasil import MAP_CLASSES_BUSER
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Assento, Onibus
from rodoviaria.forms.motorista_forms import Motorista as MotoristaForm
from rodoviaria.forms.staff_forms import VexadoAnonymousLogin
from rodoviaria.models import Cidade, LocalEmbarque, Passagem, VexadoLogin
from rodoviaria.models.core import Company, Integracao
from rodoviaria.models.vexado import RotaVexado, Veiculo, VexadoGrupoClasse
from rodoviaria.service.class_match_svc import get_buser_class_by_company
from rodoviaria.service.exceptions import (
    HibridoEmissaoForaDaData,
    PassengerNotRegistered,
    PoltronaJaSelecionadaException,
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaLocalEmbarqueException,
    RodoviariaOverbookingException,
    RodoviariaTrechoBloqueadoException,
)

buserlogger = logging.getLogger("rodoviaria")


class VexadoAPI(RodoviariaAPI):
    Rota = RotaVexado
    CPF_GENERICO = "94167878097"
    divergencia_maxima_pct = 50

    def __init__(self, company):
        super().__init__(company)
        self.login = VexadoLogin.objects.select_related("company").get(company=company)
        self.poltronas_com_erro = []
        self.cache = VexadoMC(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    def atualiza_origens(self):
        response = endpoints.CidadesEmpresa(self.login).send(self.company.company_external_id)
        return response.parsed

    def _translate_params_buscar_servico(self, params):
        return {
            "origem": params.get("LocalidadeOrigem") or params.get("origem"),
            "destino": params.get("LocalidadeDestino") or params.get("destino"),
            "data_ida": params.get("DataPartida") or params.get("data"),
        }

    def verifica_classe(self, servico, buser_class):
        api_class = servico["tipoVeiculo"].lower()
        match_class = self._does_class_match(buser_class, api_class)
        if not match_class:
            api_class = servico["descricaoTipoVeiculo"].lower()
            match_class = self._does_class_match(buser_class, api_class)
        return match_class

    def listar_empresas(self):
        response = services.listar_empresas(self.login)
        empresas_obj = parse_obj_as(list[models.VexadoEmpresa], response)
        empresas = []
        for empresa in empresas_obj:
            empresas.append(empresa.dict())
        return empresas

    def buscar_corridas(self, request_params, match_params=None):
        vexado_form = forms.BuscarServicoVexadoForm.parse_obj(request_params)
        corridas = self._buscar_corridas(vexado_form)

        corridas_form = self._make_corridas_form(corridas)
        if not match_params:
            return corridas_form

        d_args = self._find_match_corridas(corridas, vexado_form, **match_params)
        return self._parse_retorno_buscar_servico(**d_args)

    def _make_corridas_form(self, corridas):
        found = bool(corridas)
        servicos = [
            ServicoForm(
                linha=corrida["descRota"],
                external_datetime_ida=corrida["dataHoraPartida"],
                external_id=corrida["idItinerario"],
                preco=D(str(corrida["preco"])) + D(str(corrida["taxaEmbarque"] or 0)),
                vagas=corrida["assentosDisponiveis"],
                provider_data=corrida,
                external_datetime_chegada=corrida["dataHoraChegada"],
                classe=corrida["tipoVeiculo"],
                capacidade_classe=corrida["assentosDisponiveis"],
                distancia=None,  # nao tem essa informacao
                rota_external_id=corrida["idRota"],
            )
            for corrida in corridas
        ]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _find_match_corridas(self, corridas, vexado_form, datetime_ida, timezone, tipo_assento):
        tz_datetime_ida = to_tz(datetime_ida, timezone)
        if tz_datetime_ida.hour == 23 and tz_datetime_ida.minute > 30:
            corridas += self._busca_servicos_dia_seguinte(vexado_form)

        matches = []
        mismatches = []
        d_args = {}
        buser_class = tipo_assento
        servicos_ids = {s["idItinerario"] for s in corridas}
        servicos_cancelados = (
            VexadoGrupoClasse.objects.filter(grupo_classe_external_id__in=servicos_ids)
            .filter(Q(status__contains=VexadoGrupoClasse.Status.CANCELADO) | Q(status=VexadoGrupoClasse.Status.FECHADO))
            .values_list("grupo_classe_external_id", flat=True)
        )
        for servico in corridas:
            class_match = self.verifica_classe(servico, buser_class)
            datetime_ida_servico = self._get_datetime_servico(timezone, servico["dataHoraPartida"], "%Y-%m-%dT%H:%M:%S")
            if (
                servico["idEmpresa"] == self.company.company_external_id
                and self._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
                and servico["idItinerario"] not in servicos_cancelados
                and class_match
            ):
                matches.append(servico)
            else:
                mismatches.append(servico)

        if len(matches) == 1:
            matched_service = matches[0]
            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        if len(matches) > 1:
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(
                        datetime_ida, timezone, datetime.strptime(k["dataHoraPartida"], "%Y-%m-%dT%H:%M:%S")
                    ),
                    -k["assentosDisponiveis"],
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                if self.verifica_classe(servico, buser_class):
                    matched_service = servico
                    break

            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        mismatches_classe_horario = [
            (
                m["tipoVeiculo"],
                m["descricaoTipoVeiculo"],
                m["dataHoraPartida"],
            )
            for m in mismatches
        ]
        msg = (
            f"Unmatch de servico {self.company.name} com {vexado_form.dict()}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> {mismatches_classe_horario}"
        )
        buserlogger.info(msg)
        d_args.update({"timezone": timezone, "mismatches": mismatches})

        return d_args

    def _busca_servicos_dia_seguinte(self, vexado_form: forms.BuscarServicoVexadoForm):
        data_ida = (vexado_form.data + timedelta(days=1)).strftime("%Y-%m-%d")
        response = endpoints.BuscarServico(self.login).send(
            origem=vexado_form.origem, destino=vexado_form.destino, data_ida=data_ida
        )
        return response.cleaned

    def _parse_retorno_buscar_servico(
        self, servico_encontrado=None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servico = ServicoForm(
                provider_data=servico_encontrado,
                external_id=servico_encontrado["idItinerario"],
                preco=D(str(servico_encontrado["preco"])) + D(str(servico_encontrado["taxaEmbarque"] or 0)),
                vagas=servico_encontrado["assentosDisponiveis"],
                external_datetime_ida=self._get_datetime_servico(
                    timezone, servico_encontrado["dataHoraPartida"], "%Y-%m-%dT%H:%M:%S"
                ),
                veiculo_andar=servico_encontrado["andar"],
                veiculo_id=servico_encontrado["veiculoId"],
                rota_external_id=servico_encontrado["idRota"],
                classe=servico_encontrado["descricaoTipoVeiculo"],
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico, timezone):
        servico_form = ServicoForm.parse_obj(
            {
                "external_id": servico["idItinerario"],
                "preco": D(str(servico["preco"])) + D(str(servico["taxaEmbarque"] or 0)),
                "external_datetime_ida": self._get_datetime_servico(
                    timezone, servico["dataHoraPartida"], "%Y-%m-%dT%H:%M:%S"
                ),
                "classe": servico["tipoVeiculo"],
                "external_company_id": self.company.company_external_id,
                "vagas": servico["assentosDisponiveis"],
                "provider_data": servico,
            }
        )
        return servico_form

    def _verifica_poltrona_params(self, verifica_poltronas_params: VerificarPoltronaForm):
        trechoclasse = self.get_active_trecho_classe(verifica_poltronas_params.trechoclasse_id)
        id_origem = int(trechoclasse.origem.id_external)
        id_destino = int(trechoclasse.destino.id_external)
        poltronas_livres_params = {
            "origem": id_origem,
            "destino": id_destino,
            "servico": trechoclasse.external_id,
            "NumeroPoltronas": verifica_poltronas_params.passageiros,
        }
        return poltronas_livres_params, trechoclasse

    def _retorna_poltronas(self, origem, destino, servico):
        response = endpoints.RetornaPoltronas(self.login).send(itinerario=servico, origem=origem, destino=destino)
        return response.raw

    def get_map_poltronas(self, trechoclasse_id, assentos=None):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        if not assentos:
            params, _ = self._verifica_poltrona_params(
                VerificarPoltronaForm(trechoclasse_id=trechoclasse_id, passageiros=1)
            )
            assentos = self._retorna_poltronas(params["origem"], params["destino"], params["servico"])
        resp = {}
        provider_data = json.loads(trecho_classe.provider_data) if trecho_classe.provider_data else {}
        tipo_assento = provider_data.get("tipoVeiculo")
        for assento in assentos:
            for poltrona in assento["assentos"]:
                polt_detail = poltrona.get("poltrona")
                if not polt_detail:
                    continue

                tipo_categoria = polt_detail.get("tipoCategoria")
                if tipo_assento and tipo_categoria and tipo_categoria != tipo_assento:
                    continue
                numero_poltrona = polt_detail["numero"]
                ocupada = polt_detail["reservada"] or polt_detail["bloqueada"]
                resp[numero_poltrona] = "livre" if numero_poltrona and not ocupada else "ocupada"
        return collections.OrderedDict(sorted(resp.items()))

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        assentos = self._retorna_poltronas(
            int(trecho_classe.origem.id_external), int(trecho_classe.destino.id_external), trecho_classe.external_id
        )
        provider_data = json.loads(trecho_classe.provider_data) if trecho_classe.provider_data else {}
        tipo_assento_parceiro = provider_data.get("tipoVeiculo")
        tipo_assento_buser = get_buser_class_by_company(self.company, tipo_assento_parceiro)
        seats = []
        for poltronas in assentos:
            column = poltronas.get("numero")
            column = column + 1 if column > 2 else column
            for poltrona in poltronas.get("assentos", []):
                polt_detail = poltrona.get("poltrona", {})
                if not polt_detail:
                    continue
                tipo_assento = polt_detail.get("tipoCategoria")
                if tipo_assento and tipo_assento != tipo_assento_parceiro:
                    continue

                tipo_assento = poltrona.get("tipoAssento")
                if tipo_assento != "NORMAL":
                    continue
                ocupada = polt_detail.get("reservada") or polt_detail.get("bloqueada")
                seats.append(
                    Assento.parse_obj(
                        {
                            "livre": not ocupada,
                            "x": column,
                            "y": poltrona.get("ordem", 0) + 1,
                            "numero": poltrona.get("numero"),
                            "tipo_assento": tipo_assento_buser,
                            "categoria_especial": [Passagem.CategoriaEspecial.NORMAL],
                        }
                    )
                )

        return Onibus.parse_obj({"layout": [{"andar": 1, "assentos": seats}]})

    def get_qtd_poltronas_livres(self, mapa_poltronas):
        return sum(status == "livre" for poltrona, status in mapa_poltronas.items())

    def _get_poltronas_assento_id_map(self, poltronas_api):
        poltronas_map = {}
        for poltrona in poltronas_api:
            for assento in poltrona["assentos"]:
                if assento.get("poltrona"):
                    poltronas_map[assento["poltrona"]["numero"]] = assento["poltrona"]["id"]
        return poltronas_map

    def get_id_poltrona_map(self, params_mapa_poltronas):
        poltronas_api = self._retorna_poltronas(
            params_mapa_poltronas["origem"],
            params_mapa_poltronas["destino"],
            params_mapa_poltronas["itinerario"],
        )

        return self._get_poltronas_assento_id_map(poltronas_api)

    def get_poltronas_livres(self, params, trecho_classe):
        numero_poltronas = params.pop("NumeroPoltronas", 1)
        poltronas = self._retorna_poltronas(params["origem"], params["destino"], params["servico"])
        poltronas = self.get_map_poltronas(trecho_classe.trechoclasse_internal_id, poltronas)
        vagas_disponiveis = self.get_qtd_poltronas_livres(poltronas)
        if vagas_disponiveis < numero_poltronas:
            if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
                trecho_classe.tags.add(Tags.OVERBOOKING)
            raise RodoviariaOverbookingException(vagas_disponiveis)
        return self.selecionar_poltronas(numero_poltronas, poltronas)

    def selecionar_poltronas(self, numero_poltronas, poltronas, poltronas_to_exclude=None):
        if poltronas_to_exclude is None:
            poltronas_to_exclude = []
        set_poltronas = set()
        first_seat = False
        poltronas_separadas = []
        for poltrona, status in poltronas.items():
            n_poltrona = poltrona
            disponivel = status == "livre" and n_poltrona not in poltronas_to_exclude
            poltronas_restantes = numero_poltronas - len(set_poltronas)
            if not first_seat and poltronas_restantes > 1 and disponivel:
                first_seat = n_poltrona
                continue
            if poltronas_restantes == 1 and disponivel:
                set_poltronas.add(n_poltrona)
                break
            if first_seat and disponivel:
                set_poltronas.add(first_seat)
                set_poltronas.add(n_poltrona)
                first_seat = False
                if len(set_poltronas) >= numero_poltronas:
                    break
            if first_seat and not disponivel:
                poltronas_separadas.append(first_seat)
                first_seat = False
                continue
        if first_seat and len(set_poltronas) < numero_poltronas:
            set_poltronas.add(first_seat)
        while len(set_poltronas) < numero_poltronas and poltronas_separadas:
            set_poltronas.add(poltronas_separadas.pop(0))
        return list(set_poltronas)

    def _cancela_dict_to_cancela_params(self, params):
        passagens_cadastradas = self.get_passagens_confirmadas(params.travel_id, buseiro_id=params.buseiro_id)
        if not passagens_cadastradas:
            return []
        reservas_localizadores = []
        requests = []
        for passagem in passagens_cadastradas:
            requests.append(
                (
                    {
                        "empresaId": passagem.trechoclasse_integracao.grupo.company_integracao.company_external_id,
                        "motivo": "Solicitado pelo cliente",
                        "reservas": [passagem.localizador],
                    },
                    passagem,
                )
            )
            reservas_localizadores.append(passagem.localizador)
        buserlogger.info(
            "Marketplace - Vexado: passagens %s no host %s serão canceladas", reservas_localizadores, self.base_url
        )
        return requests

    def _cancelar_venda(self, params):
        if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
            return
        json = forms.CancelarReservasForm.parse_obj(params).dict(by_alias=True)
        endpoints.CancelarReservas(self.login).send(json=json)

    def cancela_venda(self, params):
        requests = self._cancela_dict_to_cancela_params(params)
        passagens_api_map_pedido = {}
        for request_params, passagem in requests:
            try:
                self._cancelar_venda(request_params)
            except VexadoAPIError as ex:
                if "reservaNaoPodeSerCancelada" in ex.codigos:
                    passagens_api_map_pedido[passagem.pedido_external_id] = (
                        passagens_api_map_pedido.get(passagem.pedido_external_id)
                        or self._recuperar_pedido(passagem.pedido_external_id)["reservas"]
                    )
                    passagens_api = passagens_api_map_pedido[passagem.pedido_external_id]
                    passagem_api = next(
                        (p for p in passagens_api if str(p["id"]) == str(passagem.localizador)),
                        None,
                    )
                    if passagem_api and passagem_api["situacaoReserva"] == "CANCELADO":
                        buserlogger.info(
                            "Vexado - Tentativa de cancelamento de passagem já cancelada na API: id = %s", passagem.id
                        )
                        passagem.save_canceled()
                        continue
                if "cancelarReservas" in ex.codigos and "O pedido não foi encontrado" in ex.message:
                    if str(self.company.company_external_id) == str(
                        534
                    ):  # Expresso Diamante TODO: Tirar isso! Já em conversa com o TI da Vexado
                        request_params["empresaId"] = 376
                        self._cancelar_venda(request_params)
                        passagem.tags.add("passagem_nao_encontra_na_api")
                        passagem.save_canceled()
                        continue
                self._save_error_cancelamento_passagens([passagem for (_, passagem) in requests], error_str(ex))
                raise ex
            except Exception as ex:
                if not isinstance(ex, RodoviariaConnectionError):
                    self._save_error_cancelamento_passagens([passagem for (_, passagem) in requests], error_str(ex))
                raise ex
            passagem.save_canceled()
        return {"Sucesso": True}

    def _sanitize_rg_number(self, rg_number):
        if rg_number:
            return rg_number[:15]
        return None

    def _reserva_dict_to_comprar_params(self, trecho_classe, params, usar_preco_api=False, extra_poltronas=None):
        travel_id = params.travel_id
        id_origem = int(trecho_classe.origem.id_external)
        id_destino = int(trecho_classe.destino.id_external)
        poltronas_livres = params.poltronas
        reservas = []
        passagens_cadastradas = {}
        provider_data = json.loads(trecho_classe.provider_data) if trecho_classe.provider_data else {}
        trecho_origem_id = provider_data.get("trechoOrigemId")
        trecho_destino_id = provider_data.get("trechoDestinoId")

        if usar_preco_api:
            valor_passagem = trecho_classe.preco_rodoviaria
        else:
            valor_passagem = params.valor_cheio
        if extra_poltronas:
            poltrona_id_map = {pl: extra_poltronas["poltronaId"] for pl in poltronas_livres}
        else:
            poltrona_id_map = self.get_id_poltrona_map(
                {
                    "origem": id_origem,
                    "destino": id_destino,
                    "itinerario": trecho_classe.external_id,
                },
            )

        for passageiro in params.passageiros:
            numero_poltrona = poltronas_livres.pop(0)
            id_poltrona = poltrona_id_map[numero_poltrona]
            reserva = {
                "passageiroDto": {
                    "poltronaId": id_poltrona,
                    "numeroPoltrona": numero_poltrona,
                    "nome": passageiro.name,
                    "documentoComFoto": self._sanitize_rg_number(passageiro.rg_number)
                    or self._cpf_passageiro(passageiro),
                    "cpfPassageiro": self._cpf_passageiro(passageiro),
                    "valor": valor_passagem,
                    "telefone": passageiro.phone,
                },
                "poltronaId": id_poltrona,
                "itinerarioId": trecho_classe.external_id,
                "idCidadeOrigem": id_origem,
                "idCidadeDestino": id_destino,
                "valor": valor_passagem,
                "trechoOrigemId": trecho_origem_id,
                "trechoDestinoId": trecho_destino_id,
            }
            reservas.append(reserva)
            passagem = Passagem(
                trechoclasse_integracao=trecho_classe,
                company_integracao=self.company,
                poltrona_external_id=numero_poltrona,
                buseiro_internal_id=passageiro.id,
                travel_internal_id=travel_id,
                valor_cheio=params.valor_cheio,
                status=Passagem.Status.INCOMPLETA,
                preco_rodoviaria=valor_passagem if not usar_preco_api else 0,
            )

            passagens_cadastradas[id_poltrona] = passagem

        self.create_passagens(passagens_cadastradas.values())
        if usar_preco_api:
            [passagem.tags.add("pago_preco_api") for passagem in passagens_cadastradas.values()]

        request_params = {
            "cidadeOrigem": id_origem,
            "cidadeDestino": id_destino,
            "itinerario": trecho_classe.external_id,
            "empresaId": self.company.company_external_id,
            "reservas": reservas,
            "formasPagamentoDto": [
                {
                    "valor": valor_passagem * len(params.passageiros),
                }
            ],
            "dadosCompradorDto": {
                "cpfComprador": self._cpf_comprador(params.passageiros),
                "nomeComprador": params.passageiros[0].name,
                "telefoneComprador": params.passageiros[0].phone,
            },
        }
        return request_params, passagens_cadastradas

    def _cpf_passageiro(self, passageiro):
        if not passageiro.cpf or passageiro.cpf == "None":
            return self.CPF_GENERICO
        return passageiro.cpf

    def _cpf_comprador(self, passageiros):
        for passageiro in passageiros:
            if passageiro.cpf:
                return passageiro.cpf
        return self.CPF_GENERICO

    def _efetuar_reserva(self, params):
        json = forms.EfetuarReservasForm.parse_obj(params).dict(by_alias=True)
        try:
            response = endpoints.EfetuarReserva(self.login).send(json=json)
            return response.raw
        except (VexadoAPIError, VexadoEfetuarReservaError):
            pedido_id = self._verifica_passageiros_na_api(params)
            if pedido_id:
                return pedido_id
            raise

    def _recuperar_pedido(self, id_pedido):
        company_external_id = self.company.company_external_id
        response = endpoints.RecuperarPedido(self.login).send(id_pedido=id_pedido, empresa_id=company_external_id)
        return response.raw

    def _efetua_compra_com_outra_poltrona(
        self, trechoclasse_id, request_params, passagens_cadastradas, poltrona_com_erro
    ):
        # TODO: isso aqui precisa estar na política de retry e não ser feito na mão
        is_third_try = len(self.poltronas_com_erro) >= 3
        if is_third_try:
            raise RodoviariaException("Erro ao selecionar poltronas")
        self.poltronas_com_erro.append(poltrona_com_erro)
        id_origem = request_params["cidadeOrigem"]
        id_destino = request_params["cidadeDestino"]
        id_itinerario = request_params["itinerario"]
        poltronas_api = self._retorna_poltronas(id_origem, id_destino, id_itinerario)

        poltronas_simplified = self.get_map_poltronas(trechoclasse_id, poltronas_api)
        novas_poltronas = self.selecionar_poltronas(
            len(passagens_cadastradas),
            poltronas_simplified,
            self.poltronas_com_erro,
        )
        if len(novas_poltronas) < len(passagens_cadastradas):
            raise RodoviariaException("Erro ao selecionar poltronas")
        novas_poltronas_cadastradas = {}
        poltrona_id_map = self._get_poltronas_assento_id_map(poltronas_api)
        for index, reserva in enumerate(request_params["reservas"]):
            nova_poltrona = novas_poltronas[index]
            id_nova_poltrona = poltrona_id_map[nova_poltrona]
            passagens_cadastradas[reserva["poltronaId"]].poltrona_external_id = id_nova_poltrona
            novas_poltronas_cadastradas[id_nova_poltrona] = passagens_cadastradas[reserva["poltronaId"]]
            reserva["poltronaId"] = id_nova_poltrona
            reserva["passageiroDto"]["poltronaId"] = id_nova_poltrona
            reserva["passageiroDto"]["numeroPoltrona"] = nova_poltrona
        Passagem.objects.bulk_update(passagens_cadastradas.values(), ["poltrona_external_id", "updated_at"])
        return self._efetuar_compra(trechoclasse_id, request_params, novas_poltronas_cadastradas)

    def _save_errors(self, error_msg, passagens):
        for key in passagens:
            passagens[key].save_error(error_msg)

    def _get_numero_poltrona_por_passageiro_nome_mensagem_erro(self, mensagem_erro, reservas):
        for reserva in reservas:
            if reserva["passageiroDto"]["nome"] in mensagem_erro:
                return reserva["passageiroDto"]["numeroPoltrona"]

    def _verifica_passageiros_na_api(self, request_params):
        itinerario_id = request_params["reservas"][0]["itinerarioId"]
        passageiros_viagem = self.lista_reservas_viagem(itinerario_id, detailed=True)

        reservas_na_api_map = {}
        for p in passageiros_viagem:
            reservas_na_api_map[(p["passageiroDto"]["cpfPassageiro"], str(p["numeroPoltrona"]))] = p["pedidoId"]

        reservas_esperadas = request_params["reservas"]
        passageiros_encontrados_map = defaultdict(list)
        passageiros_nao_encontrados = []
        for r in reservas_esperadas:
            p_id = reservas_na_api_map.get(
                (
                    r["passageiroDto"]["cpfPassageiro"],
                    str(r["passageiroDto"]["numeroPoltrona"]),
                )
            )
            passageiro = {
                "nome": r["passageiroDto"]["nome"],
                "poltrona": r["passageiroDto"]["numeroPoltrona"],
            }
            if p_id:
                passageiros_encontrados_map[p_id].append(passageiro)
            if not p_id:
                passageiros_nao_encontrados.append(passageiro)

        if not passageiros_encontrados_map:
            # passageiros não estão na API
            return

        if len(passageiros_encontrados_map) > 1:
            buserlogger.error(
                "Vexado API - passageiros de mesma compra encontrados em pedidos diferentes. Passageiros encontrados:"
                " %s. Passageiros nao encontrados %s",
                passageiros_encontrados_map,
                passageiros_nao_encontrados,
            )
            return

        return list(passageiros_encontrados_map.keys())[0]

    def _bloquear_poltrona(self, poltrona_id, origem_id, destino_id, itinerario_id):
        json = {
            "poltronaId": poltrona_id,
            "trechoOrigemId": origem_id,
            "trechoDestinoId": destino_id,
            "itinerarioId": itinerario_id,
        }
        response = endpoints.BloquearPoltrona(self.login).send(json=json)
        return response.raw

    def _desbloquear_poltronas(self, trechoclasse_id, bloqueios):
        endpoints.DesbloquearPoltrona(self.login).send(json=[bloqueio["id_poltrona"] for bloqueio in bloqueios])
        for bloq in bloqueios:
            self.cache.delete_poltrona_key_cache(trechoclasse_id, bloq["numero_poltrona"])

    def _get_bloqueio_poltrona(self, trechoclasse_id, reserva_params):
        poltrona = self.cache.get_poltrona_key_cache(trechoclasse_id, reserva_params["passageiroDto"]["numeroPoltrona"])
        if poltrona:
            return poltrona
        buserlogger.info(
            "entrou na compra vexado sem poltrona bloqueada. trechoclasse_id=%s poltrona=%s",
            trechoclasse_id,
            reserva_params["passageiroDto"]["numeroPoltrona"],
        )
        bloqueio = self._bloquear_poltrona(
            reserva_params["poltronaId"],
            reserva_params["trechoOrigemId"],
            reserva_params["trechoDestinoId"],
            reserva_params["itinerarioId"],
        )
        return bloqueio

    def _efetuar_compra(self, trechoclasse_id, request_params, passagens_cadastradas, extra_poltronas=None):
        if self.company.modelo_venda == Company.ModeloVenda.MARKETPLACE:
            request_params = self._add_dados_bloqueio_no_request_params(
                trechoclasse_id, request_params, extra_poltronas
            )

        try:
            id_pedido = self._efetuar_reserva(request_params)
            for key in passagens_cadastradas:
                passagens_cadastradas[key].pedido_external_id = id_pedido
                passagens_cadastradas[key].save()
        except VexadoAPIError as exc:
            if exc.codigos and ("poltronaReservada" in exc.codigos):
                poltrona_com_erro = (
                    self._get_numero_poltrona_por_passageiro_nome_mensagem_erro(
                        exc.violacoes[0].mensagem, request_params["reservas"]
                    )
                    if exc.violacoes[0].mensagem
                    else None
                )
                if not extra_poltronas:
                    return self._efetua_compra_com_outra_poltrona(
                        trechoclasse_id, request_params, passagens_cadastradas, poltrona_com_erro
                    )
            self._save_errors(error_str(exc), passagens_cadastradas)
            raise
        except Exception as ex:
            self._save_errors(error_str(ex), passagens_cadastradas)
            raise ex

        response = self._save_dados_passagens(passagens_cadastradas, id_pedido)
        return {"passagens": response}

    def _save_dados_passagens(self, passagens_cadastradas, id_pedido):
        try:
            dados_passagens = self._recuperar_pedido(id_pedido)
        except Exception as ex:
            self._save_errors(error_str(ex), passagens_cadastradas)
            for p in passagens_cadastradas.values():
                p.tags.add("passagem_com_erro_confirmada_na_api")
            raise ex
        response = []
        reservas = dados_passagens.get("reservas")
        if not reservas:
            raise Exception("Algo de errado nao está certo")
        seen_poltronas = set()
        dict_provider_data = self._retorna_provider_data(dados_passagens)
        for dpassagem in reservas:
            numero_poltrona = dpassagem.get("numeroPoltrona")
            id_poltrona = dpassagem.get("poltronaId")
            id_external = dpassagem.get("id")
            passagem = passagens_cadastradas[id_poltrona]

            # Gera nova Passagem para conexão com mesma poltrona
            # Para conexẽo com poltronas diferentes já dá certo
            if numero_poltrona in seen_poltronas:
                # Conexão Passagem 1/2
                passagem.valor_cheio = passagem.valor_cheio / 2
                passagem.save()

                # Conexão Passagem 2/2
                passagem.pk = None
                passagem.save()
            else:
                seen_poltronas.add(numero_poltrona)

            passagem.poltrona_external_id = numero_poltrona
            passagem.localizador = id_external
            passagem.provider_data = dict_provider_data[id_external]
            passagem.save_confirmed()
            self._atribui_bpe(passagem, dpassagem)
            response.append(passagem.to_dict_json())
            self.poltronas_com_erro = []
        return response

    def _add_dados_bloqueio_no_request_params(self, trechoclasse_id, request_params, extra_poltronas):
        if extra_poltronas:
            # caso tenha extra_poltronas, é apenas uma compra por vez
            request_params["reservas"][0]["garantiaPrecoUuid"] = extra_poltronas["uuid"]
        else:
            bloqueios = []
            for r in request_params["reservas"]:
                try:
                    bloqueio = self._get_bloqueio_poltrona(trechoclasse_id, r)
                    r["garantiaPrecoUuid"] = bloqueio["uuid"]

                    bloqueios.append(
                        {"id_poltrona": bloqueio["id"], "numero_poltrona": r["passageiroDto"]["numeroPoltrona"]}
                    )
                except (VexadoAPIError, RodoviariaTrechoBloqueadoException, PoltronaJaSelecionadaException) as ex:
                    self._desbloquear_poltronas(trechoclasse_id, bloqueios)
                    raise ex
        return request_params

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=False):
        modelo_venda_hibrido = self.company.modelo_venda == Company.ModeloVenda.HIBRIDO

        if not from_add_pax and modelo_venda_hibrido:
            return

        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        permite_excecao = params.travel_id in _get_travels_permite_emissao_com_menos_de_tres_horas()
        fora_do_horario = trecho_classe.datetime_ida > timezone.now() + timedelta(hours=3)
        if modelo_venda_hibrido and fora_do_horario and not permite_excecao:
            raise HibridoEmissaoForaDaData

        usar_preco_api = self.company.has_feature(Company.Feature.USAR_PRECO_API)
        request_params, passagens_cadastradas = self._reserva_dict_to_comprar_params(
            trecho_classe, params, usar_preco_api, params.extra_poltronas
        )
        return self._efetuar_compra(
            params.trechoclasse_id, request_params, passagens_cadastradas, params.extra_poltronas
        )

    def cadastrar_grupo(self, grupo):
        company_external_id = self.company.company_external_id
        json = {
            "descontoMaximo": "0.00",
            "dataPartida": grupo.data_partida_str,
            "habilitadoSite": False,
            "horaSaida": grupo.hora_saida,
            "idEmpresa": company_external_id,
            "idRota": grupo.id_rota,
            "tipoPrecoPrimeiroAndar": grupo.classe_primeiro_andar,
            "tipoPrecoSegundoAndar": grupo.classe_segundo_andar,
            "idVeiculo": grupo.id_veiculo,
            "tipoCategoria": "UNICA",
        }
        viagens_ids = endpoints.CadastrarGrupo(self.login).send(json=json).raw
        response = {MAP_CLASSES_BUSER[grupo.classe_primeiro_andar]: viagens_ids[0]}
        if grupo.classe_segundo_andar:
            response[MAP_CLASSES_BUSER[grupo.classe_segundo_andar]] = viagens_ids[1]
        return response

    def cadastrar_trechos(self, precos):
        result = []
        for preco in precos:
            try:
                result.append(
                    self.cadastrar_preco(
                        preco.cidade_origem_id,
                        preco.cidade_destino_id,
                        preco.classe,
                        preco.max_split_value,
                    )
                )
            except VexadoAPIError as exc:
                if "precoJaExistte" in exc.codigos:
                    result.append(
                        self._atualizar_preco(
                            preco.cidade_origem_id,
                            preco.cidade_destino_id,
                            preco.classe,
                            preco.max_split_value,
                        )
                    )
                else:
                    raise

        return result

    def cadastrar_preco(self, origem_id, destino_id, classe, valor):
        company_external_id = self.company.company_external_id
        json = {
            "empresaId": company_external_id,
            "cidadeOrigemId": origem_id,
            "cidadeDestinoId": destino_id,
            "tipoPreco": classe,
            "valor": format_preco(valor),
            "valorAntt": format_preco(valor * 2),
        }
        endpoints.CadastrarPreco(self.login).send(json=json)

    def _atualizar_preco(self, origem_id, destino_id, classe, valor):
        resp_buscar_preco = self._buscar_preco(origem_id, destino_id, classe)
        if resp_buscar_preco["totalRegistros"] == 0:
            msg = (
                f"Tentativa de alterar preço não cadastrado ({self.company.company_external_id}, {origem_id},"
                f" {destino_id}, {classe})"
            )
            raise RodoviariaException(msg)

        preco = resp_buscar_preco["precos"][0]
        return self._alterar_preco(preco["id"], origem_id, destino_id, classe, valor)

    def _buscar_preco(self, origem_id, destino_id, classe):
        company_external_id = self.company.company_external_id
        params = {
            "origem": origem_id,
            "destino": destino_id,
            "tipoPreco": classe,
        }
        response = endpoints.BuscarPrecos(self.login).send(company_external_id=company_external_id, params=params)
        return response.raw

    def _alterar_preco(self, preco_id, origem_id, destino_id, classe, valor):
        company_external_id = self.company.company_external_id
        json = {
            "id": preco_id,
            "empresaId": company_external_id,
            "cidadeOrigemId": origem_id,
            "cidadeDestinoId": destino_id,
            "tipoPreco": classe,
            "valor": format_preco(valor),
            "valorAntt": format_preco(valor * 2),
        }
        endpoints.AlterarPreco(self.login).send(json=json)

    def cadastrar_rota(self, origem_id, destino_id, prefixo, rota_internal_id):
        company_external_id = self.company.company_external_id
        json = {
            "cidadeOrigemId": origem_id,
            "cidadeDestinoId": destino_id,
            "empresaId": company_external_id,
            "exibeTrechoAlternativo": True,
            "prefixo": prefixo,
            "prestador": self.company.name,
            "delimitacao": rota_internal_id,
        }
        endpoints.CadastrarRota(self.login).send(json=json)

    def editar_rota(self, rota_external_id, delimitacao, origem_id, destino_id, prefixo):
        company_external_id = self.company.company_external_id
        json = {
            "id": rota_external_id,
            "cidadeOrigemId": origem_id,
            "cidadeDestinoId": destino_id,
            "empresaId": company_external_id,
            "prefixo": prefixo,
            "delimitacao": str(delimitacao),
        }
        endpoints.CadastrarRota(self.login).send(json=json)

    def inativar_rota(self, rota_external_id, origem_id, destino_id, prefixo):
        self.editar_rota(rota_external_id, "inativa", origem_id, destino_id, prefixo)
        # self.inativar_itinerarios_bulk(rota_external_id)
        return {"Sucesso": True}

    def inativar_itinerarios_bulk(self, rota_external_id):
        today = timezone.now()
        json = {
            "dataInicio": today.strftime("%Y/%m/%d"),
            "dataFim": (today + timedelta(hours=24 * 365)).strftime("%Y/%m/%d"),
            "diasSemana": ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"],
            "empresaId": self.company.company_external_id,
            "rotaId": rota_external_id,
            "ativo": False,
        }
        response = endpoints.InativarItinerariosBulk(self.login).send(json=json)
        return response.raw

    def verifica_poltrona(self, params: VerificarPoltronaForm):
        params_get_poltronas_livres, trecho_classe = self._verifica_poltrona_params(params)
        poltronas = self.get_poltronas_livres(params_get_poltronas_livres, trecho_classe)
        if self.company.modelo_venda == Company.ModeloVenda.MARKETPLACE:
            self.bloquear_poltronas(params.trechoclasse_id, poltronas)
        return poltronas

    def bloquear_poltronas(self, trechoclasse_id, poltronas):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        id_origem = int(trecho_classe.origem.id_external)
        id_destino = int(trecho_classe.destino.id_external)

        provider_data = json.loads(trecho_classe.provider_data) if trecho_classe.provider_data else {}
        trecho_origem_id = provider_data.get("trechoOrigemId")
        trecho_destino_id = provider_data.get("trechoDestinoId")
        poltrona_id_map = self.get_id_poltrona_map(
            {
                "origem": id_origem,
                "destino": id_destino,
                "itinerario": trecho_classe.external_id,
            },
        )
        response_bloqueios = []
        poltronas_desbloqueio_erro = []
        try:
            for polt in poltronas:
                response = self._bloquear_poltrona(
                    poltrona_id_map[polt], trecho_origem_id, trecho_destino_id, trecho_classe.external_id
                )
                response_bloqueios.append(response)
                self.cache.set_poltrona_key_cache(trechoclasse_id, polt, response)
                poltronas_desbloqueio_erro.append({"id_poltrona": response["id"], "numero_poltrona": polt})
        except (VexadoAPIError, RodoviariaTrechoBloqueadoException, PoltronaJaSelecionadaException) as ex:
            if any(poltronas_desbloqueio_erro):
                self._desbloquear_poltronas(trechoclasse_id, poltronas_desbloqueio_erro)
            raise ex
        return response_bloqueios

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        response_bloqueios = self.bloquear_poltronas(trechoclasse_id, [poltrona])
        return BloquearPoltronasResponse(
            seat=poltrona, best_before=now() + timedelta(minutes=10), external_payload=response_bloqueios[0]
        )

    def desbloquear_poltronas(self, trecho_classe_id: int, poltronas: list[int]) -> dict:
        for poltrona in poltronas:
            poltrona_cache = self.cache.get_poltrona_key_cache(trecho_classe_id, poltrona)
            if poltrona_cache:
                poltrona_cache["numero_poltrona"] = poltrona
                poltrona_cache["id_poltrona"] = poltrona_cache["id"]
                self._desbloquear_poltronas(trecho_classe_id, [poltrona_cache])

    def itinerario(self, external_id, datetime_ida=None):
        itinerario_external_id = external_id
        company_external_id = self.company.company_external_id

        return endpoints.BuscarItinerarioCorrida(self.login).send(itinerario_external_id, company_external_id)

    def buscar_cidade_por_nome(self, nome_cidade):
        response = endpoints.GetCidades(self.login).send(nome_cidade=nome_cidade)
        return response.raw

    def match_cidade(self, cidade_api, codigo_ibge, nome_cidade):
        return (
            cidade_api["codigoIbge"] == str(codigo_ibge)
            and nome_cidade.lower().strip() == cidade_api["nome"].lower().strip()
        )

    @lock(
        "cadastro_local_embarque_{company_internal_id}_{local_embarque_internal_id}",
        expire=30,
    )
    def match_local_de_embarque(
        self,
        local_embarque_internal_id,
        cidade_internal_id,
        timezone,
        nome_cidade,
        codigo_ibge,
        company,
        company_internal_id,
    ):
        if local_embarque_internal_id:
            local_embarque = LocalEmbarque.objects.filter(
                cidade__company=company,
                local_embarque_internal_id=local_embarque_internal_id,
            ).first()
            if local_embarque:
                return local_embarque

        cidades_api = self.buscar_cidade_por_nome(nome_cidade)
        for cidade_api in cidades_api:
            if self.match_cidade(cidade_api, codigo_ibge, nome_cidade):
                cidade = self.get_first_cidade_or_create(cidade_internal_id, timezone, company, cidade_api)
                if local_embarque_internal_id:
                    local_embarque = LocalEmbarque()
                    local_embarque.cidade = cidade
                    local_embarque.id_external = cidade_api["id"]
                    local_embarque.nickname = cidade_api["nomeComUf"]
                    local_embarque.local_embarque_internal_id = local_embarque_internal_id
                    local_embarque.save()
                    return local_embarque
                return None
        raise RodoviariaLocalEmbarqueException

    def get_first_cidade_or_create(self, cidade_internal_id, timezone, company, cidade_api):
        cidade = Cidade.objects.filter(company=company, cidade_internal_id=cidade_internal_id).order_by("id").first()
        if not cidade:
            cidade = Cidade.objects.create(
                company=company,
                cidade_internal_id=cidade_internal_id,
                id_external=cidade_api["id"],
                name=cidade_api["nome"],
                timezone=timezone,
            )
        return cidade

    def _atribui_bpe(self, passagem, dados_passagem):
        passagem.taxa_embarque = self._sanitize_preco(dados_passagem.get("taxaEmbarque"))
        passagem.seguro = self._sanitize_preco(dados_passagem.get("seguro"))
        passagem.pedagio = self._sanitize_preco(dados_passagem.get("pedagio"))
        passagem.outras_taxas = self._sanitize_preco(dados_passagem.get("taxaServico"))
        passagem.preco_rodoviaria = self._sanitize_preco(dados_passagem.get("valorTotalReserva"))
        passagem.preco_base = self._sanitize_preco(dados_passagem.get("tarifa"))
        if dados_passagem.get("valorICMS") and dados_passagem.get("aliquotaICMS"):
            passagem.outros_tributos = f"ICMS {dados_passagem.get('valorICMS')} ({dados_passagem.get('aliquotaICMS')}%)"
            if dados_passagem.get("outrosTributos"):
                passagem.outros_tributos += f" OUTROS TRIB: {dados_passagem['outrosTributos']}"
        passagem.linha = dados_passagem.get("rota")
        passagem.prefixo = dados_passagem.get("prefixoRota")
        passagem.origem = dados_passagem.get("descCidadeOrigem")
        passagem.destino = dados_passagem.get("descCidadeDestino")
        passagem.data_hora_partida = dados_passagem.get("dataHoraPartida")
        passagem.cnpj = dados_passagem.get("cnpjEmpresa")
        passagem.inscricao_estadual = dados_passagem.get("inscricaoEstadual")
        passagem.endereco_empresa = self._generate_endereco(dados_passagem.get("endereco"))
        passagem.nome_agencia = dados_passagem.get("nomeVendedor")
        passagem.bpe_monitriip_code = dados_passagem.get("codigoQrcodeEmbarqueMonitrip")
        if dados_passagem.get("passageiroDto"):
            passagem.tipo_emissao = dados_passagem["passageiroDto"].get("tipoEmissao")
        bpe_info = dados_passagem.get("transmissaoBpe")
        if bpe_info:
            passagem.bpe_qrcode = bpe_info.get("qrCod")
            passagem.bpe_em_contingencia = bpe_info.get("emitidoEmContigencia")
            passagem.data_autorizacao = bpe_info.get("dataAutorizacao") or to_default_tz(timezone.now())
            passagem.chave_bpe = bpe_info.get("chaveAcesso")
            passagem.numero_bpe = bpe_info["chaveAcesso"][-15:-10] if passagem.chave_bpe else None
            passagem.serie_bpe = bpe_info["chaveAcesso"][-22:-19] if passagem.chave_bpe else None
            passagem.protocolo_autorizacao = bpe_info.get("protocoloTransmissao")
        grupo = passagem.trechoclasse_integracao.grupo
        grupo.linha = dados_passagem.get("rota")
        grupo.save()
        passagem.save()

    def _generate_endereco(self, endereco_api):
        if not endereco_api:
            return ""
        cep = endereco_api.get("cep").strip()
        logradouro = (endereco_api.get("logradouro") or "").strip()
        localidade = (endereco_api.get("localidade") or "").strip()
        complemento = (endereco_api.get("complemento") or "").strip()
        bairro = (endereco_api.get("bairro") or "").strip()
        uf = (endereco_api.get("uf") or "").strip()
        numero = str(endereco_api.get("numero") or "").strip()
        return f"{logradouro} {numero}, {complemento}, {bairro} - {localidade}-{uf}, CEP: {cep}"

    def _buscar_corridas(self, vexado_form):
        response = endpoints.BuscarServico(self.login).send(
            origem=vexado_form.origem, destino=vexado_form.destino, data_ida=vexado_form.data
        )
        return response.cleaned

    def rota_id_from_trecho_classe(self, trecho_classe):
        if not trecho_classe:
            return None
        if not trecho_classe.grupo.linha:
            if not trecho_classe.provider_data:
                return None
            provider_data = json.loads(trecho_classe.provider_data)
            trecho_classe.grupo.linha = provider_data.get("descRota")
            trecho_classe.save()
        return trecho_classe.grupo.linha

    def multiplas_compras_params_dict(self, trecho_classe, multiple_pax_form):
        numero_passageiros_total = multiple_pax_form.quantidade_passageiros
        id_origem = int(trecho_classe.origem.id_external)
        id_destino = int(trecho_classe.destino.id_external)
        id_itinerario = trecho_classe.external_id
        poltronas_api = self._retorna_poltronas(id_origem, id_destino, id_itinerario)
        poltronas_simplified = self.get_map_poltronas(trecho_classe.trechoclasse_internal_id, poltronas_api)
        vagas_disponiveis = self.get_qtd_poltronas_livres(poltronas_simplified)
        if vagas_disponiveis < numero_passageiros_total:
            if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
                trecho_classe.tags.add(Tags.OVERBOOKING)
            raise RodoviariaOverbookingException(vagas_disponiveis)
        passagens_cadastradas_por_preco = collections.defaultdict(dict)
        cpf_comprador = None
        reservas_por_preco = collections.defaultdict(list)
        for travel in multiple_pax_form.travels:
            numero_passageiros_travel = len(travel.buseiros)
            valor_por_buseiro = travel.valor_por_buseiro
            poltronas = self.selecionar_poltronas(numero_passageiros_travel, poltronas_simplified)
            for poltrona in poltronas:
                poltronas_simplified[poltrona] = "ocupada"
            poltrona_id_map = self._get_poltronas_assento_id_map(poltronas_api)
            for buseiro in travel.buseiros:
                num_poltrona = poltronas.pop(0)
                id_poltrona = poltrona_id_map[num_poltrona]
                if buseiro.cpf and not cpf_comprador:
                    cpf_comprador = buseiro.cpf
                reserva_dict = {
                    "passageiroDto": {
                        "poltronaId": id_poltrona,
                        "numeroPoltrona": num_poltrona,
                        "nome": buseiro.name,
                        "documentoComFoto": buseiro.rg_number,
                        "cpfPassageiro": self._cpf_passageiro(buseiro),
                        "valor": valor_por_buseiro,
                        "telefone": buseiro.phone,
                        "tipoEmissao": "NORMAL",
                        "tipoDocumento": "RG",
                    },
                    "poltronaId": id_poltrona,
                    "itinerarioId": id_itinerario,
                    "idCidadeOrigem": id_origem,
                    "idCidadeDestino": id_destino,
                    "valor": valor_por_buseiro,
                }

                reservas_por_preco[valor_por_buseiro].append(reserva_dict)
                passagem = Passagem(
                    trechoclasse_integracao=trecho_classe,
                    company_integracao=self.company,
                    poltrona_external_id=num_poltrona,
                    buseiro_internal_id=buseiro.id,
                    travel_internal_id=travel.travel_id,
                    valor_cheio=valor_por_buseiro,
                    status=Passagem.Status.INCOMPLETA,
                )
                passagens_cadastradas_por_preco[valor_por_buseiro][id_poltrona] = passagem
        todas_passagens = []
        for passagens in passagens_cadastradas_por_preco.values():
            for passagem in passagens.values():
                todas_passagens.append(passagem)
        Passagem.objects.bulk_create(todas_passagens)
        requests_por_preco = {}
        for preco, reservas in reservas_por_preco.items():
            request = {
                "cidadeOrigem": id_origem,
                "cidadeDestino": id_destino,
                "empresaId": self.company.company_external_id,
                "reservas": reservas,
                "formasPagamentoDto": [
                    {
                        "valor": preco * len(reservas),
                    }
                ],
                "dadosCompradorDto": {
                    "cpfComprador": cpf_comprador or self.CPF_GENERICO,
                    "nomeComprador": "Buser",
                },
            }
            requests_por_preco[preco] = request
        return requests_por_preco, passagens_cadastradas_por_preco

    def add_multiple_pax_na_lista_passageiros_viagem(self, multiple_pax_form):
        trecho_classe = self.get_active_trecho_classe(multiple_pax_form.trechoclasse_id)
        is_modelo_hibrido = self.company.modelo_venda == Company.ModeloVenda.HIBRIDO
        if is_modelo_hibrido and trecho_classe.datetime_ida > timezone.now() + timedelta(hours=3):
            raise HibridoEmissaoForaDaData
        (requests_por_preco, passagens_por_preco) = self.multiplas_compras_params_dict(trecho_classe, multiple_pax_form)
        passagens_compradas = []
        for preco, request_params in requests_por_preco.items():
            passagens_cadastradas = passagens_por_preco[preco]
            response = self._efetuar_compra(multiple_pax_form.trechoclasse_id, request_params, passagens_cadastradas)
            passagens_compradas += response.get("passagens")
        return {"passagens": passagens_compradas}

    def _cadastra_usuario(self, motorista: MotoristaForm):
        company_external_id = self.company.company_external_id
        params = {
            "nome": motorista.nome,
            "email": motorista.email,
            "telefone": motorista.telefone,
            "cpf": motorista.cpf,
            "roles": ["ROLE_MOTORISTA_EMPRESA"],
        }
        json = forms.Usuario.parse_obj(params).dict(by_alias=True, exclude_unset=True)

        endpoints.CadastrarUsuario(self.login).send(company_external_id=company_external_id, json=json)

    def _busca_usuario_por_email(self, email):
        company_external_id = self.company.company_external_id
        params = {"emailUsuario": email}

        resp = endpoints.BuscarUsuarios(self.login).send(company_external_id=company_external_id, params=params).raw

        if resp["total"] == 0:
            return None

        return resp["usuarios"][0]

    def _cadastra_motorista(self, motorista: MotoristaForm, id_usuario_empresa):
        company_external_id = self.company.company_external_id
        params = {
            "id_usuario_empresa": id_usuario_empresa,
            "cnh_numero": motorista.cnh.numero,
            "cnh_validade": motorista.cnh.validade,
            "cnh_categoria": motorista.cnh.categoria,
            "cnh_orgao_emissor": motorista.cnh.orgao_emissor,
            "cnh_uf": motorista.cnh.uf,
            "antt_numero": motorista.registro_antt.numero,
            "antt_validade": motorista.registro_antt.validade,
        }
        json = forms.Motorista.parse_obj(params).dict(by_alias=True, exclude_unset=True)

        endpoints.CadastrarMotorista(self.login).send(company_external_id=company_external_id, json=json)

    def _busca_motorista_por_id(self, id_usuario_empresa):
        company_external_id = self.company.company_external_id
        response = endpoints.RecuperarMotorista(self.login).send(
            company_external_id=company_external_id, id_usuario_empresa=id_usuario_empresa
        )
        return response.raw

    def cria_motorista(self, motorista: MotoristaForm):
        try:
            self._cadastra_usuario(motorista)
        except VexadoAPIError as exc:
            # ignora apenas se usuário já foi cadastrado
            if "usuarioCadastrado" not in exc.codigos:
                raise

        usuario = self._busca_usuario_por_email(motorista.email)

        if motorista.cnh and motorista.registro_antt:
            self._cadastra_motorista(motorista, usuario["usuarioEmpresaId"])
            motora = self._busca_motorista_por_id(usuario["usuarioEmpresaId"])
        else:
            motora = None

        return {
            "id_usuario": usuario["id"],
            "id_usuario_empresa": usuario["usuarioEmpresaId"],
            "id_motorista": motora["id"] if motora else None,
        }

    def edita_dados_motorista(self, id_usuario, motorista: MotoristaForm):
        company_external_id = self.company.company_external_id
        params = {
            "id": id_usuario,
            "nome": motorista.nome,
            "email": motorista.email,
            "telefone": motorista.telefone,
            "cpf": motorista.cpf,
        }
        json = forms.Usuario.parse_obj(params).dict(by_alias=True, exclude_unset=True)

        endpoints.AlterarUsuario(self.login).send(company_external_id=company_external_id, json=json)

    def edita_documentos_motorista(self, id_motorista, id_usuario_empresa, motorista: MotoristaForm):
        company_external_id = self.company.company_external_id
        params = {
            "id": id_motorista,
            "id_usuario_empresa": id_usuario_empresa,
            "cnh_numero": motorista.cnh.numero,
            "cnh_validade": motorista.cnh.validade,
            "cnh_categoria": motorista.cnh.categoria,
            "cnh_orgao_emissor": motorista.cnh.orgao_emissor,
            "cnh_uf": motorista.cnh.uf,
            "antt_numero": motorista.registro_antt.numero,
            "antt_validade": motorista.registro_antt.validade,
        }
        json = forms.Motorista.parse_obj(params).dict(by_alias=True, exclude_unset=True)

        endpoints.AlterarMotorista(self.login).send(company_external_id=company_external_id, json=json)

    def escala_motorista(self, id_usuario_empresa, id_itinerario):
        company_external_id = self.company.company_external_id
        params = {"idsUsuarioEmpresaMotoristas": id_usuario_empresa}

        endpoints.IncluirMotoristasItinerario(self.login).send(
            id_itinerario=id_itinerario, company_external_id=company_external_id, params=params
        )

    def _count_poltronas_andar(self, fileiras):
        total_poltronas = 0
        for fileira in fileiras:
            assentos_normais_count = len(
                [assento for assento in fileira["assentos"] if assento["tipoAssento"] == "NORMAL"]
            )
            total_poltronas += assentos_normais_count
        return total_poltronas

    def get_mapas_veiculos_api(self):
        mapas_veiculos = endpoints.MapasVeiculos(self.login).send(json={}, params={"size": 300}).raw
        mapas_forms = []
        if not mapas_veiculos or mapas_veiculos.get("mapasVeiculos") is None:
            raise RodoviariaException("Algo deu errado na busca pelos mapas de veiculos")
        for mapa in mapas_veiculos["mapasVeiculos"]:
            mapa_id = mapa.get("id")
            fileiras_primeiro_andar = mapa.get("fileirasPrimeiroAndar")
            fileiras_segundo_andar = mapa.get("fileirasSegundoAndar")
            has_dois_andares = bool(fileiras_segundo_andar)
            quantidade_poltronas_primeiro_andar = self._count_poltronas_andar(fileiras_primeiro_andar)
            quantidade_poltronas_segundo_andar = (
                self._count_poltronas_andar(fileiras_segundo_andar) if has_dois_andares else 0
            )
            mapas_forms.append(
                forms.MapaVeiculoForm(
                    id_external=mapa_id,
                    has_dois_andares=has_dois_andares,
                    quantidade_poltronas_primeiro_andar=quantidade_poltronas_primeiro_andar,
                    quantidade_poltronas_segundo_andar=quantidade_poltronas_segundo_andar,
                    provider_data=mapa,
                )
            )
        return mapas_forms

    def get_lista_veiculos_api(self):
        company_external_id = self.company.company_external_id
        lista_veiculos = (
            endpoints.ListarVeiculos(self.login)
            .send(company_external_id=company_external_id, json={}, params={"size": 1000})
            .raw
        )
        if not lista_veiculos or lista_veiculos.get("veiculos") is None:
            raise RodoviariaException("Algo deu errado na busca pelos veiculos")
        veiculos_forms = []
        for veiculo in lista_veiculos["veiculos"]:
            veiculo_id = veiculo["id"]
            descricao = veiculo["descricao"]
            mapa_veiculo_id = veiculo["mapaVeiculoDto"]["id"]
            veiculos_forms.append(
                forms.VeiculoForm(
                    descricao=descricao,
                    external_mapa_veiculo_id=mapa_veiculo_id,
                    id_external=veiculo_id,
                )
            )
        return veiculos_forms

    def create_veiculos_api(self, veiculos):
        for veiculo in veiculos:
            json_body = {
                "descricao": veiculo.descricao,
                "empresaId": self.company.company_external_id,
                "mapaVeiculoId": veiculo.mapa_veiculo.id_external,
                "tipoVeiculo": "ONIBUS",
            }
            endpoints.CadastrarVeiculo(self.login).send(json=json_body)
        veiculos_api = {v.descricao: v for v in self.get_lista_veiculos_api()}
        for veiculo in veiculos:
            v_api = veiculos_api.get(veiculo.descricao)
            veiculo.id_external = v_api.id_external
        Veiculo.objects.bulk_update(veiculos, ["id_external"])  # nao possui atributo updated_at
        return veiculos

    def escala_veiculo(self, params_alterar_veiculo):
        json_body = forms.AlterarVeiculoForm.parse_obj(params_alterar_veiculo).dict()
        endpoints.AlterarVeiculo(self.login).send(json=json_body)

    def buscar_rotas(self):
        company_external_id = self.company.company_external_id
        response = endpoints.BuscarRotas(self.login).send(company_external_id=company_external_id).raw
        if not response or response.get("rotas") is None:
            raise RodoviariaException("Algo deu errado na busca pelas rotas")
        rotas_response = []
        for rota in response["rotas"]:
            rota_api = {
                "id": rota["id"],
                "descricao": rota["descricao"],
                "cidade_origem": rota["cidadeOrigem"]["nome"],
                "cidade_origem_id": rota["cidadeOrigem"]["id"],
                "cidade_destino": rota["cidadeDestino"]["nome"],
                "cidade_destino_id": rota["cidadeDestino"]["id"],
                "prefixo": rota["prefixo"],
                "delimitacao": rota["delimitacao"],
                "itinerario": rota["trechosDto"],
            }
            rotas_response.append(rota_api)
        return rotas_response

    def altera_veiculo_viagem(self, veiculo_id, andar, itinerario_id):
        json_body = {
            "id": itinerario_id,
            "idVeiculo": veiculo_id,
            "idEmpresa": self.company.company_external_id,
            "andar": andar,
        }
        endpoints.AlterarVeiculo(self.login).send(json=json_body)

    def viagens_por_rota(self, rota_external_id) -> list[SimplifiedViagensForm]:
        company_external_id = self.company.company_external_id
        viagens = endpoints.ListarViagensRota(self.login).send(
            company_external_id=company_external_id, id_rota=rota_external_id, json={}, params={"size": 1000}
        )
        return viagens.parsed

    def cadastrar_checkpoint(self, CadastrarCheckpointParams):
        json_body = {
            "cidadeDestinoId": CadastrarCheckpointParams.cidade_destino_id,
            "duracao": fill_duracao(CadastrarCheckpointParams.tempo_total),
            "empresaId": self.company.company_external_id,
            "ordem": 0,
            "pontoEmbarque": CadastrarCheckpointParams.ponto_embarque,
            "quilometragem": CadastrarCheckpointParams.km or 0,
            "rotaId": CadastrarCheckpointParams.id_rota_external,
            "taxaEmbarque": "0",
        }
        endpoints.CriarTrecho(self.login).send(json=json_body)

    def mover_posicao_checkpoint_para_baixo(self, trecho_id, ordem="BAIXO"):
        response = endpoints.AlterarPosicao(self.login).send(
            trecho_id=trecho_id, empresa_id=self.company.company_external_id, ordem=ordem
        )
        return response.raw

    def atualizar_checkpoint(self, payload, nova_descricao=None, nova_duracao=None, nova_quilometragem=None):
        json_body = {
            "cidadeDestinoId": payload["cidadeDestino"]["id"],
            "duracao": nova_duracao and fill_duracao(nova_duracao) or payload["duracao"],
            "empresaId": payload["idEmpresa"],
            "id": payload["id"],
            "ordem": payload["ordem"],
            "pontoEmbarque": nova_descricao or payload["pontoEmbarque"],
            "quilometragem": nova_quilometragem or payload["quilometragem"],
            "rotaId": payload["rotaDto"]["id"],
            "taxaEmbarque": "0",
        }
        endpoints.CriarTrecho(self.login).send(json=json_body)

    def listar_trechos(self, rota_id):
        response = endpoints.ListarTrechos(self.login).send(
            id_empresa=self.company.company_external_id, id_rota=rota_id
        )
        return response.raw

    def lista_reservas_viagem(self, grupo_classe_external_id, detailed=False):
        company_external_id = self.company.company_external_id
        endpoint = endpoints.ListarReservasViagem if detailed else endpoints.ListarReservasViagemIds
        try:
            reservas = (
                endpoint(self.login)
                .send(itinerario=grupo_classe_external_id, company_external_id=company_external_id)
                .raw
            )
        except VexadoAPIError as exc:
            if "itinerarioVazio" in exc.codigos:
                return []
            raise
        return reservas

    def cancelar_reserva_por_localizador(self, localizador):
        return self._cancelar_venda(
            {
                "empresaId": self.company.company_external_id,
                "motivo": "Viagem Alterada",
                "reservas": [localizador],
            }
        )

    def inativar_grupo_classe(self, grupo_classe_external_id):
        company_external_id = self.company.company_external_id
        endpoints.InativarItinerario(self.login).send(
            company_external_id=company_external_id, itinerario=grupo_classe_external_id
        )

    def update_bpe_passagem(self, passagem):
        id_pedido = passagem.pedido_external_id
        dados_reserva = self._recuperar_pedido(id_pedido)
        for dpassagem in dados_reserva["reservas"]:
            if str(dpassagem.get("id")) != str(passagem.localizador):
                continue
            bpe_info = dpassagem.get("transmissaoBpe")
            if bpe_info:
                passagem.bpe_qrcode = bpe_info.get("qrCod")
                passagem.bpe_em_contingencia = bpe_info.get("emitidoEmContigencia")
                passagem.data_autorizacao = bpe_info.get("dataAutorizacao") or to_default_tz(timezone.now())
                passagem.chave_bpe = bpe_info.get("chaveAcesso")
                passagem.numero_bpe = bpe_info["chaveAcesso"][-15:-10] if passagem.chave_bpe else None
                passagem.serie_bpe = bpe_info["chaveAcesso"][-22:-19] if passagem.chave_bpe else None
                passagem.protocolo_autorizacao = bpe_info.get("protocoloTransmissao")
                passagem.save()
            break
        return passagem

    def cancelar_reservas_por_pedido_id(self, pedido_id):
        passagens = self._recuperar_pedido(pedido_id)
        responses = []
        for p in passagens["reservas"]:
            passagem_api = {"poltrona": p["numeroPoltrona"], "localizador": p["id"]}
            responses.append(passagem_api)
            if p["situacaoReserva"] == "CANCELADO":
                continue
            try:
                self._cancelar_venda(
                    {
                        "empresaId": self.company.company_external_id,
                        "motivo": "Passagem com erro",
                        "reservas": [p["id"]],
                    }
                )
            except VexadoAPIError as ex:
                error_type = None
                if "tempoCancelamentoExpirado" in ex.codigos or "reservaNaoPodeSerCancelada" in ex.codigos:
                    error_type = "nao_autorizado"
                passagem_api["error"] = error_str(ex)
                passagem_api["error_type"] = error_type
                continue
            except RodoviariaConnectionError as ex:
                passagem_api["error"] = error_str(ex)
                passagem_api["error_type"] = "erro_conexao"

        return responses

    def _retorna_provider_data(self, dados_passagens):
        provider_data = dados_passagens.copy()
        reservas = provider_data.pop("reservas")
        dict_provider_data = {}

        for reserva in reservas:
            dict_provider_data[reserva["id"]] = {"reservas": reserva, **provider_data}

        return dict_provider_data

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        id_pedido = passagem.pedido_external_id
        try:
            dados_reserva = self._recuperar_pedido(id_pedido)
        except RodoviariaBaseException:
            buserlogger.exception(
                "Vexado - Erro ao consultar reserva na atualização de passagens no staff com id_pedido=%s", id_pedido
            )
            raise
        for reserva in dados_reserva["reservas"]:
            if str(reserva.get("id")) != str(passagem.localizador):
                continue
            if reserva["situacaoReserva"].lower() == "aprovado":
                status_reserva = "confirmada"
            else:
                status_reserva = "cancelada"
            bilhete_padrao = RetornoConsultarBilheteForm.parse_obj(
                {
                    "integracao": "Vexado",
                    "localizador": reserva["id"],
                    "pedido_external_id": dados_reserva["id"],
                    "status": status_reserva,
                    "numero_assento": reserva["numeroPoltrona"],
                    "primeiro_nome_pax": reserva["passageiroDto"]["nome"],
                    "tipo_documento": "CPF",
                    "numero_documento": reserva["passageiroDto"]["cpfPassageiro"],
                    "birth": reserva["passageiroDto"]["dtNascimento"],
                    "bpe_public_url": reserva["transmissaoBpe"]["qrCod"],
                    "data_partida": reserva["dataHoraPartida"],
                    "data_chegada": reserva["dataHoraDesembarque"],
                    "origem": reserva["descCidadeOrigem"],
                    "destino": reserva["descCidadeDestino"],
                    "empresa_name": reserva["nomeEmpresa"],
                    "valor_passagem": reserva["valor"],
                    "taxa_embarque": reserva["taxaServico"],
                }
            )
            return bilhete_padrao
        raise PassengerNotRegistered(f"Vexado - Não foi possível encontrar a passagem com {id_pedido=}")

    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        return descobrir_operacao.descobrir_operacao(self.login, next_days, shift_days, queue_name, return_task_object)


def lista_empresas_api(login_params: VexadoAnonymousLogin):
    company = Company.objects.filter(
        integracao__name=Integracao.API.VEXADO, modelo_venda=login_params.modelo_venda
    ).first()
    empresas = VexadoAPI(company).listar_empresas()
    return empresas


def _get_travels_permite_emissao_com_menos_de_tres_horas():
    rotas_fixas = constance_config.TRAVELS_PERMITE_EMISSAO_MENOS_DE_3_HORAS
    return [int(servico) for servico in rotas_fixas.split(",") if servico]
