import asyncio
import collections
import copy
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal as D

from asgiref.sync import async_to_sync, sync_to_async
from constance import config as constance_config
from pydantic import parse_obj_as
from zoneinfo import ZoneInfo

from commons.dateutils import midnight, now, to_default_tz, today_midnight
from commons.django_utils import error_str
from commons.memoize import memoize_with_log
from commons.redis import lock
from commons.utils import is_running_on_celery, str_contains
from rodoviaria.api.executors.impl import get_async_http_executor, get_http_executor
from rodoviaria.api.forms import (
    BuscarServicoForm,
    RetornoConsultarBilheteForm,
    RetornoItinerario,
    ServicoForm,
)
from rodoviaria.api.praxio import descobrir_operacao, endpoints, models
from rodoviaria.api.praxio.auth import PraxioAuth
from rodoviaria.api.praxio.exceptions import RodoviariaCompraParcialPraxioException
from rodoviaria.api.praxio.memcache import PraxioMC as praxio_mc
from rodoviaria.api.praxio.models import RotaPraxio, TrechoCompra
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Assento, Deck, Onibus
from rodoviaria.forms.staff_forms import PraxioUndefinedCompanyClient
from rodoviaria.models.core import Company, LocalEmbarque, Passagem, TrechoClasse
from rodoviaria.models.praxio import PraxioLogin
from rodoviaria.service import descobrir_rotas_praxio_async_svc, poltronas_svc
from rodoviaria.service.class_match_svc import get_buser_class_by_company
from rodoviaria.service.exceptions import (
    PassengerTicketAlreadyReturnedException,
    PoltronaJaSelecionadaException,
    RodoviariaBaseException,
    RodoviariaException,
    RodoviariaOverbookingException,
)
from rodoviaria.service.solicita_cancelamento_svc import solicita_cancelamento_passagens

buserlogger = logging.getLogger("rodoviaria")


class PraxioAPI(RodoviariaAPI):
    Rota = RotaPraxio
    SITUACAO_POLTRONA_DISPONIVEL = 0
    SITUACAO_POLTRONA_SUGERIDA = [4, 5]
    SITUACOES_PERMITIDAS = [SITUACAO_POLTRONA_DISPONIVEL, *SITUACAO_POLTRONA_SUGERIDA]
    MAP_CLASSES_PRAXIO = {
        0: "Convencional com sanitário",
        2: "Convencional sem sanitário",
        3: "Semileito",
        4: "Leito com ar condicionado",
        5: "Leito sem ar condicionado",
        6: "Executivo",
        7: "Semiurbano",
    }
    divergencia_maxima_pct = 50

    def __init__(self, company):
        super().__init__(company)
        self.login = PraxioLogin.objects.select_related("company", "company__integracao").get(company=company)
        self.cache = praxio_mc(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    def _get_token(self, force_update=False):
        return PraxioAuth.from_client(self.login, force_update)

    def _id_sessao_op(self):
        return self._get_token().id_sessao_op

    def _id_estabelecimento(self):
        return self._get_token().id_estabelecimento

    def _serie_bpe(self):
        return self._get_token().serie_bpe

    def _get_passagens_pra_devolucao_erro_compra(self, json_response):
        passagens = {}
        passagem = json_response.get("oObj") and json_response.get("oObj").get("Passagem")
        list_passagem = passagem and passagem.get("ListaPassagem")
        if list_passagem:
            for x in list_passagem:
                msg_passagem = x.get("Mensagem")
                if str_contains(msg_passagem, "sucesso"):
                    passagens[str(x.get("Poltrona"))] = {
                        "NumPassagem": x.get("NumPassagem"),
                        "SerieBloco": x.get("SerieBloco"),
                        "IDEstabelecimento": x.get("IDEstabelecimento"),
                    }
        return passagens

    def _bloquear_poltronas_para_venda(self, params_poltronas: list[models.BloquearPoltronaForm]):
        if constance_config.USE_HTTP_ASYNC is True and not is_running_on_celery():
            return self._bloquear_poltronas_para_venda_async(params_poltronas)

        for bloquear_poltrona_params in params_poltronas:
            last_resp = self._bloquear_poltrona(bloquear_poltrona_params)
        return last_resp

    @async_to_sync
    async def _bloquear_poltronas_para_venda_async(self, params_poltronas):
        tasks = []
        for bloquear_poltrona_params in params_poltronas:
            tasks.append(asyncio.create_task(self._bloquear_poltrona_coroutine(bloquear_poltrona_params)))
        done, _ = await asyncio.wait(tasks, return_when=asyncio.FIRST_EXCEPTION)
        return next(iter(done)).result()

    def _bloquear_poltrona(self, params: models.BloquearPoltronaForm):
        json = models.BloquearPoltronaForm.parse_obj(params).dict(by_alias=True, exclude_unset=True, exclude_none=True)

        request_config = endpoints.BloquearPoltronaConfig(self.login)
        executor = get_http_executor()
        response = request_config.invoke(executor, json=json)
        poltrona_bloqueada = response.json()["Sucesso"]
        if not poltrona_bloqueada:
            buserlogger.info("Falha ao bloquear poltrona", extra={"params": params, "response": response.json()})
            raise PoltronaJaSelecionadaException("Falha ao bloquear poltrona")
        return response

    async def _bloquear_poltrona_coroutine(self, params):
        json = models.BloquearPoltronaForm.parse_obj(params).dict(by_alias=True, exclude_unset=True, exclude_none=True)

        request_config = endpoints.BloquearPoltronaConfig(self.login)
        executor = get_async_http_executor()
        response = await request_config.ainvoke(executor, json=json)
        poltrona_bloqueada = response.json()["Sucesso"]
        if not poltrona_bloqueada:
            buserlogger.info("Falha ao bloquear poltrona", extra={"params": params, "response": response.json()})
            raise PoltronaJaSelecionadaException("Falha ao bloquear poltrona")
        return response

    def _desbloquear_poltrona(self, params):
        params = models.DesbloquearPoltronaListForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        poltronas = []
        for desbloquear_poltrona_params in params["listDesbloquearPoltrona"]:
            poltronas.append(self._desbloquear_uma_poltrona(desbloquear_poltrona_params))
        return poltronas[-1]

    def _desbloquear_uma_poltrona(self, params):
        params["IdSessaoOp"] = self._id_sessao_op()
        json = models.DesbloquearPoltronaForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        request_config = endpoints.DesmarcaPoltronaConfig(self.login)
        executor = get_http_executor()
        response = request_config.invoke(executor, json=json)
        return response.json()

    def _cancelar_venda(self, passagens_cadastradas):
        if constance_config.USE_HTTP_ASYNC and not is_running_on_celery():
            return self._cancelar_venda_async(passagens_cadastradas)
        else:
            return self._cancelar_venda_sync(passagens_cadastradas)

    def _cancelar_venda_sync(self, passagens_cadastradas):
        passagens_cadastradas = list(passagens_cadastradas)
        id_estabelecimento = self._id_estabelecimento()
        id_estabelecimento_passagem = self._id_estabelecimento()
        serie_bloco = self._serie_bpe()
        last_resp = {}
        for passagem in passagens_cadastradas:
            if not isinstance(passagem, Passagem):
                raise ValueError("Passagem não é do tipo Passagem")
            if passagem.localizador:
                numero_passagem = int(passagem.localizador.split("-")[0])
                id_estabelecimento = int(passagem.localizador.split("@")[1])
                serie_bloco = "@"
            else:
                numero_passagem = passagem.numero_passagem
                if passagem.serie_bpe:
                    serie_bloco = passagem.serie_bpe
            if passagem.id_estabelecimento_external:
                id_estabelecimento_passagem = passagem.id_estabelecimento_external

            try:
                params = {
                    "IdSessaoOp": self._id_sessao_op(),
                    "IdEstabelecimento": id_estabelecimento,
                    "IdEstabelecimentoDevolucao": id_estabelecimento,
                    "ValorVenda": passagem.preco_rodoviaria,
                    "Passagem": {
                        "SerieBloco": serie_bloco,
                        "NumeroPassagem": numero_passagem,
                        "IdEstabelecimento": id_estabelecimento_passagem,
                        "ValorDevolucao": passagem.preco_rodoviaria,
                    },
                }

                params = self._add_categoria_especial_params(passagem, params)

                json = models.GravaDevolucaoForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
                request_config = endpoints.GravaDevolucaoPassagemConfig(self.login, with_auth=False)
                executor = get_http_executor()
                last_resp = request_config.invoke(executor, json=json).json()
            except PassengerTicketAlreadyReturnedException as ex:
                self._save_error_cancelamento_passagens(passagens_cadastradas, error_str(ex))
                raise ex
        return last_resp

    def _add_categoria_especial_params(self, passagem, params):
        tipo_passageiro = self._get_tipo_passageiro_from_categoria_especial(passagem.categoria_especial)
        if tipo_passageiro:
            params["Passagem"]["PercDesconto"] = Passagem.get_desconto(passagem.categoria_especial)
            params["Passagem"]["TipoPassageiro"] = tipo_passageiro
        return params

    @async_to_sync
    async def _cancelar_venda_async(self, passagens_cadastradas):
        tasks = [asyncio.create_task(self._cancelar_passagem(passagem)) for passagem in passagens_cadastradas]
        (dones, _) = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
        try:
            passagens = [t.result() for t in dones]
            return passagens[0]
        except Exception as ex:
            # Se alguma passagem deu erro, marca todas elas com erro no cancelamento
            sync_to_async(self._save_error_cancelamento_passagens)(passagens_cadastradas, error_str(ex))
            raise ex

    async def _cancelar_passagem(self, passagem: Passagem):
        (
            numero_passagem,
            id_estabelecimento,
            serie_bloco,
            id_estabelecimento_passagem,
        ) = self._get_dados_para_cancelamento_from_passagem(passagem)

        params = {
            "IdSessaoOp": self._id_sessao_op(),
            "IdEstabelecimento": id_estabelecimento,
            "IdEstabelecimentoDevolucao": id_estabelecimento,
            "ValorVenda": passagem.preco_rodoviaria,
            "Passagem": {
                "SerieBloco": serie_bloco,
                "NumeroPassagem": numero_passagem,
                "IdEstabelecimento": id_estabelecimento_passagem,
                "ValorDevolucao": passagem.preco_rodoviaria,
            },
        }
        params = self._add_categoria_especial_params(passagem, params)

        json = models.GravaDevolucaoForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        request_config = endpoints.GravaDevolucaoPassagemConfig(self.login, with_auth=False)
        executor = get_async_http_executor()
        response = await request_config.ainvoke(executor, json=json)
        return response.json()

    def _get_dados_para_cancelamento_from_passagem(self, passagem):
        id_estabelecimento = self._id_estabelecimento()
        id_estabelecimento_passagem = self._id_estabelecimento()
        serie_bloco = self._serie_bpe()
        if passagem.localizador:
            numero_passagem = int(passagem.localizador.split("-")[0])
            id_estabelecimento = int(passagem.localizador.split("@")[1])
            serie_bloco = "@"
        else:
            numero_passagem = passagem.numero_passagem
            if passagem.serie_bpe:
                serie_bloco = passagem.serie_bpe
        if passagem.id_estabelecimento_external:
            id_estabelecimento_passagem = passagem.id_estabelecimento_external
        return (
            numero_passagem,
            id_estabelecimento,
            serie_bloco,
            id_estabelecimento_passagem,
        )

    def _confirmar_venda(self, params):
        venda_bpe = self.company.has_feature(Company.Feature.BPE)
        if venda_bpe:
            json = models.ConfirmarVendaFormBPE.parse_obj(params).dict(
                by_alias=True, exclude_unset=True, exclude_none=True
            )
            request_config = endpoints.VendaPassagemBPEConfig(self.login, with_auth=False)
        else:
            json = models.ConfirmarVendaFormVoucher.parse_obj(params).dict(
                by_alias=True, exclude_unset=True, exclude_none=True
            )
            request_config = endpoints.VendaPassagemVoucherConfig(self.login, with_auth=False)

        executor = get_http_executor()
        return request_config.invoke(executor, json=json).json()

    def _calcula_preco_rodoviaria_por_trecho(self, provider_data) -> list[D]:
        total_conexao = self._get_price(provider_data, True)

        perna_1 = provider_data
        perna_2 = provider_data["conexoes"][0]
        preco_rodoviaria_primeira_perna = self._get_preco_perna(perna_1)
        preco_rodoviaria_segunda_perna = self._get_preco_perna(perna_2)

        if preco_rodoviaria_primeira_perna + preco_rodoviaria_segunda_perna != total_conexao:
            # Documentação está incompleta. Essa validação garante que os descontos são aplicados da forma certa.
            buserlogger.info(
                "PraxioAPI: Soma das pernas diferente do total_conexao. "
                "preco_rodoviaria_primeira_perna=%s. "
                "preco_rodoviaria_segunda_perna=%s. "
                "total_conexao=%s.",
                preco_rodoviaria_primeira_perna,
                preco_rodoviaria_segunda_perna,
                total_conexao,
            )

        return [preco_rodoviaria_primeira_perna, preco_rodoviaria_segunda_perna]

    def _create_passagens(
        self,
        params: ComprarForm,
        trechoclasse: TrechoClasse,
        trechos: list[TrechoCompra],
        provider_data,
    ):
        is_conexao = len(trechos) > 1
        preco_rodoviaria_por_trecho = [D(str(trechoclasse.preco_rodoviaria))]
        valor_cheio_por_trecho = [D(str(params.valor_cheio))]
        if is_conexao:
            preco_rodoviaria_por_trecho = self._calcula_preco_rodoviaria_por_trecho(provider_data)
            valor_cheio_por_trecho = preco_rodoviaria_por_trecho

        passagens_cadastradas = []
        for trecho, preco_rodoviaria, valor_cheio in zip(trechos, preco_rodoviaria_por_trecho, valor_cheio_por_trecho):
            for poltrona, passageiro in zip(params.poltronas, params.passageiros):
                passagens_cadastradas.append(
                    Passagem(
                        trechoclasse_integracao=trechoclasse,
                        company_integracao=self.company,
                        poltrona_external_id=poltrona,
                        buseiro_internal_id=passageiro.id,
                        travel_internal_id=params.travel_id,
                        valor_cheio=valor_cheio,
                        status=Passagem.Status.INCOMPLETA,
                        preco_rodoviaria=preco_rodoviaria,
                        desconto=trecho.desconto,
                        origem=trecho.origem,
                        destino=trecho.destino,
                    )
                )

        passagens_cadastradas = self.create_passagens(passagens_cadastradas)

        valor_cheio = D(str(params.valor_cheio))
        total_conexao_calculado_pernas = D(str(sum(preco_rodoviaria_por_trecho)))
        if is_conexao and total_conexao_calculado_pernas != valor_cheio:
            buserlogger.warning(
                "PraxioAPI: valor total_conexao diferente valor do trecho. " "valor_cheio=%s. " "total_conexao=%s.",
                valor_cheio,
                total_conexao_calculado_pernas,
            )

            # Não é permitida divergência na compra de conexao.
            total_conexao = self._get_price(provider_data, True)
            if valor_cheio != total_conexao:
                error_msg = f"PraxioAPI: Divergencia na compra de conexao. {valor_cheio=} != {total_conexao=}"
                for passagem in passagens_cadastradas:
                    passagem.erro = error_msg
                    passagem.status = Passagem.Status.ERRO
                Passagem.objects.bulk_update(passagens_cadastradas, ["erro", "status"])
                raise RodoviariaException(error_msg)
        return passagens_cadastradas

    def _bloquear_poltronas_params(
        self, poltronas, trechoclasse: TrechoClasse, trechos: list[TrechoCompra]
    ) -> list[models.BloquearPoltronaForm]:
        id_viagem = int(trechoclasse.external_id)
        id_tipo_veiculo = int(trechoclasse.external_id_tipo_veiculo)
        bloquear_poltrona_list = []
        for trecho in trechos:
            id_origem = trecho.origem.id_external
            id_destino = trecho.destino.id_external
            for poltrona in poltronas:
                bloquear_poltrona_list.append(
                    models.BloquearPoltronaForm(
                        id_sessao_op=self._id_sessao_op(),
                        id_viagem=id_viagem,
                        id_poltrona=poltrona,
                        id_tipo_veiculo=id_tipo_veiculo,
                        id_loc_origem=id_origem,
                        id_loc_destino=id_destino,
                        bloqueia=1,
                    )
                )

        return bloquear_poltrona_list

    def _comprar_params(self, params: ComprarForm, trechoclasse: TrechoClasse, trechos: list[TrechoCompra]):
        pgto_by_comp = (
            json.loads(constance_config.PRAXIO_TIPO_PAGAMENTO_POR_EMPRESA)
            if constance_config.PRAXIO_TIPO_PAGAMENTO_POR_EMPRESA
            else {}
        )
        tipo_pagamento = pgto_by_comp.get(str(self.company_id), 0)

        poltronas = params.poltronas
        passageiros = params.passageiros
        categoria_especial = params.categoria_especial
        venda_bpe = self.company.has_feature(Company.Feature.BPE)
        id_viagem = int(trechoclasse.external_id)
        desconto_manual = int(self.login.desconto_manual)
        preco_rodoviaria = D(str(trechoclasse.preco_rodoviaria))
        id_desconto = trechoclasse.external_id_desconto or 0
        origem_tz = trechoclasse.origem.cidade.timezone if trechoclasse.origem.cidade.timezone else "America/Sao_Paulo"
        hora_partida = trechoclasse.external_datetime_ida or trechoclasse.datetime_ida
        hora_partida = hora_partida.astimezone(ZoneInfo(origem_tz)).strftime("%H%M")
        passagens_xml_envio = []
        for trecho in trechos:
            passagem_xml = []
            soma_valor_passagens = 0
            id_origem = trecho.origem.id_external
            id_destino = trecho.destino.id_external
            for poltrona, passageiro in zip(poltronas, passageiros):
                passagem_xml.append(
                    {
                        "IdEstabelecimento": self._id_estabelecimento(),
                        "SerieBloco": self._serie_bpe(),
                        "NumPassagem": 1,
                        "Poltrona": poltrona,
                        "Pricing": trecho.pricing,
                        "Desconto": trecho.desconto,
                        "DescontoManual": desconto_manual,
                        "IdDesconto": id_desconto,
                        "IdRota": trecho.id_rota,
                        "TipoPassageiro": self._get_tipo_passageiro_from_categoria_especial(categoria_especial),
                        "NomeCli": passageiro.name[:50],
                        "IdentidadeCli": passageiro.rg_number[:20],
                        "CpfCnpjCli": passageiro.cpf,
                        "Telefone1": passageiro.phone or "11999999999",
                    }
                )
                soma_valor_passagens += preco_rodoviaria

            if self._deve_pegar_valor_categoria_especial(params):
                soma_valor_passagens = self._get_valor_passagem_categoria_especial(
                    trechoclasse,
                    categoria_especial,
                    id_viagem,
                    hora_partida,
                    len(passageiros),
                )

            list_vendas_xml_envio = "listVendasXmlEnvio" if venda_bpe else "ListaVendaXmlEnvio"
            passagens_xml_envio.append(
                {
                    "IdSessaoOp": self._id_sessao_op(),
                    "IdEstabelecimentoVenda": self._id_estabelecimento(),
                    "IdViagem": id_viagem,
                    "HoraPartida": hora_partida,
                    "IdOrigem": id_origem,
                    "IdDestino": id_destino,
                    "Embarque": "S",
                    "Seguro": "N",
                    "Excesso": "N",
                    "IdCaixa": 0,
                    "BPe": 1,
                    "PassagemXml": passagem_xml,
                    "pagamentoXml": [
                        {
                            "DataPagamento": datetime.now().isoformat(),
                            "TipoPagamento": tipo_pagamento,
                            "ValorPagamento": soma_valor_passagens,
                        }
                    ],
                }
            )
        confirmar_venda_params = {list_vendas_xml_envio: passagens_xml_envio}
        return confirmar_venda_params

    def _deve_pegar_valor_categoria_especial(self, params):
        return (
            params.categoria_especial
            and params.categoria_especial
            not in (
                Passagem.CategoriaEspecial.NORMAL,
                Passagem.CategoriaEspecial.IGNORADO,
            )
            and len(params.passageiros) == 1
        )

    def _cancela_dict_to_cancela_params(self, params):
        trechoclasse_id = params.trechoclasse_id
        travel_id = params.travel_id
        buseiro_id = params.buseiro_id
        passagens_cadastradas = self.get_passagens_confirmadas(travel_id, buseiro_id=buseiro_id)
        if not passagens_cadastradas:
            return {}
        id_viagem = int(TrechoClasse.objects.get(trechoclasse_internal_id=trechoclasse_id).external_id)
        poltronas_vendidas = list(passagens_cadastradas.values_list("poltrona_external_id", flat=True))
        desbloquear_poltrona_list = []
        while len(poltronas_vendidas) > 0:
            id_poltrona = poltronas_vendidas.pop(0)
            msg = (
                f"Marketplace - Praxio Luna: viagem {id_viagem} no host {self.base_url} com a poltrona de numero"
                f" {id_poltrona} sera cancelada"
            )
            buserlogger.info(msg)
            desbloquear_poltrona_list.append({"IdViagem": id_viagem, "IdPoltrona": id_poltrona})
        desbloquear_poltrona_params = {"listDesbloquearPoltrona": desbloquear_poltrona_list}
        return {
            "desbloquear_poltrona_params": desbloquear_poltrona_params,
            "passagens_cadastradas": passagens_cadastradas,
            "pax_valido": params.pax_valido,
        }

    def _verifica_poltrona_params(self, verifica_poltronas_params, andar=None):
        trechoclasse = self.get_active_trecho_classe(verifica_poltronas_params.trechoclasse_id)
        id_viagem = int(trechoclasse.external_id)
        id_origem = int(trechoclasse.origem.id_external)
        id_destino = int(trechoclasse.destino.id_external)
        id_tipo_veiculo = int(trechoclasse.external_id_tipo_veiculo)
        poltronas_livres_params = {
            "IdViagem": id_viagem,
            "IdTipoVeiculo": id_tipo_veiculo,
            "IdLocOrigem": id_origem,
            "IdLocDestino": id_destino,
            "NumeroPoltronas": verifica_poltronas_params.passageiros,
        }
        if andar:
            poltronas_livres_params["Andar"] = andar
        return poltronas_livres_params

    def _get_price(self, servico, has_connection=False):
        discount = D("0")
        pricing = D("0")
        if has_connection:
            raw_price = D(str(servico["TotalConexao"]))
            if servico.get("ValorMaiorDesconto"):
                discount = D(str(servico["ValorMaiorDesconto"]))
        else:
            raw_price = (
                D(str(servico["TxEmbarque"]))
                + D(str(servico["Seguro"]))
                + D(str(servico["Pedagio"]))
                + D(str(servico["VlTarifa"]))
            )
            discount = D(str(servico["Desconto"]))
            pricing = D(str(servico["Pricing"]))

        price = round(raw_price, 2)
        # parceiro pode cadastrar desconto negativo quando quer aumentar o preço
        price_with_discount = price - discount - pricing
        if price_with_discount < 0:
            buserlogger.warning(
                "PraxioAPI - Servico da empresa %s",
                self.company.name,
                " com preco negativo %s. Retornando preço sem desconto. Servico: %s",
                price_with_discount,
                servico,
            )
            return price
        return price_with_discount

    def _parse_retorno_buscar_servico(
        self, servico_encontrado=None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            existe_conexao = servico_encontrado.get("conexoes") and len(servico_encontrado.get("conexoes")) > 0
            servico = ServicoForm(
                has_connection=existe_conexao,
                provider_data=servico_encontrado,
                external_id=servico_encontrado["IdViagem"],
                tipo_veiculo=servico_encontrado["TipoVeiculo"],
                preco=self._get_price(servico_encontrado, existe_conexao),
                vagas=servico_encontrado["ViagemTFO"]["PoltronasDisponiveis"],
                classe=self._get_classe(servico_encontrado["ViagemTFO"]),
                desconto=D(str(servico_encontrado["Desconto"])),
                id_desconto=servico_encontrado["IdDesconto"],
                linha=servico_encontrado["ViagemTFO"]["NomeLinha"],
                external_datetime_ida=self._get_datetime_servico(
                    timezone,
                    servico_encontrado["ViagemTFO"]["DataHoraInicio"],
                    "%Y-%m-%dT%H:%M",
                ),
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico, timezone):
        existe_conexao = servico.get("conexoes") and len(servico.get("conexoes")) > 0
        servico_form = ServicoForm.parse_obj(
            {
                "external_id": servico["IdViagem"],
                "preco": self._get_price(servico, existe_conexao),
                "external_datetime_ida": self._get_datetime_servico(
                    timezone, servico["ViagemTFO"]["DataHoraInicio"], "%Y-%m-%dT%H:%M"
                ),
                "classe": self._get_classe(servico["ViagemTFO"]).lower(),
                "external_company_id": self.company.company_external_id,
                "vagas": servico["ViagemTFO"]["PoltronasDisponiveis"],
                "provider_data": servico,
            }
        )
        return servico_form

    def _translate_params_buscar_servico(self, params):
        return {
            "LocalidadeOrigem": params.get("LocalidadeOrigem") or params.get("origem"),
            "LocalidadeDestino": params.get("LocalidadeDestino") or params.get("destino"),
            "DataPartida": params.get("DataPartida") or params.get("data"),
            "IdEstabelecimento": params.get("IdEstabelecimento"),
            "TempoPartida": 1,
            "DescontoAutomatico": 1,
            "sugestaoPassagem": 1,
        }

    def _poltrona_disponivel(self, situacao, categoria_especial: Passagem.CategoriaEspecial | None = None):
        categorias_situacoes = {
            Passagem.CategoriaEspecial.IDOSO_100: [-10],
            Passagem.CategoriaEspecial.IDOSO_50: [-4],
            Passagem.CategoriaEspecial.JOVEM_100: [-7],
            Passagem.CategoriaEspecial.JOVEM_50: [-6],
            Passagem.CategoriaEspecial.CRIANCA: [],  # TODO,
            Passagem.CategoriaEspecial.PCD: [-2],
            Passagem.CategoriaEspecial.ESPACO_MULHER: [-12],
            Passagem.CategoriaEspecial.NORMAL: self.SITUACOES_PERMITIDAS,
        }
        if categoria_especial is not None:
            return situacao in categorias_situacoes[categoria_especial]
        return situacao in self.SITUACOES_PERMITIDAS

    def _categoria_especial(self, situacao):
        categorias_situacoes = {
            -10: Passagem.CategoriaEspecial.IDOSO_100,
            -4: Passagem.CategoriaEspecial.IDOSO_50,
            -7: Passagem.CategoriaEspecial.JOVEM_100,
            -6: Passagem.CategoriaEspecial.JOVEM_50,
            -2: Passagem.CategoriaEspecial.PCD,
            -12: Passagem.CategoriaEspecial.ESPACO_MULHER,
            # 0:Passagem.CategoriaEspecial.NORMAL,
            # 4:Passagem.CategoriaEspecial.NORMAL,
            # 5:Passagem.CategoriaEspecial.NORMAL,
        }
        return categorias_situacoes.get(situacao, Passagem.CategoriaEspecial.NORMAL)

    def get_map_poltronas(
        self,
        trechoclasse_id,
        andar=None,
        categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL,
    ):
        params = self._verifica_poltrona_params(
            VerificarPoltronaForm(trechoclasse_id=trechoclasse_id, passageiros=1), andar
        )
        params["IdSessaoOp"] = self._id_sessao_op()
        params["VerificarSugestao"] = 1

        poltronas = self._retorna_poltronas_request(params)

        resp = {}
        for poltrona in poltronas:
            resp[poltrona["Caption"]] = (
                "livre" if self._poltrona_disponivel(poltrona["Situacao"], categoria_especial) else "ocupada"
            )
        sem_poltronas_validas = resp == {"": "ocupada"}
        if sem_poltronas_validas and andar is None:
            return self.get_map_poltronas(trechoclasse_id, andar=2, categoria_especial=categoria_especial)
        return collections.OrderedDict(sorted(resp.items()))

    def get_map_poltronas_typed(
        self,
        trechoclasse_id,
        andar=None,
        categoria_especial: Passagem.CategoriaEspecial | None = None,
    ) -> list[models.MapaPoltronaItem]:
        params = self._verifica_poltrona_params(
            VerificarPoltronaForm(trechoclasse_id=trechoclasse_id, passageiros=1), andar
        )
        params["IdSessaoOp"] = self._id_sessao_op()
        params["VerificarSugestao"] = 1

        poltronas = self._retorna_poltronas_request(params)

        resp: dict[str, models.MapaPoltronaItem] = {}
        for poltrona in poltronas:
            num_poltrona = poltrona["Caption"]
            if not num_poltrona:
                continue

            categoria = self._categoria_especial(poltrona["Situacao"])
            if categoria_especial and categoria_especial != categoria:
                continue

            resp[num_poltrona] = models.MapaPoltronaItem(
                disponivel=self._poltrona_disponivel(poltrona["Situacao"], categoria),
                numero=num_poltrona,
                categoria_especial=categoria,
            )

        sem_poltronas_validas = len(resp) == 0
        if sem_poltronas_validas and andar is None:
            return self.get_map_poltronas_typed(trechoclasse_id, andar=2, categoria_especial=categoria_especial)
        poltronas_ordenadas: list[models.MapaPoltronaItem] = list(
            collections.OrderedDict(sorted(resp.items())).values()
        )
        return poltronas_ordenadas

    def _retorna_poltronas_request(self, params):
        json = models.RetornaPoltronasForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        response = endpoints.RetornaPoltronasConfig(self.login).invoke(get_http_executor(), json=json)
        poltronas = response.json()["LaypoltronaXml"]["PoltronaXmlRetorno"]
        return poltronas

    def vagas_por_categoria_especial(self, trechoclasse_id: int):
        mapa_poltronas = self.get_map_poltronas_typed(trechoclasse_id)
        assentos_por_categoria = collections.Counter([str(assento.categoria_especial) for assento in mapa_poltronas])
        return assentos_por_categoria

    def atualiza_origens(self):
        params = {
            "IdSessaoOp": self._id_sessao_op(),
            "IdEstabelecimento": self._id_estabelecimento(),
        }
        json = models.BuscaOrigemForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)

        response = endpoints.PartidasOrigensConfig(self.login).invoke(get_http_executor(), json=json).json()
        cleaned = response["Xml"]["NewDataSet"]["Table"]
        parsed = parse_obj_as(list[models.LocalPartida], cleaned)
        return parsed

    def cidades_destino(self, origem_external_id, miss_cache=False):
        if miss_cache:
            self._call_buscar_destinos.delete_memoized()
        destinos = self._call_buscar_destinos(origem_external_id)
        return [destino.external_local_id for destino in destinos]

    @memoize_with_log(48 * 60 * 60)
    def _call_buscar_destinos(self, origem_external_id):
        json = {"IdOrigem": origem_external_id}
        response = endpoints.BuscarDestinosConfig(self.login).invoke(get_http_executor(), json=json).json()
        cleaned = response["Xml"]["NewDataSet"]["Table"] if response["Xml"] else []
        return parse_obj_as(list[models.SimpleLocalidade], cleaned)

    def cancela_venda(self, params: CancelaVendaForm):
        params = self._cancela_dict_to_cancela_params(params)
        if not params:
            return {}
        passagens_cadastradas = params.pop("passagens_cadastradas")
        desbloquear_poltrona_params = params.pop("desbloquear_poltrona_params")
        try:
            if constance_config.DESBLOQUEIA_POLTRONA_NO_CANCELAMENTO_PRAXIO:
                self._desbloquear_poltrona(desbloquear_poltrona_params)
            cancela_venda_response = self._cancelar_venda(passagens_cadastradas)
        except Exception as ex:
            self._save_error_cancelamento_passagens(passagens_cadastradas, error_str(ex))
            raise ex
        for passagem in passagens_cadastradas:
            msg = f"Localizador - Praxio: passagem {passagem.numero_passagem}|serie {passagem.serie_bpe} foi cancelada"
            buserlogger.info(msg)
            passagem.status = Passagem.Status.CANCELADA
            passagem.datetime_cancelamento = to_default_tz(datetime.now())
            passagem.tags.remove("cancelamento_pendente")
        Passagem.objects.bulk_update(passagens_cadastradas, ["status", "datetime_cancelamento", "updated_at"])
        return cancela_venda_response

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=None):
        trechoclasse = self.get_active_trecho_classe(params.trechoclasse_id)

        provider_data = json.loads(trechoclasse.provider_data)
        trechos_compra = self._get_trechos_compra(trechoclasse, provider_data)

        passagens_cadastradas = self._create_passagens(params, trechoclasse, trechos_compra, provider_data)
        confirmar_venda_params = self._comprar_params(params, trechoclasse, trechos_compra)
        self._garante_bloqueio_poltronas(params.trechoclasse_id, params.poltronas, passagens_cadastradas)
        return self.comprar_passagens(passagens_cadastradas, confirmar_venda_params)

    # função temporaria. apenas pra garantir que compra não quebrou
    def _garante_bloqueio_poltronas(self, trechoclasse_id, poltronas, passagens_cadastradas):
        # validacao temporaria do cache antigo
        if self.cache.get_poltrona_key_cache_list(trechoclasse_id, poltronas):
            return

        poltronas_nao_bloqueadas = [
            polt for polt in poltronas if not self.cache.get_poltrona_key_cache(trechoclasse_id, polt)
        ]
        if poltronas_nao_bloqueadas:
            # Apenas pra garantir que a transição não quebre emissões de passagens
            buserlogger.info(
                "entrou na compra praxio sem poltrona bloqueada. " "passagem_id=%s poltronas_nao_bloqueadas=%s",
                passagens_cadastradas[0].id,
                poltronas_nao_bloqueadas,
            )
            try:
                self.bloquear_poltronas(trechoclasse_id, poltronas)
            except Exception as ex:
                self._marca_passagens_com_erro(error_str(ex), passagens_cadastradas)
                raise ex

    def _get_trechos_compra(self, trechoclasse, provider_data):
        trechos = [
            TrechoCompra(
                desconto=trechoclasse.desconto or 0,
                preco=trechoclasse.preco_rodoviaria,
                id_rota=provider_data["IdRota"],
                pricing=provider_data.get("Pricing", D("0.00")),
                origem=trechoclasse.origem,
                destino=trechoclasse.destino,
            )
        ]

        conexoes = provider_data["conexoes"]
        if conexoes:
            if len(conexoes) != 1:
                raise RodoviariaException("Não é possível emitir passagens para viagens com mais de uma conexão.")
            conexao_external_id = conexoes[0]["CodigoOrigem"]

            local_embarque_conexao = LocalEmbarque.objects.get(
                cidade__company=self.company, id_external=conexao_external_id
            )

            trechos = [
                TrechoCompra(
                    desconto=provider_data["Desconto"],
                    preco=self._get_preco_perna(provider_data),
                    id_rota=provider_data["IdRota"],
                    pricing=provider_data["Pricing"],
                    origem=trechoclasse.origem,
                    destino=local_embarque_conexao,
                ),
                TrechoCompra(
                    desconto=provider_data["conexoes"][0]["Desconto"],
                    preco=self._get_preco_perna(provider_data["conexoes"][0]),
                    id_rota=provider_data["conexoes"][0]["IdRota"],
                    pricing=provider_data["conexoes"][0]["Pricing"],
                    origem=local_embarque_conexao,
                    destino=trechoclasse.destino,
                ),
            ]
        return trechos

    def _get_preco_perna(self, servico):
        return (
            D(str(servico["TxEmbarque"]))
            + D(str(servico["VlTarifa"]))
            + D(str(servico["Seguro"]))
            + D(str(servico["Pedagio"]))
            - D(str(servico["Desconto"]))
        )

    def comprar_passagens(
        self,
        passagens_cadastradas: list[Passagem],
        confirmar_venda_params,
    ):
        try:
            confirmar_venda_response = self._confirmar_venda(confirmar_venda_params)
        except RodoviariaCompraParcialPraxioException as ex:
            passagens_confirmadas = self._get_passagens_pra_devolucao_erro_compra(ex.json_response)
            self._marca_passagens_com_erro_e_solicita_cancelamento_passagens_confirmadas(
                error_str(ex), passagens_cadastradas, passagens_confirmadas
            )
            raise ex
        except Exception as ex:
            self._marca_passagens_com_erro(error_str(ex), passagens_cadastradas)
            raise ex
        return self._comprar_on_success(passagens_cadastradas, confirmar_venda_response)

    def _comprar_on_success(self, passagens_cadastradas, confirmar_venda_response):
        passagens_response, dict_provider_data = self._retorna_provider_data(confirmar_venda_response)
        bpe_response = self._get_bpe_response_if_exists(confirmar_venda_response)
        for idx in range(0, len(passagens_response)):
            # as passagens no banco de dados e as passagens confirmadas na API estão ligadas pela ordenação
            # uma FK não-praticante kkkrying
            # TODO: Ver se tem como mudar isso
            passagem = passagens_cadastradas[idx]
            passagem_response = passagens_response[idx]

            localizador = passagem_response.get("Localizador", None)
            passagem_form = None
            if localizador:
                passagem_form = models.PassagemVoucher.parse_obj(passagem_response)
                passagem.localizador = passagem_form.localizador
            else:
                passagem_form = models.PassagemBPE.parse_obj(passagem_response)
                if passagem_form.codigo_barras_curitiba:
                    passagem.numero_bilhete_embarque = passagem_form.codigo_barras_curitiba
                    passagem.tipo_taxa_embarque = Passagem.TipoTaxaEmbarque.QRCODE
                else:
                    passagem.numero_bilhete_embarque = passagem_form.numero_bilhete_embarque
                    passagem.tipo_taxa_embarque = passagem_form.tipo_taxa_embarque
                passagem.numero_bpe = passagem_form.numero_bpe
                try:
                    if bpe_response:
                        bpe_form = models.BPE.parse_obj(bpe_response[idx])
                        passagem.bpe_monitriip_code = bpe_form.monitriip
                        passagem.bpe_qrcode = bpe_form.bpe_qrcode
                        passagem.protocolo_autorizacao = bpe_form.protocolo_autorizacao
                        passagem.chave_bpe = bpe_form.chave_bpe
                        passagem.data_autorizacao = bpe_form.data_autorizacao
                except IndexError:
                    pass

            passagem.status = Passagem.Status.CONFIRMADA
            passagem.serie_bpe = passagem_form.serie_bpe
            passagem.numero_passagem = passagem_form.numero_passagem
            passagem.id_estabelecimento_external = passagem_form.id_estabelecimento
            passagem.prefixo = passagem_form.prefixo
            passagem.linha = passagem_form.linha
            passagem.plataforma = passagem_form.plataforma
            passagem.preco_base = passagem_form.preco_base
            passagem.embarque = passagem_form.embarque
            passagem.seguro = passagem_form.seguro
            passagem.pedagio = passagem_form.pedagio
            passagem.outras_taxas = passagem_form.outras_taxas
            passagem.provider_data = dict_provider_data[passagem_form.identificador_provider_data]

        updated_fields = [
            "status",
            "localizador",
            "numero_passagem",
            "preco_base",
            "taxa_embarque",
            "seguro",
            "pedagio",
            "outras_taxas",
            "preco_rodoviaria",
            "bpe_qrcode",
            "data_autorizacao",
            "protocolo_autorizacao",
            "chave_bpe",
            "bpe_monitriip_code",
            "plataforma",
            "numero_bpe",
            "serie_bpe",
            "id_estabelecimento_external",
            "prefixo",
            "linha",
            "numero_bilhete_embarque",
            "tipo_taxa_embarque",
            "provider_data",
            "updated_at",
        ]
        Passagem.objects.bulk_update(passagens_cadastradas, updated_fields)
        responses = [passagem.to_dict_json() for passagem in passagens_cadastradas]
        return {"passagens": responses}

    # TODO: jogar isso pro form
    def _get_bpe_response_if_exists(self, confirmar_venda_response):
        if "Bpe" not in confirmar_venda_response.get("oObj", {}):
            return None
        bpes = confirmar_venda_response.get("oObj", {}).get("Bpe", {})
        if not bpes:
            return None
        return [bpe.get("Result", [])[0] for bpe in bpes if bpe.get("Result")]

    def _marca_passagens_com_erro(self, error_msg, passagens_cadastradas):
        for passagem in passagens_cadastradas:
            passagem.erro = error_msg
            passagem.status = Passagem.Status.ERRO

        Passagem.objects.bulk_update(
            passagens_cadastradas,
            ["status", "erro", "numero_passagem", "serie_bpe", "id_estabelecimento_external", "updated_at"],
        )

    def _marca_passagens_com_erro_e_solicita_cancelamento_passagens_confirmadas(
        self, error_msg, passagens_cadastradas, passagens_confirmadas
    ):
        """
        É possível que uma passagem da reserva dê erro e outras sejam confirmadas.

        Caso a poltrona esteja dentro da lista passagens_confirmadas, salva como confirmada
        e marca pra cancelar mais abaixo no metodo `solicita_cancelamento_passagens`
        """
        for passagem in passagens_cadastradas:
            passagem_confirmada = passagens_confirmadas.get(str(passagem.poltrona_external_id))
            if passagem_confirmada:
                passagem.status = Passagem.Status.CONFIRMADA
                passagem.numero_passagem = passagem_confirmada["NumPassagem"]
                passagem.serie_bpe = passagem_confirmada["SerieBloco"]
                passagem.id_estabelecimento_external = passagem_confirmada["IDEstabelecimento"]
            else:
                passagem.erro = error_msg
                passagem.status = Passagem.Status.ERRO

        Passagem.objects.bulk_update(
            passagens_cadastradas,
            ["status", "erro", "numero_passagem", "serie_bpe", "id_estabelecimento_external", "updated_at"],
        )
        solicita_cancelamento_passagens([x for x in passagens_cadastradas if x.status == Passagem.Status.CONFIRMADA])

    def itinerario(self, external_id, datetime_ida=None):
        response_json = (
            endpoints.ListaPartidasTFOConfig(self.login)
            .invoke(get_http_executor(), json={"IdViagem": external_id})
            .json()
        )
        return RetornoItinerario(
            raw=response_json,
            cleaned=response_json["ListaPartidasTFO"],
            parsed=models.ListaPartidasTFO.parse_obj(response_json["ListaPartidasTFO"]),
        )

    def buscar_itinerario(self, params):
        return self.itinerario(params["id_viagem"])

    def buscar_corridas(self, request_params, match_params=None):
        buscar_corridas_return = self._buscar_corridas(request_params)
        corridas = buscar_corridas_return["ListaPartidas"]

        corridas_form = self._make_corridas_form(corridas)
        if not match_params:
            return corridas_form

        d_args = self._find_match_corridas(corridas, request_params, **match_params)
        return self._parse_retorno_buscar_servico(**d_args)

    def _make_corridas_form(self, corridas):
        found = bool(corridas)
        servicos = []

        for corrida in corridas:
            viagem_tfo = corrida["ViagemTFO"]

            distancia = viagem_tfo["DistanciaViagem"]
            capacidade_classe = viagem_tfo["QtdPoltronas"]
            datetime_chegada = f'{viagem_tfo["DtaHoraChegada"]}:00'

            existe_conexao = corrida.get("conexoes") and len(corrida.get("conexoes")) > 0
            if existe_conexao:
                ultima_conexao_tfo = self._get_conexao_valida(corrida)
                if not ultima_conexao_tfo:
                    # caso nao tenha conexao valida, trata como trecho nao encontrado
                    continue

                datetime_chegada = f'{ultima_conexao_tfo["DtaHoraChegada"]}:00'
                distancia = distancia + ultima_conexao_tfo["DistanciaViagem"]
                capacidade_classe = min(capacidade_classe, ultima_conexao_tfo["QtdPoltronas"])

            preco_rodoviaria = self._get_price(corrida, existe_conexao)

            servicos.append(
                ServicoForm(
                    linha=viagem_tfo["NomeLinha"],
                    external_datetime_ida=f'{viagem_tfo["DataHoraInicio"]}:00',
                    external_id=corrida["IdViagem"],
                    preco=preco_rodoviaria,
                    vagas=corrida["ViagemTFO"]["PoltronasDisponiveis"],
                    provider_data=corrida,
                    external_datetime_chegada=datetime_chegada,
                    classe=self._get_classe(viagem_tfo),
                    capacidade_classe=capacidade_classe,
                    distancia=distancia,
                    rota_external_id=corrida["IdViagem"],
                )
            )

        return BuscarServicoForm(found=found, servicos=servicos)

    def _find_match_corridas(self, corridas, request_params, datetime_ida, timezone, tipo_assento):
        matches = []
        mismatches = []
        d_args = {}
        buser_class = tipo_assento

        for servico in corridas:
            datetime_ida_servico = self._get_datetime_servico(
                timezone, servico["ViagemTFO"]["DataHoraInicio"], "%Y-%m-%dT%H:%M"
            )
            match_datetime_ida = self._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)

            api_class = self._get_classe(servico["ViagemTFO"]).lower()
            match_classe = self._does_class_match(buser_class, api_class)

            existe_conexao = servico.get("conexoes") and len(servico.get("conexoes")) > 0
            if existe_conexao:
                conexao_valida = self._get_conexao_valida(servico) is not None
            # caso exista conexao, mas não é uma conexao valida, da unmatch no trecho
            unmatch_por_conexao = existe_conexao and not conexao_valida

            if match_datetime_ida and match_classe and not unmatch_por_conexao:
                matches.append(servico)
            else:
                mismatches.append(servico)

        if len(matches) == 1:
            matched_service = matches[0]
            api_class = self._get_classe(matched_service["ViagemTFO"]).lower()
            if self._does_class_match(buser_class, api_class):
                d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
                return d_args

        if len(matches) > 1:
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(
                        datetime_ida,
                        timezone,
                        datetime.strptime(k["ViagemTFO"]["DataHoraInicio"], "%Y-%m-%dT%H:%M"),
                    ),
                    -k["ViagemTFO"]["PoltronasDisponiveis"],
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                api_class = self._get_classe(servico["ViagemTFO"]).lower()
                if self._does_class_match(buser_class, api_class):
                    matched_service = servico
                    break

            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        mismatches_classe_horario = [
            (
                self._get_classe(m["ViagemTFO"]).lower(),
                m["ViagemTFO"]["DataHoraInicio"],
            )
            for m in mismatches
        ]
        msg = (
            f"Unmatch de servico {self.company.name} com {request_params}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> {mismatches_classe_horario}"
        )
        buserlogger.info(msg)
        d_args.update({"timezone": timezone, "mismatches": mismatches})

        return d_args

    def _get_classe(self, viagem_tfo):
        tipo_horario = viagem_tfo.get("TipoHorario")
        if tipo_horario:
            return tipo_horario

        tipo_servico = viagem_tfo.get("TipoServico")
        if tipo_servico:
            return self.MAP_CLASSES_PRAXIO[int(tipo_servico)]

        # campos de classe são opcionais na Praxio.
        # usa a primeira opção do map caso estejam vazios (comportamento sugerido pela própria Praxio)
        return self.MAP_CLASSES_PRAXIO[0]

    def _get_conexao_valida(self, corrida):
        viagem_tfo = corrida["ViagemTFO"]

        # Só pega conexao se for apenas uma. Por ser um teste de conceito e ter complexidade reduzida
        apenas_uma_conexao = len(corrida.get("conexoes")) == 1
        if not apenas_uma_conexao:
            return

        ultima_conexao_tfo = corrida["conexoes"][-1]["viagemTFO"]
        mesmo_id_viagem = corrida["conexoes"][-1]["IdViagem"] == corrida["IdViagem"]
        mesma_classe = self._get_classe(ultima_conexao_tfo) == self._get_classe(viagem_tfo)
        sem_tempo_espera = ultima_conexao_tfo["DataHoraInicio"] == viagem_tfo["DtaHoraChegada"]

        # caso seja mesmo id viagem, mesma classe e tempo de espera 0, assumimos que a conexão é com o mesmo onibus
        if mesmo_id_viagem and mesma_classe and sem_tempo_espera:
            return ultima_conexao_tfo

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        provider_data = json.loads(trecho_classe.provider_data)
        tipo_assento_parceiro = self._get_classe(provider_data["ViagemTFO"])

        tipo_assento_buser = get_buser_class_by_company(self.company, tipo_assento_parceiro)
        andares = int(provider_data["Andares"])
        decks = []
        for andar in range(1, andares + 1):
            params = self._verifica_poltrona_params(
                VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1), andar
            )
            params["IdSessaoOp"] = self._id_sessao_op()
            params["VerificarSugestao"] = 1

            poltronas = self._retorna_poltronas_request(params)

            seats = []
            for poltrona in poltronas:
                if poltrona["Situacao"] == 3:
                    continue

                categoria = self._categoria_especial(poltrona["Situacao"])
                num_poltrona = int(poltrona["NumeroPoltrona"])
                row = ((num_poltrona - 1) // 4) + 1
                column = [1, 2, 4, 3][(num_poltrona - 1) % 4]
                # mantem o padrao de layout onde o X == 3 indica o corredor
                column = column + 1 if column > 2 else column
                seats.append(
                    {
                        "livre": self._poltrona_disponivel(poltrona["Situacao"], categoria),
                        "x": column,
                        "y": row,
                        "numero": int(poltrona["Caption"]),
                        "tipo_assento": tipo_assento_buser,
                        "categoria_especial": [categoria],
                    }
                )
            decks.append(Deck(andar=andar, assentos=parse_obj_as(list[Assento], seats)))
        return Onibus(layout=decks)

    def bloquear_poltronas(self, trechoclasse_id, poltronas):
        trechoclasse = self.get_active_trecho_classe(trechoclasse_id)
        provider_data = json.loads(trechoclasse.provider_data)
        trechos = self._get_trechos_compra(trechoclasse, provider_data)
        bloquear_poltrona_params = self._bloquear_poltronas_params(poltronas, trechoclasse, trechos)
        self._bloquear_poltronas_para_venda(bloquear_poltrona_params)

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        self.bloquear_poltronas(trechoclasse_id, [poltrona])
        # apenas pra conseguir diferenciar na compra que tem poltrona ja
        self.cache.set_poltrona_key_cache(trechoclasse_id, poltrona, True)
        return BloquearPoltronasResponse(
            seat=poltrona, best_before=now() + timedelta(minutes=20), external_payload=None
        )

    def desbloquear_poltronas(self, trecho_classe_id: int, poltronas: list[int]) -> dict:
        trechoclasse = self.get_active_trecho_classe(trecho_classe_id)

        poltronas_indisponiveis = self.cache.get_poltrona_indisponivel_cache(trecho_classe_id)
        id_viagem = int(trechoclasse.external_id)
        id_sessao = self._id_sessao_op()
        for poltrona in poltronas:
            params = {"IdViagem": id_viagem, "IdPoltrona": poltrona, "IdSessaoOp": id_sessao}
            json = models.DesbloquearPoltronaForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
            endpoints.DesmarcaPoltronaConfig(self.login).invoke(get_http_executor(), json=json)

            self.cache.delete_poltrona_key_cache(trecho_classe_id, poltrona)
            poltronas_indisponiveis.discard(poltrona)

        self.cache.set_poltrona_indisponivel_cache(trecho_classe_id, poltronas_indisponiveis)
        return {}

    @lock("praxio_bloqueia_poltrona_{verifica_poltronas_params.trechoclasse_id}")
    def verifica_poltrona(self, verifica_poltronas_params: VerificarPoltronaForm) -> list[int]:
        poltronas_map = self.get_map_poltronas(
            verifica_poltronas_params.trechoclasse_id, categoria_especial=verifica_poltronas_params.categoria_especial
        )
        vagas_disponiveis = len([poltrona for poltrona, situacao in poltronas_map.items() if situacao == "livre"])
        if vagas_disponiveis < verifica_poltronas_params.passageiros:
            raise RodoviariaOverbookingException(vagas_disponiveis)

        poltronas_to_exclude = self.cache.get_poltrona_indisponivel_cache(verifica_poltronas_params.trechoclasse_id)
        poltronas_selecionadas = poltronas_svc.seleciona_poltrona(
            poltronas_map, verifica_poltronas_params.passageiros, poltronas_to_exclude
        )

        poltronas_ocupadas = [poltrona for poltrona, situacao in poltronas_map.items() if situacao == "ocupada"]

        poltronas_bloqueadas = list(set(poltronas_to_exclude) | set(poltronas_selecionadas) - set(poltronas_ocupadas))
        poltronas_selecionadas = sorted([int(poltrona) for poltrona in poltronas_selecionadas])

        self.bloquear_poltronas(verifica_poltronas_params.trechoclasse_id, poltronas_selecionadas)

        [
            self.cache.set_poltrona_key_cache(verifica_poltronas_params.trechoclasse_id, polt, True)
            for polt in poltronas_selecionadas
        ]
        [
            self.cache.insert_poltrona_indisponivel_cache(verifica_poltronas_params.trechoclasse_id, poltrona)
            for poltrona in poltronas_bloqueadas
        ]

        return poltronas_selecionadas

    def _buscar_corridas(self, params):
        params = self._translate_params_buscar_servico(params)
        if self.company.company_external_id:
            params["IdEstabelecimento"] = self.company.company_external_id

        params["IdSessaoOp"] = self._id_sessao_op()
        params["IdEstabelecimentoVenda"] = self._id_estabelecimento()
        json = models.BuscarServicoForm.parse_obj(params).dict(by_alias=True, exclude_none=True)
        response = endpoints.BuscarServicoConfig(self.login).invoke(get_http_executor(), json=json).json()
        return response

    def _call_buscar_servicos_por_data(self, data_inicio, data_fim):
        if (data_fim - data_inicio).days > 30:
            raise ValueError("Período de busca não pode ser superior a 30 dias")

        response = endpoints.BuscarServicosConfig(self.login).invoke(
            get_http_executor(),
            json=endpoints.BuscarServicosConfig.build_json_params(data_inicio, data_fim),
        )
        return response.json()["oObj"]

    def buscar_servicos_por_data(self, data_inicio, data_fim):
        servicos = self._call_buscar_servicos_por_data(data_inicio, data_fim)
        if not servicos:
            return {}

        map_servicos_data = {}
        for serv in servicos:
            id_viagem = serv["IdViagem"]
            hora_viagem = serv["HoraViagem"]
            data_viagem = serv["DataViagem"]
            data_viagem = data_viagem[:11]
            datetime_ida = data_viagem + hora_viagem
            datetime_ida = datetime.strptime(datetime_ida, "%Y-%m-%dT%H%M")
            map_servicos_data[id_viagem] = max(datetime_ida, map_servicos_data.get(id_viagem, datetime.min))

        return map_servicos_data

    def rota_id_from_trecho_classe(self, trecho_classe):
        if not trecho_classe.provider_data:
            return None
        provider_data = json.loads(trecho_classe.provider_data)
        return provider_data.get("IdCodigoLinha")

    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object, modelo_venda):
        return descobrir_rotas_praxio_async_svc.descobrir_rotas(
            self.login,
            self.company.company_internal_id,
            next_days,
            shift_days,
            queue_name,
            return_task_object,
        )

    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        return descobrir_operacao.descobrir_operacao(self.login, next_days, shift_days, queue_name, return_task_object)

    def fetch_rotinas_empresa(self, next_days, first_day, queue_name):
        shift_days = 0
        if not first_day:
            first_day = today_midnight()
        else:
            first_day = datetime.fromisoformat(first_day) if isinstance(first_day, str) else first_day
            if first_day > today_midnight():
                shift_days = (midnight(first_day) - today_midnight()).days
        return descobrir_rotas_praxio_async_svc.descobrir_rotas(
            self.login,
            self.company.company_internal_id,
            next_days,
            shift_days,
            queue_name,
            False,
        )

    def _retorna_provider_data(self, confirmar_venda_response):
        dict_provider_data_por_identificador = self._normaliza_provider_data(confirmar_venda_response)
        passagens_response = self._get_list_passagem_outer_dict(confirmar_venda_response).get("ListaPassagem")

        return passagens_response, dict_provider_data_por_identificador

    # TODO: jogar isso pro form
    def _normaliza_provider_data(self, confirmar_venda_response):
        provider_data = copy.deepcopy(confirmar_venda_response)
        lista_passagens = self._get_list_passagem_outer_dict(provider_data).pop("ListaPassagem")
        venda_bpe = self.company.has_feature(Company.Feature.BPE)
        if venda_bpe:
            identificador = "NumPassagem"
        else:
            identificador = "Localizador"
        dict_provider_data_por_identificador = {}
        for passagem in lista_passagens:
            dict_provider_data_por_identificador[passagem[identificador]] = {
                "ListaPassagem": passagem,
                **provider_data,
            }
        return dict_provider_data_por_identificador

    def _get_list_passagem_outer_dict(self, confirmar_venda_response):
        if "ListaPassagem" in confirmar_venda_response:
            return confirmar_venda_response
        return confirmar_venda_response.get("oObj").get("Passagem")

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        json = {
            "IdSessaoOp": self._id_sessao_op(),
            "IdEstabelecimento": passagem.id_estabelecimento_external,
            "SerieBloco": passagem.serie_bpe,
            "NumPassagem": passagem.numero_passagem,
        }
        try:
            passagem = endpoints.ReimprimePassagemConfig(self.login).invoke(get_http_executor(), json=json).json()
        except RodoviariaBaseException:
            buserlogger.exception(
                "Praxio - Erro ao consultar reserva na atualização de passagens no staff"
                "com passagem.numero_passagem=%s",
                passagem.numero_passagem,
            )
            raise
        dados_gerais_passagem = passagem["PassRetorno"]

        bpe_id = None
        bpe_public_url = None
        birth = None
        if dados_gerais_passagem["DadosBilheteEmbarque"]:  # venda com BPE
            bpe_id = dados_gerais_passagem["ChaveBPe"]
            bpe_public_url = dados_gerais_passagem["UrlQrCodeBPe"]
            birth = datetime.strptime(
                dados_gerais_passagem["DadosBilheteEmbarque"]["DataNascimento"],
                "%Y-%m-%dT%H:%M:%S",
            ).strftime("%d/%m/%Y")

        data_hora_datetime = datetime.strptime(
            dados_gerais_passagem["DataPartida"][:10] + dados_gerais_passagem["HoraPartida"],
            "%Y-%m-%d%H%M",
        )
        data_hora_partida_str_formatada = data_hora_datetime.strftime("%d-%m-%Y %H:%M:%S")

        bilhete_padrao = RetornoConsultarBilheteForm.parse_obj(
            {
                "integracao": "Praxio",
                "numero_passagem": dados_gerais_passagem["NumPassagem"],
                "localizador": dados_gerais_passagem["Localizador"],
                "status": "",
                "numero_assento": dados_gerais_passagem["Poltrona"],
                "primeiro_nome_pax": dados_gerais_passagem["NomeCliente"],
                "tipo_documento": "CPF",
                "numero_documento": dados_gerais_passagem.get("CpfCliente"),
                "birth": birth,
                "bpe_id": bpe_id,
                "bpe_public_url": bpe_public_url,
                "data_partida": data_hora_partida_str_formatada,
                "origem": dados_gerais_passagem["Origem"],
                "estado_origem": dados_gerais_passagem["UfOrigem"],
                "destino": dados_gerais_passagem["Destino"],
                "estado_destino": dados_gerais_passagem["UfDestino"],
                "empresa_name": dados_gerais_passagem["DadosEstabEmissor"]["RazaoSocial"],
                "valor_passagem": dados_gerais_passagem["ValorPago"],
                "taxa_embarque": dados_gerais_passagem["TaxaEmbarque"],
            }
        )
        return bilhete_padrao

    def buscar_trechos_vendidos(self, id_viagem):
        response = endpoints.BuscarTrechosVendidosConfig(self.login).invoke(
            get_http_executor(), json={"IdViagem": id_viagem}
        )
        trechos_api = parse_obj_as(list[models.TrechoVendidoModel], response.json()["oObj"])
        return trechos_api

    def _get_tipo_passageiro_from_categoria_especial(
        self, categoria_especial: Passagem.CategoriaEspecial
    ) -> int | None:
        converter_dict = {
            Passagem.CategoriaEspecial.IDOSO_100: 4,
            Passagem.CategoriaEspecial.IDOSO_50: 5,
            Passagem.CategoriaEspecial.PCD: 6,
            Passagem.CategoriaEspecial.JOVEM_100: 11,
            Passagem.CategoriaEspecial.JOVEM_50: 10,
            Passagem.CategoriaEspecial.ESPACO_MULHER: 18,
            Passagem.CategoriaEspecial.ESPACO_PET: 13,
            Passagem.CategoriaEspecial.CRIANCA: 1,
        }
        return converter_dict.get(categoria_especial, None)

    def _get_valor_passagem_categoria_especial(
        self,
        trecho_classe: TrechoClasse,
        categoria_especial: Passagem.CategoriaEspecial,
        id_viagem,
        hora_partida,
        qtde_pax=1,
    ) -> D:
        tipo_passageiro = self._get_tipo_passageiro_from_categoria_especial(categoria_especial)
        response = endpoints.ValorTipoPassageiro(self.login).invoke(
            get_http_executor(),
            json={
                "IdViagem": id_viagem,
                "HoraPartida": hora_partida,
                "CodigoOrigem": trecho_classe.origem.id_external,
                "CodigoDestino": trecho_classe.destino.id_external,
                "TipoPassageiro": tipo_passageiro,
                "QtdPassageiros": qtde_pax,
            },
        )
        return D(str(response.json()["Valor"]))


def verify_praxio_login(nome, senha, cliente):
    resp = (
        endpoints.EfetuaLoginConfig(PraxioUndefinedCompanyClient(), with_auth=False)
        .invoke(
            get_http_executor(),
            json=endpoints.EfetuaLoginConfig.build_json_params(nome=nome, senha=senha, cliente=cliente),
        )
        .json()
    )
    return resp
