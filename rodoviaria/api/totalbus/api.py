import asyncio
import json
import logging
import warnings
from collections import defaultdict
from datetime import date, datetime, timedelta
from datetime import timezone as dttz
from decimal import Decimal
from json import loads
from random import randrange

from asgiref.sync import async_to_sync, sync_to_async
from celery import shared_task
from constance import config as constance_config
from django.utils import timezone
from pydantic import parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

import rodoviaria.api.totalbus.endpoints as endpoints
from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_tz
from commons.django_utils import error_str
from commons.memoize import memoize_with_log
from commons.redis import lock
from commons.utils import is_running_on_celery
from rodoviaria.api.executors.impl import get_async_http_executor, get_http_executor
from rodoviaria.api.forms import BuscarServicoForm, RetornoConsultarBilheteForm, RetornoItinerario, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.api.totalbus import models
from rodoviaria.api.totalbus.exceptions import (
    TempoCancelamentoExcedido,
    TotalbusCategoriaNotFound,
    TotalbusEmptyResponseError,
)
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.forms.staff_forms import TotalbusUndefinedCompanyClient
from rodoviaria.models import TotalbusLogin
from rodoviaria.models.core import Company, CompanyCategoriaEspecial, Passagem, TrechoClasse
from rodoviaria.models.totalbus import RotaTotalbus
from rodoviaria.service import (
    descobrir_rotas_totalbus_async_svc_v2,
    novos_modelos_svc,
    rotina_totalbus_async_svc,
)
from rodoviaria.service.class_match_svc import get_buser_class_by_company
from rodoviaria.service.exceptions import (
    PassengerNotRegistered,
    PoltronaJaSelecionadaException,
    PoltronaTrocadaException,
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaOverbookingException,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaUnauthorizedError,
)
from rodoviaria.service.rotina_totalbus_svc import RotinaTotalbus
from rodoviaria.service.solicita_cancelamento_svc import solicita_cancelamento_passagens

from . import memcache as totalbus_mc

buserlogger = logging.getLogger(__name__)


class TotalbusAPI(RodoviariaAPI):
    Rota = RotaTotalbus
    divergencia_maxima_pct = 50

    def __init__(self, company: Company):
        super().__init__(company)
        self.login = TotalbusLogin.objects.select_related("company", "company__integracao").get(company=company)
        self.cache = totalbus_mc.TotalbusMC(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    def bloquear_poltronas(
        self,
        trecho_classe_internal_id: int,
        poltronas: list[int],
        categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL,
    ) -> None:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_internal_id)
        categoria_reservada_id = self._get_categoria_reservada_id(categoria_especial)

        if trecho_classe.conexao:
            return self._bloquear_poltronas_conexao(
                trecho_classe.conexao, trecho_classe_internal_id, poltronas, categoria_reservada_id
            )
        else:
            return self._bloquear_trecho_unico(
                trecho_classe, trecho_classe_internal_id, poltronas, categoria_reservada_id
            )

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        bloqueio_poltronas = self.bloquear_poltronas(trechoclasse_id, [poltrona], categoria_especial)
        cache_payload = self.cache.get_poltrona_key_cache(trechoclasse_id, poltrona)
        return BloquearPoltronasResponse(
            seat=poltrona,
            best_before=timezone.now() + timedelta(minutes=self._get_tempo_limite_bloqueio(bloqueio_poltronas)),
            external_payload=cache_payload,
        )

    def _get_tempo_limite_bloqueio(self, bloqueio_poltronas):
        responses = []

        if isinstance(bloqueio_poltronas, tuple):
            # Conexão: cada item é um dicionário de bloqueios
            for bloqueio in bloqueio_poltronas:
                responses.extend(bloqueio.values())
        else:
            # Viagem direta
            responses.extend(bloqueio_poltronas.values())

        if not responses:
            return 10

        return min((r.duracao for r in responses), default=10)

    def _bloquear_trecho_unico(
        self,
        trecho_classe: TrechoClasse,
        trecho_classe_internal_id: int,
        poltronas: list[int],
        categoria_reservada_id: str,
    ) -> dict[int, models.BloquearPoltronaRequest]:
        servico_params = models.ServicoTrecho.from_trechoclasse(trecho_classe)
        fn_bloquear_poltrona = self._bloquear_poltronas_sync
        if constance_config.USE_HTTP_ASYNC is True and not is_running_on_celery():
            fn_bloquear_poltrona = self._bloquear_poltronas_async
        poltronas_bloqueadas = fn_bloquear_poltrona(
            servico_params, trecho_classe_internal_id, poltronas, categoria_reservada_id
        )
        self._create_cache_poltronas_bloqueadas(trecho_classe_internal_id, poltronas_bloqueadas)
        return poltronas_bloqueadas

    def _create_cache_poltronas_bloqueadas(
        self, trecho_classe_internal_id: int, poltronas_bloqueadas: dict[str, models.BloquearPoltronaRequest]
    ):
        for key, trecho_params in poltronas_bloqueadas.items():
            cache_params = self._extrai_infos_cacheaveis(trecho_params)
            self.cache.set_poltrona_key_cache(
                trecho_classe_internal_id, key, [cache_params], trecho_params.duracao * 60
            )

    def _bloquear_poltronas_sync(
        self,
        servico_trecho: models.ServicoTrecho,
        trecho_classe_internal_id: int,
        poltronas: list[int],
        categoria_reservada_id: str,
    ) -> dict[int, models.BloquearPoltronaRequest]:
        poltronas_bloqueadas = {}
        try:
            for poltrona in poltronas:
                poltrona_bloqueada = self.bloquear_poltrona_request_sync(
                    trecho_classe_internal_id,
                    servico_trecho,
                    poltrona=poltrona,
                    categoria_reservada_id=categoria_reservada_id,
                )
                poltronas_bloqueadas[poltrona] = poltrona_bloqueada
            return poltronas_bloqueadas
        except RodoviariaException:
            poltronas = list(poltronas_bloqueadas.keys())
            self.envia_poltronas_fila_desbloqueio(trecho_classe_internal_id, poltronas)
            raise

    @async_to_sync
    async def _bloquear_poltronas_async(
        self,
        servico_trecho: models.ServicoTrecho,
        trecho_classe_internal_id: int,
        poltronas: list[int],
        categoria_reservada_id: str,
    ) -> dict[int, models.BloquearPoltronaRequest]:
        tasks = [
            asyncio.create_task(
                self.bloquear_poltrona_request_async(
                    trecho_classe_internal_id,
                    servico_trecho,
                    poltrona=poltrona,
                    categoria_reservada_id=categoria_reservada_id,
                ),
                name=poltrona,
            )
            for poltrona in poltronas
        ]

        (done, _) = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
        try:
            success = {poltrona: task.result() for task, poltrona in zip(done, poltronas)}
            return success
        except RodoviariaException as exc:
            failed = [poltrona for task, poltrona in zip(done, poltronas) if task.exception() is None]
            await sync_to_async(self.envia_poltronas_fila_desbloqueio)(trecho_classe_internal_id, failed)
            raise exc
        finally:
            blocked = [poltrona for task, poltrona in zip(done, poltronas) if task.exception() is not None]
            for poltrona in blocked:
                self.cache.insert_poltrona_indisponivel_cache(trecho_classe_internal_id, poltrona)

    def _extrai_infos_cacheaveis(
        self, response: models.BloquearPoltronaRequest, preco_conexao: str | None = None, is_conexao: bool = False
    ) -> models.InfosCacheaveisBloqueioPoltrona:
        preco_rodoviaria = self._get_preco_passagem_rodoviaria(response)
        return models.InfosCacheaveisBloqueioPoltrona(
            transacao=response.transacao,
            preco=preco_rodoviaria,
            preco_conexao=preco_conexao,
            origem=response.origem.cidade,
            destino=response.destino.cidade,
            data_hora_partida=response.data_saida.strftime("%Y-%m-%d %H:%M"),
            is_conexao=is_conexao,
        )

    def _get_preco_passagem_rodoviaria(self, response: models.BloquearPoltronaRequest) -> Decimal:
        tarifa_com_pricing = response.preco.tarifa_com_pricing
        outras_tarifas = response.preco.outros
        pedagio = response.preco.pedagio
        seguro = response.preco.seguro
        taxa_embarque = response.preco.taxa_embarque
        preco_rodoviaria = tarifa_com_pricing + outras_tarifas + pedagio + seguro + taxa_embarque
        return preco_rodoviaria

    def _bloquear_poltronas_conexao(
        self,
        conexao: models.ConexaoModel,
        trecho_classe_internal_id: int,
        poltronas_primeira_perna: list[int],
        categoria_reservada_id: str,
    ) -> tuple[dict[int, models.BloquearPoltronaRequest], dict[int, models.BloquearPoltronaRequest]]:
        fn_bloquear_poltronas = self._bloquear_poltronas_sync
        if constance_config.USE_HTTP_ASYNC is True and not is_running_on_celery():
            fn_bloquear_poltronas = self._bloquear_poltronas_async
        conexao = models.ConexaoModel.parse_obj(conexao)
        primeira_perna, segunda_perna = self._get_pernas_conexao(conexao)
        poltronas_bloqueadas_primeira_perna = None
        try:
            poltronas_bloqueadas_primeira_perna = fn_bloquear_poltronas(
                primeira_perna, trecho_classe_internal_id, poltronas_primeira_perna, categoria_reservada_id
            )
            poltronas_segunda_perna = self._get_poltronas_segunda_perna(
                segunda_perna, poltronas_bloqueadas_primeira_perna
            )
            poltronas_bloqueadas_segunda_perna = fn_bloquear_poltronas(
                segunda_perna, trecho_classe_internal_id, poltronas_segunda_perna, categoria_reservada_id
            )

            self._set_cache_poltronas_conexao(
                trecho_classe_internal_id,
                primeira_perna.preco_conexao,
                segunda_perna.preco_conexao,
                poltronas_bloqueadas_primeira_perna,
                poltronas_bloqueadas_segunda_perna,
            )
            return poltronas_bloqueadas_primeira_perna, poltronas_bloqueadas_segunda_perna
        except RodoviariaException as exc:
            # se falhar o bloqueio da segunda perna, libera as poltronas da primeira perna.
            if poltronas_bloqueadas_primeira_perna:
                self.envia_poltronas_fila_desbloqueio(
                    trecho_classe_internal_id, list(poltronas_bloqueadas_primeira_perna.keys())
                )
            raise exc

    def _set_cache_poltronas_conexao(
        self,
        trecho_classe_internal_id: int,
        primeira_perna_preco_conexao: Decimal,
        segunda_perna_preco_conexao: Decimal,
        poltronas_bloqueadas_primeira_perna: dict,
        poltronas_bloqueadas_segunda_perna: dict,
    ):
        # mantem o comportamento atual, mesmo que não seja o ideal.
        for (poltrona, perna1_params), perna2_params in zip(
            poltronas_bloqueadas_primeira_perna.items(), poltronas_bloqueadas_segunda_perna.values()
        ):
            cache_params = [
                self._extrai_infos_cacheaveis(
                    perna1_params, preco_conexao=primeira_perna_preco_conexao, is_conexao=True
                ),
                self._extrai_infos_cacheaveis(
                    perna2_params, preco_conexao=segunda_perna_preco_conexao, is_conexao=True
                ),
            ]
            self.cache.set_poltrona_key_cache(trecho_classe_internal_id, poltrona, cache_params)

    def _get_poltronas_segunda_perna(self, segunda_perna, poltronas_bloqueadas_primeira_perna):
        # TODO: tech-debt conexao não implementa gratuidade
        map_poltronas_segunda_perna = self._map_poltronas_from_api(segunda_perna, Passagem.CategoriaEspecial.NORMAL)
        # padroniza poltrona como string com dois digitos, p.e. "01"
        poltronas_primeira_perna: list[str] = [
            str(poltrona).zfill(2) for poltrona in poltronas_bloqueadas_primeira_perna
        ]
        poltronas_segunda_perna_livres = [
            poltrona for poltrona in map_poltronas_segunda_perna if map_poltronas_segunda_perna[poltrona] == "livre"
        ]
        poltronas_segunda_perna = self._get_poltronas_em_comum(poltronas_primeira_perna, poltronas_segunda_perna_livres)
        return poltronas_segunda_perna

    def _get_poltronas_em_comum(
        self, poltronas_primeira_perna: list[int], poltronas_segunda_perna: list[int]
    ) -> list[int]:
        # Encontrar poltronas comuns entre primeira e segunda perna
        common_items = [item for item in poltronas_primeira_perna if item in poltronas_segunda_perna]

        # Adicionar poltronas comuns à lista final
        final_list = common_items.copy()

        # Adicionar poltronas da segunda perna até que a lista final tenha o mesmo tamanho da primeira perna
        for item in poltronas_segunda_perna:
            if len(final_list) < len(poltronas_primeira_perna):
                if item not in final_list:
                    final_list.append(item)
            else:
                break

        if len(final_list) < len(poltronas_primeira_perna):
            raise RodoviariaOverbookingException(vagas_disponiveis=len(final_list))
        return final_list

    def _get_pernas_conexao(self, conexao: models.ConexaoModel) -> tuple[models.ServicoTrecho, models.ServicoTrecho]:
        primeira_perna = models.ServicoTrecho(
            origem=conexao.primeiro_trecho_origem,
            destino=conexao.localidade_conexao_id,
            data=conexao.primeiro_trecho_data_corrida,
            servico=conexao.primeiro_trecho_servico,
            preco_conexao=conexao.primeiro_trecho_preco,
            sequencia=conexao.primeiro_trecho_sequencia,
            conexao_id=conexao.conexion_ctrl_id,
            conexao_grupo_id=conexao.conexion_grupo,
        )
        segunda_perna = models.ServicoTrecho(
            origem=conexao.localidade_conexao_id,
            destino=conexao.segundo_trecho_destino,
            data=conexao.segundo_trecho_data_corrida,
            servico=conexao.servico_conexao,
            preco_conexao=conexao.segundo_trecho_preco,
            sequencia=conexao.primeiro_trecho_sequencia,
            conexao_id=conexao.conexion_ctrl_id,
            conexao_grupo_id=conexao.conexion_grupo,
        )
        return (primeira_perna, segunda_perna)

    async def bloquear_poltrona_request_async(
        self,
        trecho_classe_internal_id: int,
        servico_trecho: models.ServicoTrecho,
        poltrona: int,
        categoria_reservada_id: str,
    ) -> models.BloquearPoltronaRequest:
        try:
            params = servico_trecho.dict(by_alias=True, exclude_unset=True)
            executor = get_async_http_executor()
            request_config = endpoints.BloquearPoltronaVendaNormalConfig(self.login)
            json_params = {**params, "poltrona": poltrona}
            if categoria_reservada_id != "-1":
                json_params["categoriaId"] = categoria_reservada_id
            response = await request_config.ainvoke(executor, json=json_params)
            response_obj = models.BloquearPoltronaRequest.parse_obj(response.json())
            return response_obj
        except PoltronaJaSelecionadaException:
            raise

    def bloquear_poltrona_request_sync(
        self,
        trecho_classe_internal_id: int,
        servico_trecho: models.ServicoTrecho,
        poltrona: int,
        categoria_reservada_id: str,
    ) -> models.BloquearPoltronaRequest:
        try:
            params = servico_trecho.dict(by_alias=True, exclude_unset=True)
            executor = get_http_executor()
            request_config = endpoints.BloquearPoltronaVendaNormalConfig(self.login)
            json_params = {**params, "poltrona": poltrona}
            if categoria_reservada_id != "-1":
                json_params["categoriaId"] = categoria_reservada_id
            response = request_config.invoke(executor, json=json_params)
            response_obj = models.BloquearPoltronaRequest.parse_obj(response.json())
            return response_obj
        except PoltronaJaSelecionadaException:
            raise
        finally:
            self.cache.insert_poltrona_indisponivel_cache(trecho_classe_internal_id, poltrona)

    def desbloquear_poltronas(self, trecho_classe_id: int, poltronas: list[int]) -> dict:
        poltronas_indisponiveis = self.cache.get_poltrona_indisponivel_cache(trecho_classe_id)
        for poltrona in poltronas:
            bloqueios = self.cache.get_poltrona_key_cache(trecho_classe_id, poltrona)
            if bloqueios is not None:
                for bloqueio in bloqueios:
                    self.desbloquear_poltrona(bloqueio)
            self.cache.delete_poltrona_key_cache(trecho_classe_id, poltrona)
            poltronas_indisponiveis.discard(poltrona)
        self.cache.set_poltrona_indisponivel_cache(trecho_classe_id, poltronas_indisponiveis)
        return {}

    def desbloquear_poltrona(self, bloqueio: models.InfosCacheaveisBloqueioPoltrona) -> dict:
        json_params = {"transacao": bloqueio.transacao}
        executor = get_http_executor()
        request_config = endpoints.DesbloquearPoltronasConfig(self.login)
        response = request_config.invoke(executor, json=json_params)
        return response.json()

    @retry(
        retry=retry_if_exception_type(TotalbusCategoriaNotFound),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def confirmar_venda(self, params: models.ConfirmarVendaForm) -> dict:
        executor = get_http_executor()

        request_config = endpoints.ConfirmarVendaRequestConfig(self.login)
        response = request_config.invoke(executor, json=params.dict(by_alias=True, exclude_none=True))
        return response.json()

    @retry(
        retry=retry_if_exception_type(TotalbusCategoriaNotFound),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    async def confirmar_venda_async(self, params: models.ConfirmarVendaForm) -> dict:
        executor = get_async_http_executor()

        request_config = endpoints.ConfirmarVendaRequestConfig(self.login)
        response = await request_config.ainvoke(executor, json=params.dict(by_alias=True, exclude_none=True))
        return response.json()

    def cancelar_venda(self, params: dict, validar_multa: bool = False) -> dict:
        params["validarMulta"] = validar_multa
        json_params = models.CancelarVendaForm.parse_obj(params).dict(by_alias=True)

        try:
            executor = get_http_executor()
            request_config = endpoints.CancelarVendaConfig(self.login)
            response = request_config.invoke(executor, json=json_params)
            return response.json()
        except (PassengerNotRegistered, PoltronaTrocadaException):
            return self._cancelar_venda_por_ultimo_bilhete_ativo(params)
        except TempoCancelamentoExcedido:
            if not validar_multa and self.login.validar_multa:
                return self.cancelar_venda(params, validar_multa=True)
            raise

    def _cancelar_venda_por_ultimo_bilhete_ativo(self, params: dict) -> dict:
        bilhetes = self.buscar_bilhetes(params)
        bilhetes_ativos = list(filter(lambda x: not x["cancelado"], bilhetes))
        if len(bilhetes_ativos) == 0:
            # se já está cancelada no buser_django e na API, deixa prosseguir
            if params["pax_valido"] is False:
                return {}
            raise PassengerNotRegistered("O bilhete já foi cancelado")
        return self.cancelar_venda_por_bilhete({"numeroBilhete": bilhetes_ativos[0]["numeroBilhete"]})

    def cancelar_venda_por_bilhete(self, params: dict, validar_multa: bool = False) -> dict:
        params["validarMulta"] = validar_multa
        json_params = models.CancelarVendaPorBilheteForm.parse_obj(params).dict(by_alias=True)
        try:
            executor = get_http_executor()

            request_config = endpoints.CancelarVendaPorBilheteRequestConfig(self.login)
            response = request_config.invoke(executor, json=json_params)
            return response.json()
        except TempoCancelamentoExcedido:
            if not validar_multa and self.login.validar_multa:
                return self.cancelar_venda_por_bilhete(params, validar_multa=True)
            raise

    def retorna_poltronas_request(self, params: dict) -> list[models.MapaPoltronaItem]:
        json_params = models.RetornaPoltronasModel.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        executor = get_http_executor()
        request_config = endpoints.RetornaPoltronasConfig(self.login)
        response = request_config.invoke(executor, json=json_params)
        mapa_poltronas_raw = response.json()["mapaPoltrona"]
        return parse_obj_as(list[models.MapaPoltronaItem], mapa_poltronas_raw)

    @retry(
        retry=retry_if_exception_type(RodoviariaConnectionError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def consultar_categoria_corrida_request(self, params: dict) -> list[models.AssentoCategoriaEspecial]:
        json_params = models.ServicoTrecho.parse_obj(params).dict(exclude_none=True, exclude_unset=True)
        executor = get_http_executor()
        request_config = endpoints.ConsultarCategoriaCorridaRequestConfig(self.login)
        response = request_config.invoke(executor, json=json_params)
        return parse_obj_as(list[models.AssentoCategoriaEspecial], response.json())

    def cidades_destino(self, origem_external_id):
        destinos = self._buscar_destinos(origem_external_id)
        destinos = parse_obj_as(list[models.Localidade], destinos)
        return [destino.external_local_id for destino in destinos]

    @memoize_with_log(48 * 60 * 60)
    def _buscar_destinos(self, origem_external_id):
        executor = get_http_executor()
        request_config = endpoints.BuscarDestinosConfig(self.login, origem_external_id=origem_external_id)
        response = request_config.invoke(executor)
        destinos = response.json()
        return destinos

    def map_cidades_destinos(self):
        if self.company.company_external_id:
            empresas_external_ids = [self.company.company_external_id]
        else:
            empresas_external_ids = [e.external_id for e in self.buscar_empresas()]
        origens_destinos_map = defaultdict(set)
        for empresa_external_id in empresas_external_ids:
            origens_destinos = self._buscar_origens_e_destinos(empresa_external_id)
            origens_destinos = parse_obj_as(list[models.BuscaOrigensDestinosModel], origens_destinos)
            for od in origens_destinos:
                origens_destinos_map[od.origem.external_local_id].update([d.external_local_id for d in od.destinos])
        return dict(origens_destinos_map)

    def _buscar_origens_e_destinos(self, empresa_external_id):
        executor = get_http_executor()
        request_config = endpoints.BuscarOrigensDestinosConfig(self.login, company_external_id=empresa_external_id)
        response = request_config.invoke(executor)
        return response.json()

    def atualiza_origens(self) -> list[models.Localidade]:
        executor = get_http_executor()
        request_config = endpoints.BuscaOrigensConfig(self.login)
        response = request_config.invoke(executor)
        return parse_obj_as(list[models.Localidade], response.json())

    def buscar_empresas(self):
        empresas = self._buscar_empresas()
        empresas = parse_obj_as(list[models.ExternalCompany], empresas)
        return empresas

    @memoize_with_log(timeout=24 * 60 * 60)
    def _buscar_empresas(self):
        executor = get_http_executor()
        request_config = endpoints.ConsultarEmpresasConfig(self.login)
        response = request_config.invoke(executor)
        response_json = response.json()
        if {"id": -1, "nome": "TODAS"} in response_json:
            response_json.remove({"id": -1, "nome": "TODAS"})
        return response_json

    def itinerario(self, external_id, datetime_ida):
        return self._buscar_itinerario(external_id, datetime_ida)

    def buscar_itinerario(self, params):
        return self._buscar_itinerario(params["servico"], params["data"])

    def _buscar_itinerario(self, servico, data_ida):
        executor = get_http_executor()
        request_config = endpoints.BuscarItinerarioCorridaRequestConfig(self.login)
        response = request_config.invoke(
            executor,
            json={
                "servico": servico,
                "data": data_ida,
            },
        )
        response_json = response.json()
        return RetornoItinerario(
            raw=response_json,
            cleaned=response_json["lsParadas"],
            parsed=parse_obj_as(models.Itinerario, response.json()["lsParadas"]),
        )

    @retry(
        retry=retry_if_exception_type(TotalbusEmptyResponseError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def buscar_viagens_por_periodo(
        self, datetime_inicial: date, datetime_final: date, company_external_id: int | None = None
    ) -> list[models.PadraoBuscarServicoOutput]:
        """Retorna todos as corridas disponíveis na api para o período informado."""
        company_external_id = company_external_id or self.company.company_external_id
        if not company_external_id:
            raise ValueError(
                "Função deve receber company_external_id como argumento "
                "ou empresa deve ter company_external_id cadastrado"
            )
        params = models.PadraoBuscarServicoInput(
            data_inicial=datetime_inicial, data_final=datetime_final, company_external_id=company_external_id
        )
        executor = get_http_executor()
        request_config = endpoints.BuscarServicosPeriodoConfig(self.login)
        response = request_config.invoke(executor, json=params.dict())
        return parse_obj_as(list[models.PadraoBuscarServicoOutput], response.json())

    def buscar_bilhetes(self, params: dict) -> dict:
        json_params = models.BuscarBilhetePorNumeroSistemaForm.parse_obj(params).dict(by_alias=True, exclude_unset=True)
        executor = get_http_executor()

        request_config = endpoints.BuscarBilheteRequestConfig(self.login)
        response = request_config.invoke(executor, json=json_params)
        return response.json()

    def get_map_poltronas(
        self, trecho_classe_id: int, categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL
    ) -> dict:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        params = self._verifica_poltrona_params(trecho_classe)
        resp = self._map_poltronas_from_api(params, categoria_especial)
        # Muda para ocupado o status das poltronas já vendidas pela Buser
        resp = self._muda_para_ocupado_poltronas_from_db(trecho_classe_id, resp)
        # Muda para ocupado o status das poltronas que deram erro ao tentar bloquear
        resp = self._muda_para_ocupado_poltronas_from_cache(trecho_classe_id, resp)
        return resp

    def _muda_para_ocupado_poltronas_from_cache(self, trechoclasse_id: int, poltronas_map: dict) -> dict:
        poltronas_cache = self.cache.get_poltrona_indisponivel_cache(trechoclasse_id)
        if poltronas_cache:
            for poltrona in poltronas_cache:
                p = str(poltrona).zfill(2)
                if poltronas_map.get(p) == "livre":
                    poltronas_map[p] = "ocupada"
        return poltronas_map

    def _map_poltronas_from_api(self, params: dict, categoria_especial: Passagem.CategoriaEspecial) -> dict:
        """faz um mapa de poltronas da api no formato

        {
            numero_poltrona: 'livre' ou 'ocupada'
        }

        O atributo `categoria_especial` filtra as poltronas de acordo com a categoria desejada:

        == "normal": retorna todas as poltronas com a categoria normal, removendo as categorias especiais

        != "normal": retorna todas as poltronas normais e da categoria desejada

        """

        poltronas = self.retorna_poltronas_request(params)
        resp = {}
        categoria_reservada_id = self._get_categoria_reservada_id(categoria_especial)
        categoria_todos = "-1"
        for poltrona in poltronas:
            categoria_poltrona = poltrona.categoria_reservada_id
            poltrona_eh_todos = categoria_poltrona == categoria_todos
            poltrona_eh_categoria_buscada = categoria_poltrona == categoria_reservada_id
            if (
                poltrona_eh_todos
                and not novos_modelos_svc.is_compra_antecipada_poltronas(self.company.company_internal_id)
            ) or poltrona_eh_categoria_buscada:
                status = "livre" if poltrona.disponivel else "ocupada"
                resp[poltrona.numero] = status

        return resp

    def _get_categoria_reservada_id(self, categoria_especial):
        try:
            categoria_reservada_id = CompanyCategoriaEspecial.objects.get(
                company_id=self.company_id, categoria_especial=categoria_especial
            ).categoria_id_external
        except CompanyCategoriaEspecial.DoesNotExist:
            categoria_reservada_id = "-1"
        return categoria_reservada_id

    def get_qtd_poltronas_livres(self, mapa_poltronas: dict) -> int:
        return sum(status == "livre" for status in list(mapa_poltronas.values()))

    def rotate_poltronas(self, poltronas):
        poltronas = sorted(poltronas.items())
        # em viagens híbrido, para otimizar a emissão das passagens o ideal é que selecionemos
        # sempre a primeira poltrona, para evitar selecionar poltronas que ocupem a viagem inteira
        # exemplo: SAO -> SJK -> TAU
        # selecionando a primeira poltrona sempre, garantimos que se tiver duas compras, uma no trecho
        # SAO -> SJK e uma no trecho SJK -> TAU, ambas vão estar na mesma poltrona
        if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
            return dict(poltronas)
        n_rotate = randrange(len(poltronas))  # noqa: S311
        poltronas_rotated = poltronas[n_rotate:] + poltronas[:n_rotate]
        return dict(poltronas_rotated)

    def get_poltronas_livres(
        self, trecho_classe_id: int, numero_poltronas: int, categoria_especial: Passagem.CategoriaEspecial
    ) -> list[int]:
        map_poltronas = self.get_map_poltronas(trecho_classe_id, categoria_especial)
        vagas_disponiveis = self.get_qtd_poltronas_livres(map_poltronas)
        if vagas_disponiveis < numero_poltronas:
            raise RodoviariaOverbookingException(vagas_disponiveis)
        map_poltronas = self._sanitize_poltronas(map_poltronas)
        map_poltronas = self.rotate_poltronas(map_poltronas)
        set_poltronas = set()
        first_seat = False
        poltronas_separadas = []
        for p in map_poltronas:
            disponivel = map_poltronas[p] == "livre"
            poltronas_restantes = numero_poltronas - len(set_poltronas)
            if not first_seat and poltronas_restantes > 1 and disponivel:
                first_seat = p
                continue
            if poltronas_restantes == 1 and disponivel:
                set_poltronas.add(p)
                break
            if first_seat and disponivel:
                set_poltronas.add(first_seat)
                set_poltronas.add(p)
                first_seat = False
                if len(set_poltronas) >= numero_poltronas:
                    break
            if first_seat and not disponivel:
                poltronas_separadas.append(first_seat)
                first_seat = False
                continue
        if first_seat and len(set_poltronas) < numero_poltronas:
            set_poltronas.add(first_seat)
        while len(set_poltronas) < numero_poltronas and poltronas_separadas:
            set_poltronas.add(poltronas_separadas.pop(0))
        return list(set_poltronas)

    def _sanitize_poltronas(self, poltronas):
        poltronas_sanitized = {}
        for poltrona, status in poltronas.items():
            try:
                poltrona = int(poltrona)
            except ValueError:
                poltrona = 999
            poltronas_sanitized[poltrona] = status
        return poltronas_sanitized

    def _sanitize_tipo_documento(self, tipo_documento):
        if tipo_documento and tipo_documento.lower() in ["rg", "cpf", "passaporte"]:
            return tipo_documento
        return "RG"

    def _reserva_dict_to_comprar_params(self, trechoclasse: TrechoClasse, params: ComprarForm):
        trechoclasse_id = params.trechoclasse_id
        travel_id = params.travel_id
        origem_tz = trechoclasse.origem.cidade.timezone if trechoclasse.origem.cidade.timezone else "America/Sao_Paulo"
        datetime_partida = trechoclasse.datetime_ida.astimezone(ZoneInfo(origem_tz)).strftime("%Y-%m-%d")
        poltronas_livres = params.poltronas
        valor_cheio = params.valor_cheio
        forms = []
        passagens = []
        categoria_reservada_id = self._get_categoria_reservada_id(params.categoria_especial)

        for passageiro in params.passageiros:
            id_poltrona = poltronas_livres.pop(0)
            if params.extra_poltronas:
                bloqueios = parse_obj_as(list[models.InfosCacheaveisBloqueioPoltrona], params.extra_poltronas)
            else:
                bloqueios = self._get_poltrona_bloqueada_key_and_price(
                    trechoclasse_id, id_poltrona, categoria_reservada_id
                )
            for bloqueio in bloqueios:
                preco_rodoviaria = bloqueio.preco
                seat_key = bloqueio.transacao
                origem_cidade_name = bloqueio.origem
                destino_cidade_name = bloqueio.destino
                data_hora_partida = bloqueio.data_hora_partida
                buserlogger.info(
                    "Marketplace - Totalbus: viagem de service %s ",
                    trechoclasse.external_id,
                    "no dia %s no host %s: a poltrona de ",
                    datetime_partida,
                    self.base_url,
                    "numero %s sera reservada",
                    id_poltrona,
                )
                confirmar_venda_form = models.ConfirmarVendaForm(
                    nomePassageiro=passageiro.name[:60],
                    documentoPassageiro=self.rg_or_cpf(passageiro)[:20],
                    tipoDocumentoPassageiro=self._sanitize_tipo_documento(passageiro.tipo_documento),
                    telefone=passageiro.phone,
                    idFormaPagamento=self.login.id_forma_pagamento,
                    formaPagamento=self.login.forma_pagamento,
                    transacao=seat_key,
                )
                if categoria_reservada_id != "-1":
                    confirmar_venda_form.categoria_especial = categoria_reservada_id
                if passageiro.birthday:
                    confirmar_venda_form.data_nascimento = passageiro.birthday.strftime("%d/%m/%Y")
                desconto = Decimal("0")
                if bloqueio.is_conexao:
                    desconto = bloqueio.preco - bloqueio.preco_conexao
                    confirmar_venda_form.valor_desconto = desconto
                    preco_rodoviaria = bloqueio.preco_conexao
                    log_extra = {
                        "trechoclasse_id": trechoclasse_id,
                        "travel_id": travel_id,
                        "valor_cheio": valor_cheio,
                        "desconto": desconto,
                        "preco_rodoviaria": preco_rodoviaria,
                        "preco_conexao": bloqueio.preco_conexao,
                    }
                    buserlogger.info("totalbus_compra_conexao", extra=log_extra)
                passagem = Passagem(
                    trechoclasse_integracao=trechoclasse,
                    company_integracao=self.company,
                    poltrona_external_id=id_poltrona,
                    buseiro_internal_id=passageiro.id,
                    pedido_external_id=seat_key,
                    travel_internal_id=travel_id,
                    valor_cheio=valor_cheio,
                    preco_rodoviaria=preco_rodoviaria,
                    desconto=desconto,
                    status=Passagem.Status.INCOMPLETA,
                    origem=origem_cidade_name,
                    destino=destino_cidade_name,
                    data_hora_partida=data_hora_partida,
                    categoria_especial=params.categoria_especial,
                )
                passagens.append(passagem)
                forms.append(confirmar_venda_form)
        passagens = self.create_passagens(passagens)
        return [
            {
                "confirmar_venda_form": form,
                "passagem": passagem,
            }
            for passagem, form in zip(passagens, forms)
        ]

    def _get_poltrona_bloqueada_key_and_price(
        self, trechoclasse_id: int, poltrona: int, categoria_reservada_id: str
    ) -> list[models.InfosCacheaveisBloqueioPoltrona]:
        bloqueios = self.cache.get_poltrona_key_cache(trechoclasse_id, poltrona)
        if not bloqueios or not bloqueios[0].transacao:
            self.bloquear_poltronas(trechoclasse_id, [poltrona], categoria_reservada_id)
            bloqueios = self.cache.get_poltrona_key_cache(trechoclasse_id, poltrona)
        return bloqueios

    def _cancela_dict_to_cancela_params(self, params):
        travel_id = params.travel_id
        buseiro_id = params.buseiro_id
        passagens_cadastradas = self.get_passagens_confirmadas(travel_id, buseiro_id=buseiro_id)
        if not passagens_cadastradas:
            return []
        requests = []
        for passagem in passagens_cadastradas:
            buserlogger.info(
                "Marketplace - Totalbus: passagem %s no host %s ",
                passagem.localizador,
                self.base_url,
                "com a poltrona de numero %s sera cancelada",
                passagem.poltrona_external_id,
            )
            if passagem.data_hora_partida:
                datetime_partida = datetime.strptime(passagem.data_hora_partida, "%Y-%m-%d %H:%M")
            else:
                origem_tz = passagem.trechoclasse_integracao.origem.cidade.timezone or "America/Sao_Paulo"
                datetime_partida = passagem.trechoclasse_integracao.datetime_ida.astimezone(ZoneInfo(origem_tz))
            datetime_partida_str = datetime_partida.strftime("%Y-%m-%d")
            cancelar_venda_params = {
                "transacao": passagem.pedido_external_id,
                "numeroSistema": passagem.numero_passagem,
                "data": datetime_partida_str,
                "pax_valido": params.pax_valido,
            }
            requests.append({"cancelar_venda_params": cancelar_venda_params, "passagem": passagem})
        return requests

    def _verifica_poltrona_params(self, trecho_classe: TrechoClasse) -> models.RetornaPoltronasModel:
        id_origem = int(trecho_classe.origem.id_external)
        id_destino = int(trecho_classe.destino.id_external)
        data_corrida = loads(trecho_classe.provider_data).get("dataCorrida")
        if trecho_classe.conexao:
            id_destino = trecho_classe.conexao.get("localidadeConexaoId")
        poltronas_livres_params = models.RetornaPoltronasModel(
            origem=id_origem,
            destino=id_destino,
            data=data_corrida,
            servico=trecho_classe.external_id,
        )
        return poltronas_livres_params

    def compra_connection_errors(self, error_str: str) -> bool:
        for error in [
            "Categoria indisponível para esse serviço.",
            "O tipo de passagem selecionado já não tem disponibilidade",
            "#BPERS#",
            "Error.Bloqueo.Asiento.Ya.Cedido",
        ]:
            if error in error_str:
                return True
        return False

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax: bool = False):
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        requests = self._reserva_dict_to_comprar_params(trecho_classe, params)

        empresa_com_emissao_conexao_sync = str(self.company.id) in self.get_lista_empresas_com_emissao_conexao_sync()
        emite_conexao_sync = trecho_classe.conexao and empresa_com_emissao_conexao_sync

        if constance_config.USE_HTTP_ASYNC is True and not is_running_on_celery() and not emite_conexao_sync:
            return self._comprar_async(params, requests)
        else:
            return self._comprar_sync(params, requests)

    @async_to_sync
    async def _comprar_async(self, params: ComprarForm, requests):
        reservas_por_id = {str(reserve["passagem"].id): reserve for reserve in requests}

        tasks = [
            asyncio.create_task(self._confirmar_venda_reserva(params, reserve), name=chave)
            for chave, reserve in reservas_por_id.items()
        ]
        (done, _) = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
        try:
            # Se uma emissão deu erro, a exceção será propagada ao acessar o result da task
            return {"passagens": [t.result() for t in done]}
        except Exception as ex:
            passagens_confirmadas = []
            passagens_exception = []
            for task_done in done:
                reserva = reservas_por_id[task_done.get_name()]
                if task_done.exception() is None:
                    passagens_confirmadas.append(reserva["passagem"])
                else:
                    passagens_exception.append(reserva["passagem"])

            # Caso tenha dado erro em alguma emissão de passagem da travel, cancela as restantes já emitidas
            await sync_to_async(solicita_cancelamento_passagens)(passagens_confirmadas)
            await self.envia_poltronas_fila_desbloqueio_async(
                params.trechoclasse_id,
                poltronas=[p.poltrona_external_id for p in passagens_exception],
            )
            error_msg = await sync_to_async(error_str)(ex)
            for p in passagens_exception:
                await p.asave_error(error_msg)
            if await sync_to_async(self.compra_connection_errors)(error_msg):
                raise RodoviariaConnectionError from ex
            raise ex

    def _comprar_sync(self, params: ComprarForm, requests):
        warnings.warn("deprecated, please use _comprar_async", DeprecationWarning, stacklevel=1)
        responses = []
        passagens_confirmadas = []
        for index, reserve in enumerate(requests):
            passagem = reserve["passagem"]
            confirmar_venda_form = reserve["confirmar_venda_form"]
            try:
                response = self.confirmar_venda(confirmar_venda_form)
            except Exception as ex:
                solicita_cancelamento_passagens(passagens_confirmadas)
                poltronas_para_desbloquear = [r["passagem"].poltrona_external_id for r in requests[index:]]
                if poltronas_para_desbloquear:
                    self.envia_poltronas_fila_desbloqueio(
                        params.trechoclasse_id,
                        poltronas=poltronas_para_desbloquear,
                    )
                passagem.save_error(error_str(ex))
                if self.compra_connection_errors(error_str(ex)):
                    raise RodoviariaConnectionError from ex
                raise ex
            buserlogger.info("Transacao - Totalbus %s", confirmar_venda_form.transacao)
            passagem.localizador = response["localizador"]
            passagem.numero_passagem = response["numeroSistema"]
            passagem.poltrona_external_id = response["poltrona"]
            passagem.save_confirmed()
            passagem.provider_data = response
            self._atribui_bpe(passagem, response)
            self._atribui_taxa_embarque(passagem, response)
            passagem.save()
            grupo = passagem.trechoclasse_integracao.grupo
            grupo.linha = passagem.linha
            grupo.save()
            passagens_confirmadas.append(passagem)
            responses.append(passagem.to_dict_json())
        return {"passagens": responses}

    async def _confirmar_venda_reserva(self, params: ComprarForm, reserve):
        passagem = reserve["passagem"]
        confirmar_venda_form = reserve["confirmar_venda_form"]
        response = await self.confirmar_venda_async(confirmar_venda_form)

        buserlogger.info("(async) Transacao - Totalbus %s", confirmar_venda_form.transacao)
        passagem.localizador = response["localizador"]
        passagem.numero_passagem = response["numeroSistema"]
        passagem.poltrona_external_id = response["poltrona"]
        await passagem.asave_confirmed()
        passagem.provider_data = response
        await sync_to_async(self._atribui_bpe)(passagem, response)
        await sync_to_async(self._atribui_taxa_embarque)(passagem, response)
        await passagem.asave()
        grupo = passagem.trechoclasse_integracao.grupo
        grupo.linha = passagem.linha
        await grupo.asave()
        buserlogger.info("(async) deu bom")
        return await sync_to_async(passagem.to_dict_json)()

    def _atribui_bpe(self, passagem, response):
        bpe_info = response.get("bpe")
        if bpe_info:
            passagem.bpe_qrcode = bpe_info.get("qrcodeBpe")
            passagem.bpe_monitriip_code = bpe_info.get("codigoMonitriipBPe")
            passagem.data_autorizacao = datetime.now(dttz.utc)
            passagem.preco_base = self._sanitize_preco(bpe_info.get("tarifa"))
            passagem.taxa_embarque = self._sanitize_preco(bpe_info.get("taxaEmbarque"))
            passagem.seguro = self._sanitize_preco(bpe_info.get("seguro"))
            passagem.pedagio = self._sanitize_preco(bpe_info.get("pedagio"))
            passagem.outras_taxas = self._sanitize_preco(bpe_info.get("outros"))
            passagem.outros_tributos = bpe_info.get("outrosTributos")
            passagem.chave_bpe = bpe_info.get("chaveBpe")
            passagem.numero_bpe = bpe_info.get("numeroBpe")
            passagem.serie_bpe = bpe_info.get("serie")
            passagem.protocolo_autorizacao = bpe_info.get("protocoloAutorizacao")
            passagem.prefixo = bpe_info.get("prefixo")
            passagem.plataforma = bpe_info.get("plataforma")
            passagem.numero_bilhete = response.get("numeroBilhete")
            linha = bpe_info.get("linha")
            passagem.linha = linha

    def _atribui_taxa_embarque(self, passagem, response):
        if "taxaEmbarque" in response:
            tipo = response["taxaEmbarque"]["tipo"]
            passagem.codigo_taxa_embarque = response.get("qrCodeTaxaEmbarque")
            if tipo == "QrCode":
                passagem.tipo_taxa_embarque = Passagem.TipoTaxaEmbarque.QRCODE
            elif tipo == "BarCode":  # TODO: Validar retorno nas passagens da Totalbus
                passagem.tipo_taxa_embarque = Passagem.TipoTaxaEmbarque.BARCODE
            passagem.numero_bilhete_embarque = response["taxaEmbarque"]["numeroBilheteEmbarque"]

    def cancela_venda(self, params):
        requests = self._cancela_dict_to_cancela_params(params)
        responses = []
        for reserve in requests:
            passagem = reserve.get("passagem")
            cancelar_venda_params = reserve.get("cancelar_venda_params")
            cancelar_venda_response = {}
            try:
                cancelar_venda_response = self.cancelar_venda(cancelar_venda_params)
            except RodoviariaUnauthorizedError:
                # Se entrou aqui ignora que deu erro e deixa cancelar.
                passagem.tags.add("cancelada_login_inativo")
            except Exception as ex:
                self._save_error_cancelamento_passagens(
                    [request.get("passagem") for request in requests], error_str(ex)
                )
                raise ex

            if cancelar_venda_response:
                responses.append(cancelar_venda_response)
                passagem.multa = cancelar_venda_response.get("multa")

            buserlogger.info("Transacao - Totalbus %s", cancelar_venda_params["transacao"])
            passagem.save_canceled()
        return responses

    @retry(
        retry=retry_if_exception_type(PoltronaJaSelecionadaException),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def verifica_poltrona(self, verifica_poltronas_params: VerificarPoltronaForm):
        categoria_especial = verifica_poltronas_params.categoria_especial or Passagem.CategoriaEspecial.NORMAL
        poltronas = self.get_poltronas_livres(
            verifica_poltronas_params.trechoclasse_id, verifica_poltronas_params.passageiros, categoria_especial
        )
        self.bloquear_poltronas(verifica_poltronas_params.trechoclasse_id, poltronas, categoria_especial)
        return poltronas

    def vagas_por_categoria_especial(self, trechoclasse_id: int):
        # aproveita o get_desenho_mapa_poltronas que filtra pela classe pesquisada
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        servico_params = models.ServicoTrecho.from_trechoclasse(trecho_classe=trecho_classe).dict()
        return self._busca_mapa_vagas_por_categoria_especial(servico_params)

    def _busca_mapa_vagas_por_categoria_especial(self, servico_params):
        categorias_viagem: list[models.AssentoCategoriaEspecial] = self.consultar_categoria_corrida_request(
            servico_params
        )
        categorias_cadastradas = CompanyCategoriaEspecial.objects.filter(company=self.company).exclude(
            categoria_especial=Passagem.CategoriaEspecial.IGNORADO
        )
        categorias_especiais_map = {c.categoria_id_external: c.categoria_especial for c in categorias_cadastradas}
        assentos_por_categoria = {}
        categorias_para_cadastrar = []
        for categoria in categorias_viagem:
            categoria_especial = categorias_especiais_map.get(categoria.categoria_id)
            if not categoria_especial:
                categorias_para_cadastrar.append(
                    CompanyCategoriaEspecial(
                        company=self.company,
                        categoria_id_external=categoria.categoria_id,
                        descricao_external=categoria.desccategoria,
                    )
                )
            else:
                assentos_por_categoria[categoria_especial] = categoria.disponibilidade_cota
        if categorias_para_cadastrar:
            CompanyCategoriaEspecial.objects.bulk_create(
                categorias_para_cadastrar,
                update_conflicts=True,
                unique_fields=["company_id", "categoria_id_external"],
                update_fields=["categoria_id_external", "descricao_external"],
            )
        return assentos_por_categoria

    def buscar_todos_servicos(self, periodo_dias=None):
        request = endpoints.BuscarTodosServicosDisponiveisConfig(self.login)
        executor = get_http_executor()
        response = request.invoke(executor)
        return response.json()["servicos"]

    def buscar_corridas(self, request_params, match_params=None):
        try:
            corridas = self._buscar_corridas(
                request_params["origem"], request_params["destino"], request_params["data"]
            )
        except RodoviariaException as e:
            if not match_params:
                raise e
            return self._parse_retorno_buscar_servico(None, None, [])

        timezone = None
        if match_params:
            timezone = match_params["timezone"]

        corridas_form = self._make_corridas_form(corridas, timezone)
        corridas_form.servicos = novos_modelos_svc.filter_servicos_novos_modelos(
            corridas_form.servicos,
            self.company.company_internal_id,
            self.company.modelo_venda,
        )
        if not match_params:
            return corridas_form

        d_args = self._find_match_corridas(corridas_form.servicos, request_params, **match_params)
        if (
            d_args.get("servico_encontrado")
            and self.company.company_internal_id == novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID
        ):
            servico_params = models.ServicoTrecho(
                origem=request_params["origem"],
                destino=request_params["destino"],
                data=d_args["servico_encontrado"].provider_data["dataCorrida"],
                servico=d_args["servico_encontrado"].external_id,
            ).dict()
            map_vagas_categoria_especial = self._busca_mapa_vagas_por_categoria_especial(servico_params)
            d_args["servico_encontrado"].vagas = map_vagas_categoria_especial.get(Passagem.CategoriaEspecial.NORMAL, 0)
        return self._parse_retorno_buscar_servico(**d_args)

    def _filter_conexoes_com_mesmo_onibus(self, corridas):
        corridas_filtradas = []
        for c in corridas:
            if conexao := c.get("conexao"):
                dia_chegada_primeira_perna = conexao["primeiroTrechoDataChegada"]
                hora_chegada_primeira_perna = conexao["primeiroTrechoHoraChegada"]
                datetime_chegada_primeira_perna = datetime.strptime(
                    f"{dia_chegada_primeira_perna} {hora_chegada_primeira_perna}", "%d/%m/%Y %H:%M"
                )
                dia_saida_segunda_perna = conexao["segundoTrechoDataSaida"]
                hora_saida_segunda_perna = conexao["segundoTrechoHoraSaida"]
                datetime_saida_segunda_perna = datetime.strptime(
                    f"{dia_saida_segunda_perna} {hora_saida_segunda_perna}", "%d/%m/%Y %H:%M"
                )
                tempo_espera = datetime_saida_segunda_perna - datetime_chegada_primeira_perna
                if tempo_espera > timedelta(minutes=10):
                    continue
            corridas_filtradas.append(c)
        return corridas_filtradas

    def _find_match_corridas(self, corridas: list[ServicoForm], request_params, datetime_ida, timezone, tipo_assento):
        matches: list[ServicoForm] = []
        mismatches: list[ServicoForm] = []
        buser_class = tipo_assento
        d_args = {}
        for servico in corridas:
            api_class = servico.classe.lower()
            datetime_ida_servico = to_tz(servico.external_datetime_ida, timezone)
            if self._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico) and (
                self._does_class_match(buser_class, api_class)
            ):
                matches.append(servico)
            else:
                mismatches.append(servico)

        mismatches_classe_horario = [(m.classe, m.external_datetime_ida) for m in mismatches]
        matches_classe_horario = [(m.classe, m.external_datetime_ida) for m in matches]
        msg = (
            f"TotalbusAPI - Buscando servico: {self.company.name} com {request_params}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> matches = {matches_classe_horario} "
            f"-> mismatches = {mismatches_classe_horario}"
        )
        buserlogger.info(msg)

        if len(matches) == 1:
            matched_service = matches[0]
            api_class = matched_service.classe.lower()
            if self._does_class_match(buser_class, api_class):
                d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
                return d_args

        if len(matches) > 1:
            if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
                matches_com_poltronas_vagas = [m for m in matches if m.get("poltronasLivres", 0) > 0]
                if matches_com_poltronas_vagas and len(matches_com_poltronas_vagas) != len(matches):
                    matches = matches_com_poltronas_vagas
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(datetime_ida, timezone, k.external_datetime_ida),
                    -k.vagas,
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                api_class = servico.classe.lower()
                if self._does_class_match(buser_class, api_class):
                    matched_service = servico
                    break

            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args
        d_args.update({"timezone": timezone, "mismatches": mismatches})
        return d_args

    def _parse_retorno_buscar_servico(
        self, servico_encontrado: ServicoForm | None = None, timezone=None, mismatches: list[ServicoForm] | None = None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servicos = [servico_encontrado]
        else:
            servicos = mismatches

        return BuscarServicoForm(found=found, servicos=servicos)

    def _buscar_corridas(self, origem_id, destino_id, data_str):
        params = models.BuscarServicoForm(origem=origem_id, destino=destino_id, data=data_str)  # aaaa-mm-dd

        request = endpoints.BuscarCorridasRequestConfig(self.login)
        executor = get_http_executor()
        response = request.invoke(executor, json=params.dict())
        corridas = response.json()["lsServicos"]
        if self.company.modelo_venda == Company.ModeloVenda.MARKETPLACE:
            corridas = self._filter_conexoes_com_mesmo_onibus(corridas)
        if self.company.company_external_id:
            corridas = [corrida for corrida in corridas if corrida["empresaId"] == self.company.company_external_id]
        return corridas

    def format_datetime(self, dt, timezone=None):
        dt = datetime.strptime(dt, "%Y-%m-%d %H:%M")
        if timezone:
            dt = to_tz(dt, timezone)
        return dt

    def _make_corridas_form(self, corridas, timezone):
        found = bool(corridas)

        servicos = [
            ServicoForm(
                linha=corrida["marcaId"],
                external_datetime_ida=self.format_datetime(corrida["saida"], timezone),
                external_id=corrida["servico"],
                preco=corrida["preco"],
                vagas=corrida["poltronasLivres"],
                provider_data=corrida,
                external_datetime_chegada=self.format_datetime(corrida["chegada"]),
                classe=corrida["classe"],
                capacidade_classe=self._get_capacidade_servico(corrida),
                distancia=corrida["km"],
                rota_external_id=corrida["servico"],
            )
            for corrida in corridas
        ]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _get_capacidade_servico(self, corrida):
        if self.company.company_internal_id == novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID:
            # no novo modelo de compra de poltronas antecipadas, compramos no máximo 4 poltronas por viagem
            return 4
        return corrida["poltronasTotal"]

    def rota_id_from_trecho_classe(self, trecho_classe):
        return trecho_classe.external_id

    def datetime_ida_from_trecho_classe(self, trecho_classe):
        if trecho_classe.provider_data:
            provider_data = json.loads(trecho_classe.provider_data)
            datetime_ida = provider_data["dataCorrida"]
        else:
            try:
                datetime_ida = to_tz(
                    trecho_classe.grupo.datetime_ida,
                    trecho_classe.origem.cidade.timezone,
                ).strftime("%Y-%m-%d")
            except ZoneInfoNotFoundError as exc:
                raise RodoviariaException(
                    "Não foi possível achar o timezone. Verifique se o trecho contém origem e se há timezone na cidade."
                ) from exc
        return datetime_ida

    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object, modelo_venda):
        return descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
            self.login,
            self.company.company_internal_id,
            modelo_venda,
            next_days,
            shift_days,
            queue_name,
            return_task_object,
        )

    def fetch_rotina(self, rota, next_days, first_day):
        return RotinaTotalbus(self).fetch_rotina(rota, next_days, first_day)

    def fetch_rotina_async(self, rota, next_days, first_day, queue_name):
        company_internal_id = self.company.company_internal_id
        modelo_venda = Company.ModeloVenda.MARKETPLACE
        return rotina_totalbus_async_svc.fetch(
            company_internal_id, modelo_venda, rota, next_days, first_day, queue_name
        )

    def fetch_rotinas_empresa(self, next_days, first_day, queue_name):
        return rotina_totalbus_async_svc.fetch_rotinas_empresa(
            self.login,
            self.company_id,
            next_days,
            first_day,
            queue_name,
        )

    def buscar_servicos_por_data(self, data_inicio: date, data_fim: date, company_external_id: int | None = None):
        servicos = self.buscar_viagens_por_periodo(data_inicio, data_fim, company_external_id)
        if not servicos:
            return {}

        map_servicos_data = {}
        for serv in servicos:
            id_viagem = serv.external_id
            datetime_ida = serv.data_hora_servico
            map_servicos_data[id_viagem] = max(datetime_ida, map_servicos_data.get(id_viagem, datetime.min))

        return map_servicos_data

    def fetch_data_limite_servicos(self, data_inicio, next_days=7, map_servicos_data=None, ids_empresas_api=None):
        # para o endpoint de servicos detalhado, é obrigatorio o id_external da empresa, mas pra alguns parceiros,
        # vendemos passagens de todos ids_externals.
        # Para buscar o serviço de todas, caso não seja uma especifica, chama o endpoint de get empresas e chama o
        # endpoint pra cada uma
        if map_servicos_data is None:
            map_servicos_data = {}
        if not ids_empresas_api:
            if self.company.company_external_id:
                ids_empresas_api = [self.company.company_external_id]
            else:
                ids_empresas_api = [e.external_id for e in self.buscar_empresas()]

        data_fim = data_inicio + timedelta(days=next_days)
        for id_external_empresa in ids_empresas_api:
            buserlogger.info(
                "[TotalbusAPI][fetch_data_limite_servicos][self.company.company_internal_id=%s] ",
                self.company.company_internal_id,
                f"Buscando servicos por data. {data_inicio=} {data_fim=} {id_external_empresa=}",
            )
            new_map_servicos_data = self.buscar_servicos_por_data(data_inicio, data_fim, id_external_empresa)
            if not new_map_servicos_data:
                # Caso o endpoint não retorne nada para o periodo, não procura mais nessa empresa
                # (para inativas ou sem servicos)
                ids_empresas_api.remove(id_external_empresa)
                continue

            for serv in new_map_servicos_data:
                map_servicos_data[serv] = max(
                    new_map_servicos_data[serv],
                    map_servicos_data.get(serv, datetime.min),
                )

        if len(ids_empresas_api) == 0:
            # se a lista chegou a zero, todas as empresas foram testadas até nenhuma retornar mais. Então retorna o map
            return map_servicos_data

        return self.fetch_data_limite_servicos(data_fim, next_days, map_servicos_data, ids_empresas_api)

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        timezone = passagem.trechoclasse_integracao.origem.cidade.timezone
        data = passagem.trechoclasse_integracao.datetime_ida
        data = to_tz(data, timezone)
        params = {"data": data.strftime("%Y-%m-%d"), "numeroSistema": passagem.numero_passagem}
        try:
            bilhetes = self.buscar_bilhetes(params)
        except RodoviariaBaseException:
            buserlogger.exception(
                "Totalbus - Erro ao consultar reserva na atualização de passagens no staff"
                "com passagem.numero_passagem=%s",
                passagem.numero_passagem,
            )
            raise
        else:
            if len(bilhetes) == 0:
                raise PassengerNotRegistered(
                    f"Totalbus - Não foi possível encontrar a passagem {passagem.numero_passagem}"
                )
        bilhetes_ativos = list(filter(lambda x: not x["cancelado"], bilhetes))
        if len(bilhetes_ativos) == 0:
            bilhete_mais_atual = bilhetes[0]
        else:
            bilhete_mais_atual = bilhetes_ativos[0]
        bilhete_padrao = RetornoConsultarBilheteForm.parse_obj(
            {
                "integracao": "Totalbus",
                "numero_passagem": bilhete_mais_atual["bpe"]["numeroSistema"],
                "localizador": bilhete_mais_atual["localizador"],
                "numero_bilhete": bilhete_mais_atual["numeroBilhete"],
                "status": ("cancelada" if bilhete_mais_atual["cancelado"] else "confirmada"),
                "numero_assento": bilhete_mais_atual["poltrona"],
                "primeiro_nome_pax": bilhete_mais_atual["nome"],
                "numero_documento": bilhete_mais_atual["documento"],
                "bpe_id": bilhete_mais_atual["bpe"]["numeroBPe"],
                "bpe_public_url": bilhete_mais_atual["bpe"]["qrCode"],
                "data_partida": bilhete_mais_atual["dataViagem"],
                "origem": bilhete_mais_atual["origem"]["cidade"],
                "destino": bilhete_mais_atual["destino"]["cidade"],
                "empresa_name": bilhete_mais_atual["bpe"]["cabecalhoEmitente"]["razaoSocial"],
                "valor_passagem": bilhete_mais_atual["bpe"]["tarifa"],
                "taxa_embarque": bilhete_mais_atual["bpe"]["embarque"],
            }
        )
        return bilhete_padrao

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        params = self._verifica_poltrona_params(trecho_classe)
        poltronas = self.retorna_poltronas_request(params)
        return self._parse_poltronas_in_bus_layout(trecho_classe, poltronas)

    def _get_map_categoria_especial_empresa_buser(self, trecho_classe: TrechoClasse):
        if trecho_classe.is_conexao:
            conexao = models.ConexaoModel.parse_obj(trecho_classe.conexao)
            primeira_perna, _ = self._get_pernas_conexao(conexao)
            servico_params = primeira_perna
        else:
            servico_params = models.ServicoTrecho.from_trechoclasse(trecho_classe=trecho_classe).dict()
        categorias = self.consultar_categoria_corrida_request(servico_params)
        categoria_objs = [
            CompanyCategoriaEspecial(
                company_id=self.company_id,
                categoria_id_external=categoria.categoria_id,
                descricao_external=categoria.desccategoria,
            )
            for categoria in categorias
        ]
        CompanyCategoriaEspecial.objects.bulk_create(
            categoria_objs,
            update_conflicts=True,
            unique_fields=["company_id", "categoria_id_external"],
            update_fields=["categoria_id_external", "descricao_external"],
        )
        categoria_objs = CompanyCategoriaEspecial.objects.filter(company_id=self.company_id)
        mapa_categoria = {categoria.categoria_id_external: categoria.categoria_especial for categoria in categoria_objs}
        if None in mapa_categoria.values():
            buserlogger.info("totalbus_categoria_especial_none", extra={"trechoclasse_id": trecho_classe.id})
            raise RodoviariaTrechoclasseFactoryException(
                trechoclasse_id=trecho_classe.id, message="Não foi possível encontrar todas as categorias"
            )
        mapa_categoria = {**mapa_categoria, "-1": "normal"}  # adiciona valor default Totalbus.
        return mapa_categoria

    def _parse_poltronas_in_bus_layout(self, trecho_classe, poltronas: list[models.MapaPoltronaItem]):
        provider_data = loads(trecho_classe.provider_data)
        map_categorias = self._get_map_categoria_especial_empresa_buser(trecho_classe)
        tipo_assento_parceiro = provider_data.get("classe")
        tipo_assento_buser = get_buser_class_by_company(self.company, tipo_assento_parceiro)

        def categoria_especial(poltrona):
            return map_categorias.get(str(poltrona.categoria_reservada_id), Passagem.CategoriaEspecial.NORMAL)

        seats = [
            {
                "livre": poltrona.disponivel,
                "y": poltrona.x,
                "x": poltrona.y + 1,
                "numero": poltrona.numero,
                "tipo_assento": tipo_assento_buser,
                "categoria_especial": [categoria_especial(poltrona)],
            }
            for poltrona in poltronas
            if poltrona.numero.isnumeric() and categoria_especial(poltrona) != Passagem.CategoriaEspecial.IGNORADO
        ]
        return Onibus.parse_obj({"layout": [{"andar": 1, "assentos": seats}]})

    def get_lista_empresas_com_emissao_conexao_sync(self):
        empresas_com_emissao_conexao_sync = constance_config.EMPRESAS_COM_EMISSAO_CONEXAO_SYNC
        return [empresa for empresa in empresas_com_emissao_conexao_sync.split(",") if empresa]

    async def envia_poltronas_fila_desbloqueio_async(self, trecho_classe_id, poltronas):
        await sync_to_async(async_desbloquear_poltronas.delay)(self.company.id, trecho_classe_id, poltronas)

    def envia_poltronas_fila_desbloqueio(self, trecho_classe_id: int, poltronas: list[int]):
        if not poltronas:
            return
        async_desbloquear_poltronas.delay(self.company.id, trecho_classe_id, poltronas)


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas(company_rodoviaria_id: int, trecho_classe_id: int, poltronas: list[int]):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = TotalbusAPI(company)
    api.desbloquear_poltronas(trecho_classe_id, poltronas)


def lista_empresas_api(login_params):
    login_obj = TotalbusUndefinedCompanyClient.parse_obj(login_params)
    executor = get_http_executor()
    request_config = endpoints.ConsultarEmpresasConfig(login_obj)
    response = request_config.invoke(executor)
    response_json = response.json()
    if {"id": -1, "nome": "TODAS"} in response_json:
        response_json.remove({"id": -1, "nome": "TODAS"})
    return response_json


def fetch_formas_pagamento(login_params):
    login_obj = TotalbusUndefinedCompanyClient.parse_obj(login_params)
    executor = get_http_executor()
    request_config = endpoints.BuscarFormasPagamentoConfig(login_obj)
    response = request_config.invoke(executor)
    return response.json()
