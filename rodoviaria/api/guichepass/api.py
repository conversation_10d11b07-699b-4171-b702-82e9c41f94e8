import json
import logging
from collections import OrderedDict, defaultdict
from datetime import datetime, timed<PERSON><PERSON>

from celery import shared_task
from pydantic import parse_obj_as
from zoneinfo import ZoneInfo

import rodoviaria.api.guichepass.endpoints as endpoints
import rodoviaria.api.guichepass.memcache as guichepass_mc
import rodoviaria.api.guichepass.models as models
from commons.celery_utils import DefaultQueueNames
from commons.dateutils import now, to_default_tz
from commons.django_utils import error_str
from commons.redis import lock
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Assento, Deck, Onibus
from rodoviaria.forms.staff_forms import (
    GuichepassAnonymousLogin,
    GuichepassUndefinedCompany,
    GuichepassUndefinedCompanyLogin,
)
from rodoviaria.models.core import Company, Passagem, TrechoClasse
from rodoviaria.models.guichepass import GuichepassLogin
from rodoviaria.service import poltronas_svc
from rodoviaria.service.class_match_svc import get_buser_class_by_company
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaOverbookingException,
)
from rodoviaria.service.solicita_cancelamento_svc import solicita_cancelamento_passagens

buserlogger = logging.getLogger("rodoviaria")


class GuichepassAPI(RodoviariaAPI):
    PASSAGEM_TYPE_MAP = {
        Passagem.CategoriaEspecial.CRIANCA: "",
        Passagem.CategoriaEspecial.PCD: "DEFICIENT",
        Passagem.CategoriaEspecial.JOVEM_100: "YOUNG_LOW_INCOME",
        Passagem.CategoriaEspecial.JOVEM_50: "YOUNG_LOW_INCOME_50",
        Passagem.CategoriaEspecial.IDOSO_100: "ELDER",
        Passagem.CategoriaEspecial.IDOSO_50: "ELDER_50",
        Passagem.CategoriaEspecial.NORMAL: "DEFAULT",
    }

    CATEGORIA_ESPECIAL_MAP = {valor: chave for chave, valor in PASSAGEM_TYPE_MAP.items()}
    divergencia_maxima_pct = 50

    def __init__(self, company):
        super().__init__(company)
        self.login = GuichepassLogin.objects.select_related("company").get(company=self.company)
        self.map_tipos_onibus = None
        self.cache = guichepass_mc.GuichepassMC(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    @property
    def tipos_de_onibus_map(self):
        if not self.map_tipos_onibus:
            self.map_tipos_onibus = {str(d["id"]): d["name"] for d in self._tipos_de_onibus()}
        return self.map_tipos_onibus

    def _bloquear_poltrona(self, params: models.BloquearPoltronaForm):
        resp = endpoints.bloquear_poltronas_request(self.login, params)
        return resp

    def _cancelar_venda(self, params: models.CancelarVendaForm):
        resp = endpoints.cancelar_venda_request(self.login, params)
        return resp

    def _confirmar_venda(self, params: models.ConfirmarVendaForm):
        resp = endpoints.confirmar_venda_request(self.login, params)
        return resp

    def get_map_poltronas(self, trechoclasse_id):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        params = self._verifica_poltrona_params(trecho_classe)
        response = endpoints.retorna_poltronas_request(self.login, params)
        poltronas = response.poltronas

        resp = {}
        for poltrona in poltronas:
            resp[poltrona.number] = "livre" if poltrona.status == "FREE" else "ocupada"
        return resp

    def get_poltronas_livres(self, params: models.RetornaPoltronasForm, numero_poltronas):
        resp = endpoints.retorna_poltronas_request(self.login, params)
        vagas_disponiveis = resp.travel.free_seats
        if vagas_disponiveis < numero_poltronas:
            msg = "Guichepass: Not enough free seats on trechoclasse (Overbooking)"
            buserlogger.warning(msg)
            raise RodoviariaOverbookingException(vagas_disponiveis)
        for poltrona in resp.poltronas:
            n_poltrona = poltrona.number
            if n_poltrona == "":
                n_poltrona = 999
            poltrona.number = int(n_poltrona)
        poltronas = sorted(resp.poltronas, key=lambda k: k.number)
        set_poltronas = set()
        first_seat = False
        poltronas_separadas = []
        for seat in poltronas:
            n_poltrona = int(seat.number)
            status = seat.status
            poltronas_restantes = numero_poltronas - len(set_poltronas)
            if not first_seat and poltronas_restantes > 1 and status == "FREE":
                first_seat = n_poltrona
                continue
            if poltronas_restantes == 1 and status == "FREE":
                set_poltronas.add(n_poltrona)
                break
            if first_seat and status == "FREE":
                set_poltronas.add(first_seat)
                set_poltronas.add(n_poltrona)
                first_seat = False
                if len(set_poltronas) >= numero_poltronas:
                    break
                continue
            if first_seat and status != "FREE":
                poltronas_separadas.append(first_seat)
                first_seat = False
                continue
        while len(set_poltronas) < numero_poltronas:
            set_poltronas.add(poltronas_separadas.pop(0))
        return list(set_poltronas)

    def _reserva_dict_to_comprar_params(self, params: ComprarForm):
        travel_id = params.travel_id
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        poltronas_livres = params.poltronas

        forms = []
        passagens = []

        for passageiro in params.passageiros:
            poltrona = poltronas_livres.pop(0)
            if params.extra_poltronas:
                dados_bloqueio_poltrona = parse_obj_as(models.InfosCacheaveisBloqueioPoltrona, params.extra_poltronas)
            else:
                dados_bloqueio_poltrona = self.cache.get_poltrona_bloqueada_cache(params.trechoclasse_id, poltrona)
            reserva_id = dados_bloqueio_poltrona.reserva_id
            confirma_reserva_form = models.ConfirmarVendaForm(
                name=passageiro.name,
                document=(passageiro.rg_number[:20] if passageiro.rg_number else "") or passageiro.cpf,
                price=trecho_classe.preco_rodoviaria,
                id=reserva_id,
            )
            if passageiro.birthday:
                confirma_reserva_form.birthdate = passageiro.birthday.strftime("%Y-%m-%d")
            if (
                params.categoria_especial != Passagem.CategoriaEspecial.NORMAL
                and dados_bloqueio_poltrona.categoria_external_id
            ):
                confirma_reserva_form.ticketTypeId = dados_bloqueio_poltrona.categoria_external_id
                confirma_reserva_form.price = dados_bloqueio_poltrona.preco
            passagem = Passagem(
                trechoclasse_integracao=trecho_classe,
                company_integracao=self.company,
                poltrona_external_id=poltrona,
                buseiro_internal_id=passageiro.id,
                travel_internal_id=travel_id,
                valor_cheio=params.valor_cheio,
                status=Passagem.Status.INCOMPLETA,
                numero_passagem=reserva_id,
                categoria_especial=params.categoria_especial,
                preco_rodoviaria=confirma_reserva_form.price,
            )
            passagens.append(passagem)
            forms.append(confirma_reserva_form)

        passagens = self.create_passagens(passagens)
        return [
            {
                "confirmar_venda_form": form,
                "passagem": passagem,
            }
            for form, passagem in zip(forms, passagens)
        ]

    def _cancela_dict_to_cancela_params(self, params):
        travel_id = params.travel_id
        buseiro_id = params.buseiro_id
        passagens_cadastradas = self.get_passagens_confirmadas(travel_id, buseiro_id=buseiro_id)
        if not passagens_cadastradas:
            return []
        requests = []
        for passagem in passagens_cadastradas:
            numero_passagem = passagem.numero_passagem
            id_poltrona = passagem.poltrona_external_id
            msg = (
                f"Marketplace - Guichepass: passagem {numero_passagem} no host {self.base_url} com a poltrona de numero"
                f" {id_poltrona} sera cancelada"
            )
            buserlogger.info(msg)
            cancelar_venda_params = {"id": numero_passagem}
            requests.append({"cancelar_venda_params": cancelar_venda_params, "passagem": passagem})
        return requests

    def _verifica_poltrona_params(self, trecho_classe) -> models.RetornaPoltronasForm:
        id_origem = int(trecho_classe.origem.id_external)
        id_destino = int(trecho_classe.destino.id_external)
        datetime_partida = trecho_classe.datetime_ida.astimezone(ZoneInfo("America/Sao_Paulo")).strftime("%Y-%m-%d")
        poltronas_livres_params = {
            "origin": id_origem,
            "destination": id_destino,
            "date": datetime_partida,
            "busCompany": self.company.company_external_id,
            "service": trecho_classe.external_id,
        }
        return models.RetornaPoltronasForm.parse_obj(poltronas_livres_params)

    def _tipos_de_onibus(self):
        resp = endpoints.tipos_onibus_request(self.login)
        return resp

    def _parse_retorno_buscar_servico(
        self, servico_encontrado=None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servico = ServicoForm(
                provider_data=servico_encontrado,
                external_id=servico_encontrado["service"],
                tipo_veiculo=servico_encontrado["busType"],
                classe=self.tipos_de_onibus_map[servico_encontrado["busType"]],
                vagas=servico_encontrado["freeSeats"],
                preco=servico_encontrado["price"],
                desconto=servico_encontrado["companyDiscount"],
                external_datetime_ida=self._get_datetime_servico(
                    timezone, servico_encontrado["departure"], "%Y-%m-%dT%H:%M:%S"
                ),
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico, timezone):
        servico_form = ServicoForm.parse_obj(
            {
                "external_id": servico["service"],
                "preco": servico["price"],
                "external_datetime_ida": self._get_datetime_servico(
                    timezone, servico["departure"], "%Y-%m-%dT%H:%M:%S"
                ),
                "classe": self.tipos_de_onibus_map[servico["busType"]],
                "external_company_id": self.company.company_external_id,
                "vagas": servico["freeSeats"],
                "provider_data": servico,
            }
        )
        return servico_form

    def cancela_venda(self, params):
        requests = self._cancela_dict_to_cancela_params(params)
        responses = []
        for reserve in requests:
            passagem = reserve.get("passagem")
            cancelar_venda_params = reserve.get("cancelar_venda_params")
            try:
                responses.append(self._cancelar_venda(cancelar_venda_params))
            except Exception as ex:
                if not passagem.erro_cancelamento and not isinstance(ex, RodoviariaConnectionError):
                    self._save_error_cancelamento_passagens(
                        [request.get("passagem") for request in requests], error_str(ex)
                    )
                raise ex
            msg = f"Localizador - Guichepass passagem {cancelar_venda_params['id']} foi cancelada"
            buserlogger.info(msg)
            passagem.save_canceled()
        return responses

    def desbloquear_poltronas(self, trecho_classe_id: int, poltronas: list[int]) -> dict:
        for poltrona in poltronas:
            dados_poltrona_bloqueada: models.InfosCacheaveisBloqueioPoltrona = self.cache.get_poltrona_bloqueada_cache(
                trecho_classe_id, poltrona
            )
            params_desbloqueio = {"id": dados_poltrona_bloqueada.reserva_id}
            self._cancelar_venda(params_desbloqueio)
            self.cache.delete_poltrona_bloqueada_cache(trecho_classe_id, poltrona)

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=False):
        requests = self._reserva_dict_to_comprar_params(params)
        responses = []
        passagens_confirmadas = []
        for reserve in requests:
            passagem = reserve["passagem"]
            confirmar_venda_form = reserve["confirmar_venda_form"]
            try:
                comprar_response = self._confirmar_venda(confirmar_venda_form)
                localizador = comprar_response["ticketNumber"]
            except Exception as ex:
                solicita_cancelamento_passagens(passagens_confirmadas)
                passagem.save_error(error_str(ex))
                raise ex
            passagem.localizador = localizador
            self._atribui_bpe(passagem, comprar_response)
            passagem.provider_data = comprar_response
            passagem.save_confirmed()
            passagens_confirmadas.append(passagem)
            responses.append(passagem.to_dict_json())
        return {"passagens": responses}

    def _atribui_bpe(self, passagem, response):
        bpe_info = response.get("bpeInfo")
        if bpe_info:
            passagem.data_autorizacao = (
                to_default_tz(datetime.fromisoformat(bpe_info["bpeAuthorizationDate"]))
                if bpe_info.get("bpeAuthorizationDate")
                else None
            )
            passagem.bpe_qrcode = bpe_info.get("bpeQrCode")
            passagem.bpe_monitriip_code = bpe_info.get("bpeMonitriipCode")
            passagem.outros_tributos = bpe_info.get("otherTributes")
            passagem.chave_bpe = bpe_info.get("bpeAccessKey")[3:] if bpe_info.get("bpeAccessKey") else None
            passagem.numero_bpe = bpe_info.get("bpeNumber")
            passagem.serie_bpe = bpe_info.get("bpeSeries")
            passagem.protocolo_autorizacao = bpe_info.get("bpeAuthProtocol")
            passagem.prefixo = bpe_info.get("prefix")
            passagem.numero_bilhete = response.get("ticketNumber")
        price_info = response.get("priceInfo")
        if price_info:
            passagem.preco_base = self._sanitize_preco(price_info.get("basePrice"))
            passagem.taxa_embarque = self._sanitize_preco(price_info.get("boardingPrice"))
            passagem.seguro = self._sanitize_preco(price_info.get("insurancePrice"))
            passagem.pedagio = self._sanitize_preco(price_info.get("tollPrice"))
            passagem.outras_taxas = self._sanitize_preco(price_info.get("otherPrice"))
            passagem.preco_rodoviaria = self._sanitize_preco(price_info.get("price"))
        passagem.save()

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        (_, mapa_poltronas_categoria) = self._get_map_poltronas_and_ticket_types(
            self._verifica_poltrona_params(trecho_classe), categoria_especial
        )
        self._bloquear_poltronas(trecho_classe, [poltrona], mapa_poltronas_categoria)
        dados_bloqueio_poltrona: models.InfosCacheaveisBloqueioPoltrona = self.cache.get_poltrona_bloqueada_cache(
            trechoclasse_id, poltrona
        )
        return BloquearPoltronasResponse(
            seat=poltrona, best_before=now() + timedelta(minutes=20), external_payload=dados_bloqueio_poltrona
        )

    def verifica_poltrona(self, verifica_poltronas_params: VerificarPoltronaForm):
        trecho_classe = self.get_active_trecho_classe(verifica_poltronas_params.trechoclasse_id)
        params = self._verifica_poltrona_params(trecho_classe)
        (mapa_poltronas, mapa_poltronas_categoria) = self._get_map_poltronas_and_ticket_types(
            params, verifica_poltronas_params.categoria_especial
        )
        poltronas = poltronas_svc.seleciona_poltrona(mapa_poltronas, verifica_poltronas_params.passageiros)
        self._bloquear_poltronas(trecho_classe, poltronas, mapa_poltronas_categoria)
        return poltronas

    def _get_map_poltronas_and_ticket_types(
        self, params: models.RetornaPoltronasForm, categoria_especial: Passagem.CategoriaEspecial
    ):
        categoria_especial = categoria_especial or Passagem.CategoriaEspecial.NORMAL
        onibus = endpoints.retorna_poltronas_request(self.login, params)
        for cota in onibus.cotas:
            if (
                self.PASSAGEM_TYPE_MAP[categoria_especial] == cota.categoria_external
                and cota.tipo_cota == "DYNAMIC_QUOTAS"
            ):
                # se a categoria_especial tem assentos dinâmicos, pode selecionar qualquer poltrona livre
                map_poltronas = {p.number: "livre" if p.status == "FREE" else "ocupada" for p in onibus.poltronas}
                mapa_poltronas_categoria = defaultdict(lambda cota=cota: cota)
                return OrderedDict(sorted(map_poltronas.items())), mapa_poltronas_categoria
        map_poltronas = {}
        mapa_poltronas_categoria = {}
        # gera o mapa apenas com poltronas da categoria desejada
        for poltrona in onibus.poltronas:
            poltrona_status = "livre" if poltrona.status == "FREE" else "ocupada"
            for categoria in poltrona.categorias:
                if self.PASSAGEM_TYPE_MAP[categoria_especial] == categoria.categoria_external:
                    map_poltronas[poltrona.number] = poltrona_status
                    mapa_poltronas_categoria[poltrona.number] = categoria
                    break
        return OrderedDict(sorted(map_poltronas.items())), mapa_poltronas_categoria

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        params = self._verifica_poltrona_params(trecho_classe)
        onibus = endpoints.retorna_poltronas_request(self.login, params)

        # se a categoria_especial tem assentos dinâmicos, pode selecionar qualquer poltrona livre
        categorias_universais = {self.CATEGORIA_ESPECIAL_MAP.get(cota.categoria_external)
         for cota in onibus.cotas if cota.tipo_cota == "DYNAMIC_QUOTAS"}

        tipo_assento_parceiro = self.tipos_de_onibus_map[json.loads(trecho_classe.provider_data).get("busType")]
        tipo_assento_buser = get_buser_class_by_company(self.company, tipo_assento_parceiro)

        decks_map = {}
        for poltrona in onibus.poltronas:
            categoria_especial = {Passagem.CategoriaEspecial.NORMAL}.union(categorias_universais)
            if poltrona.categorias:
                for categoria in poltrona.categorias:
                    categoria_especial_external = self.CATEGORIA_ESPECIAL_MAP.get(categoria.categoria_external)
                    if categoria_especial_external:
                        categoria_especial.add(categoria_especial_external)
            if poltrona.z not in decks_map:
                decks_map[poltrona.z] = Deck(andar=poltrona.z + 1, assentos=[])
            decks_map[poltrona.z].assentos.append(
                Assento.parse_obj(
                    {
                        "livre": poltrona.status == "FREE",
                        "x": poltrona.y + 1,
                        "y": poltrona.x + 1,
                        "numero": poltrona.number,
                        "tipo_assento": tipo_assento_buser,
                        "categoria_especial": list(categoria_especial),
                    }
                )
            )
        return Onibus(layout=list(decks_map.values()))

    def _bloquear_poltronas(
        self,
        trecho_classe: TrechoClasse,
        poltronas: list[int],
        mapa_poltronas_categoria: dict[int, models.ListQuota | models.TicketTypeItem],
    ):
        trecho_classe_id = trecho_classe.trechoclasse_internal_id
        retorna_poltronas_form: models.RetornaPoltronasForm = self._verifica_poltrona_params(trecho_classe)
        poltronas_bloqueadas = []
        for poltrona in poltronas:
            dict_params = retorna_poltronas_form.dict(by_alias=True)
            dict_params["seat"] = poltrona
            params_bloquear_poltrona = models.BloquearPoltronaForm.parse_obj(dict_params)
            try:
                bloqueio = self._bloquear_poltrona(params_bloquear_poltrona)
                poltronas_bloqueadas.append(poltrona)
                reserva_id = bloqueio["id"]
                infos_cacheaveis = models.InfosCacheaveisBloqueioPoltrona(
                    reserva_id=reserva_id,
                    categoria_external_id=mapa_poltronas_categoria[poltrona].categoria_external_id,
                    preco=mapa_poltronas_categoria[poltrona].preco,
                )
                self.cache.set_poltrona_bloqueada_cache(trecho_classe_id, poltrona, infos_cacheaveis)
            except Exception:
                async_desbloquear_poltronas.delay(self.company.id, trecho_classe_id, poltronas_bloqueadas)
                raise
        return poltronas_bloqueadas

    def atualiza_origens(self):
        resp = endpoints.atualiza_origens_request(self.login)
        return resp

    def buscar_itinerario(self, params):
        # endpoint = endpoints.BUSCA_ITINERARIO
        pass

    def buscar_corridas(self, request_params, match_params=None):
        resp = endpoints.buscar_corridas_request(self.login, request_params)
        servicos = resp

        matches = []
        mismatches = []

        corridas_form = self._make_corridas_form(servicos)
        if not match_params:
            return corridas_form

        datetime_ida, timezone, tipo_assento = (
            match_params["datetime_ida"],
            match_params["timezone"],
            match_params["tipo_assento"],
        )
        buser_class = tipo_assento

        for servico in servicos:
            api_class = self.tipos_de_onibus_map[servico["busType"]]
            datetime_ida_servico = datetime.strptime(servico["departure"], "%Y-%m-%dT%H:%M:%S")
            match_horario = self._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
            match_classe = self._does_class_match(buser_class, api_class)
            if match_horario and match_classe:
                matches.append(servico)
            else:
                mismatches.append(servico)

        if not matches:
            return self._parse_retorno_buscar_servico(mismatches=mismatches, timezone=timezone)

        if len(matches) == 1:
            match = matches[0]
        else:
            match = sorted(
                matches,
                key=lambda servico: (
                    self._get_diff_datetime_ida_in_minutes(
                        datetime_ida, timezone, datetime.strptime(servico["departure"], "%Y-%m-%dT%H:%M:%S")
                    ),
                    -servico["freeSeats"],
                ),
            )[0]

        return self._parse_retorno_buscar_servico(servico_encontrado=match, timezone=timezone)

    def _make_corridas_form(self, corridas):
        found = bool(corridas)
        servicos = []

        for corrida in corridas:
            servico = ServicoForm(
                provider_data=corrida,
                external_id=corrida["service"],
                tipo_veiculo=corrida["busType"],
                classe=self.tipos_de_onibus_map[corrida["busType"]],
                vagas=corrida["freeSeats"],
                preco=corrida["price"],
                desconto=corrida["companyDiscount"],
                external_datetime_ida=datetime.strptime(corrida["departure"], "%Y-%m-%dT%H:%M:%S"),
            )
            servicos.append(servico)

        return BuscarServicoForm(found=found, servicos=servicos)

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        params = {"id": passagem.numero_passagem}

        bilhete_padrao = endpoints.consultar_bilhete_request(self.login, params)
        bilhete_padrao.numero_passagem = passagem.numero_passagem
        bilhete_padrao.empresa_name = passagem.company_integracao.name
        return bilhete_padrao

    def vagas_por_categoria_especial(self, trechoclasse_id):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        request_params: models.RetornaPoltronasForm = self._verifica_poltrona_params(trecho_classe)
        onibus = endpoints.retorna_poltronas_request(self.login, request_params)

        assentos_por_categoria = defaultdict(int)
        # adiciona vagas da lista de cotas
        for cota in onibus.cotas:
            categoria_especial = self.CATEGORIA_ESPECIAL_MAP.get(cota.categoria_external)
            if not categoria_especial:
                continue
            if cota.tipo_cota == "DYNAMIC_QUOTAS":
                assentos_por_categoria[categoria_especial] = cota.quantidade

        # adiciona vagas verificando cada TicketType das poltronas
        for poltrona in onibus.poltronas:
            if poltrona.status != "FREE":
                continue
            if not poltrona.categorias:
                assentos_por_categoria[Passagem.CategoriaEspecial.NORMAL] += 1
                continue
            for categoria in poltrona.categorias:
                categoria_especial = self.CATEGORIA_ESPECIAL_MAP.get(categoria.categoria_external)
                if not categoria_especial:
                    continue
                assentos_por_categoria[categoria_especial] += 1

        return assentos_por_categoria


def lista_empresas_api(login_params: GuichepassAnonymousLogin):
    company = GuichepassUndefinedCompany(url_base=login_params.url_base)
    login = GuichepassUndefinedCompanyLogin(
        url_base=login_params.url_base,
        client_id=login_params.client_id,
        username=login_params.username,
        password=login_params.password,
        company=company,
    )
    resp = endpoints.consultar_empresas_request(login)
    return resp


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas(company_rodoviaria_id: int, trecho_classe_id: int, poltronas: list[int]):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = GuichepassAPI(company)
    api.desbloquear_poltronas(trecho_classe_id, poltronas)
