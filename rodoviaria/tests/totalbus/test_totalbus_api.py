import json
from datetime import date, datetime, timedelta
from decimal import Decimal as D
from http import HTTPStatus
from types import SimpleNamespace
from unittest import mock
from unittest.mock import ANY, Mock

import pytest
import pytest_asyncio
import requests
import responses
import time_machine
from django.utils.timezone import now
from model_bakery import baker
from pydantic import ValidationError, parse_obj_as
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz, today_midnight
from commons.django_utils import error_str
from rodoviaria import views
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.totalbus import api as totalbus_api
from rodoviaria.api.totalbus import endpoints as totalbus_endpoints
from rodoviaria.api.totalbus import models
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.api.totalbus.exceptions import TotalbusCategoriaNotFound, TotalbusItinerarioNotFound
from rodoviaria.api.totalbus.memcache import TotalbusMC
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasResponse,
    ComprarForm,
    PassageiroForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.forms.staff_forms import TotalbusNoCompanyLogin
from rodoviaria.models import Passagem
from rodoviaria.models.core import Cidade, Company, CompanyCategoriaEspecial, LocalEmbarque, TrechoClasse
from rodoviaria.service import class_match_svc, novos_modelos_svc, reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    CampoObrigatorioException,
    PassengerNotRegistered,
    PassengerTicketAlreadyPrintedException,
    PoltronaExpirada,
    PoltronaJaSelecionadaException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaOverbookingException,
    RodoviariaTooManyRequestsError,
    RodoviariaTrechoBloqueadoException,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaTrechoclasseNotFoundException,
    RodoviariaTrechoNotFoundException,
    RodoviariaUnauthorizedError,
)
from rodoviaria.tests.totalbus import mocker
from rodoviaria.tests.totalbus import mocker as mocker_totalbus
from rodoviaria.tests.utils_testes import _comprar_params


@pytest.fixture
def company_categorias_especiais(totalbus_api):
    return [
        baker.make(
            "rodoviaria.CompanyCategoriaEspecial",
            company=totalbus_api.company,
            categoria_id_external=1,
            descricao_external="Assento Anormal",
            categoria_especial=Passagem.CategoriaEspecial.NORMAL,
        ),
        baker.make(
            "rodoviaria.CompanyCategoriaEspecial",
            company=totalbus_api.company,
            categoria_id_external=33,
            descricao_external="Benefício PCD",
            categoria_especial=Passagem.CategoriaEspecial.PCD,
        ),
        baker.make(
            "rodoviaria.CompanyCategoriaEspecial",
            company=totalbus_api.company,
            categoria_id_external=30,
            descricao_external="Espaço Mulher",
            categoria_especial=Passagem.CategoriaEspecial.ESPACO_MULHER,
        ),
    ]


@pytest.fixture
def passagens_registradas(totalbus_trechoclasses):
    passagem_registrada1 = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        poltrona_external_id=32,
        status="confirmada",
    )
    passagem_registrada2 = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        poltrona_external_id=42,
        status="confirmada",
    )
    yield passagem_registrada1, passagem_registrada2
    passagem_registrada1.delete()
    passagem_registrada2.delete()


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_bloquear_poltronas_v2(mock_bloquear_poltrona_venda_normal, totalbus_api, totalbus_trechoclasses):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    expected_best_before = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")) + timedelta(
        minutes=10
    )
    poltrona = 18
    result = totalbus_api.bloquear_poltronas_v2(trecho_classe_id, poltrona, Passagem.CategoriaEspecial.NORMAL)
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    expected_cache = parse_obj_as(
        list[models.InfosCacheaveisBloqueioPoltrona],
        [
            {
                "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
                "preco": D("589.30"),
                "origem": "FORTALEZA - CE",
                "destino": "SOBRAL - CE",
                "data_hora_partida": "2023-12-28 22:45",
            }
        ],
    )
    assert mc.get_poltrona_key_cache(trecho_classe_id, poltrona) == expected_cache
    assert result == BloquearPoltronasResponse(
        seat=poltrona,
        best_before=expected_best_before,
        external_payload=expected_cache,
    )


def test_bloquear_poltronas(mock_bloquear_poltrona_venda_normal, totalbus_api, totalbus_trechoclasses):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18]
    totalbus_api.bloquear_poltronas(trecho_classe_id, poltronas)
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    assert mc.get_poltrona_key_cache(trecho_classe_id, 18) == parse_obj_as(
        list[models.InfosCacheaveisBloqueioPoltrona],
        [
            {
                "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
                "preco": D("589.30"),
                "origem": "FORTALEZA - CE",
                "destino": "SOBRAL - CE",
                "data_hora_partida": "2023-12-28 22:45",
            }
        ],
    )


def test_bloquear_poltronas_categoria_especial(
    mock_bloquear_poltrona_venda_normal, totalbus_api, totalbus_trechoclasses
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18]
    baker.make(
        CompanyCategoriaEspecial,
        company=totalbus_api.company,
        categoria_id_external=30,
        categoria_especial=Passagem.CategoriaEspecial.IDOSO_100,
        descricao_external="IDOSO 100%",
    )
    totalbus_api.bloquear_poltronas(
        trecho_classe_id, poltronas, categoria_especial=Passagem.CategoriaEspecial.IDOSO_100
    )
    assert json.loads(mock_bloquear_poltrona_venda_normal._requests[0][1].content)["categoriaId"] == "30"


def test_bloquear_poltronas_poltronas_ja_selecionada_add_no_cache(
    mock_bloquear_poltrona_erro_poltrona_selecionada,
    totalbus_api,
    totalbus_trechoclasses,
    cache_mock,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18]
    with pytest.raises(PoltronaJaSelecionadaException, match="A poltrona já foi selecionada"):
        totalbus_api.bloquear_poltronas(trecho_classe_id, poltronas)
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    assert mc.get_poltrona_indisponivel_cache(trecho_classe_id) == {18}


@mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.envia_poltronas_fila_desbloqueio")
def test_bloquear_poltronas_erro_desbloqueia_poltronas(
    mock_desbloquear_poltronas_async,
    mock_bloquear_poltrona_twice_sucesso_and_error,
    totalbus_api,
    totalbus_trechoclasses,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18, 19]
    with pytest.raises(RodoviariaException):
        totalbus_api.bloquear_poltronas(trecho_classe_id, poltronas)
    mock_desbloquear_poltronas_async.assert_called_once_with(trecho_classe_id, [ANY])


def test_bloquear_poltrona_venda_normal(mock_bloquear_poltrona_venda_normal, totalbus_api, totalbus_trechoclasses):
    totalbus_api.company.company_internal_id = 303
    totalbus_api.company.save()
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(
        transacao="TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
        preco=D("589.30"),
        origem="FORTALEZA - CE",
        destino="SOBRAL - CE",
        data_hora_partida="2023-12-28 22:45",
    )
    with mock.patch.object(TotalbusMC, "set_poltrona_key_cache") as mock_cache:
        totalbus_api.bloquear_poltronas(trecho_classe_id, [1])
    mock_cache.assert_called_with(trecho_classe_id, 1, [bloqueio], 10 * 60)


def test_bloquear_poltrona_erro_venda_impedida(
    mock_bloquear_poltrona_erro_venda_bloqueada, totalbus_api, totalbus_trechoclasses
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(RodoviariaTrechoBloqueadoException, match="Trecho bloqueado pelo parceiro"):
        totalbus_api.bloquear_poltronas(trecho_classe_id, [1])


def test_bloquear_poltrona_erro_venda_impedida_categoria_indisponivel(
    mock_bloquear_poltrona_erro_venda_bloqueada_categoria_indisponivel,
    totalbus_api,
    totalbus_trechoclasses,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(RodoviariaTrechoBloqueadoException, match="Trecho bloqueado pelo parceiro"):
        totalbus_api.bloquear_poltronas(trecho_classe_id, [1])


def test_bloquear_poltrona_erro_venda_impedida_tipo_passagem_sem_disponibilidad(
    mock_bloquear_poltrona_erro_venda_bloqueada_tipo_passagem_sem_disponibilidade,
    totalbus_api,
    totalbus_trechoclasses,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(RodoviariaTrechoBloqueadoException, match="Trecho bloqueado pelo parceiro"):
        totalbus_api.bloquear_poltronas(trecho_classe_id, [1])


def test_bloquear_poltrona_erro_poltrona_selecionada(
    mock_bloquear_poltrona_erro_poltrona_selecionada,
    totalbus_api,
    totalbus_trechoclasses,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(RodoviariaException, match="A poltrona já foi selecionada"):
        totalbus_api.bloquear_poltronas(trecho_classe_id, [1])


def test_desbloquear_poltronas(mock_desbloquear_poltrona, totalbus_api):
    trecho_classe_id = 1932
    poltrona = 15
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(transacao="transacao", preco=D("12.00"))
    mc.set_poltrona_key_cache(trecho_classe_id, poltrona, [bloqueio])
    resp = totalbus_api.desbloquear_poltronas(trecho_classe_id, [poltrona])
    assert resp == {}
    assert not mc.get_poltrona_key_cache(trecho_classe_id, poltrona)


def test_desbloquear_poltronas_remove_poltrona_cache_poltrona_indisponivel(mock_desbloquear_poltrona, totalbus_api):
    trecho_classe_id = 1932
    poltrona = 15
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(transacao="transacao", preco=D("12.00"))
    mc.set_poltrona_key_cache(trecho_classe_id, poltrona, [bloqueio])
    mc.set_poltrona_indisponivel_cache(trecho_classe_id, {poltrona})
    resp = totalbus_api.desbloquear_poltronas(trecho_classe_id, [poltrona])
    assert resp == {}
    assert not mc.get_poltrona_key_cache(trecho_classe_id, poltrona)
    assert poltrona not in mc.get_poltrona_indisponivel_cache(trecho_classe_id)


def test_async_desbloquear_poltronas(totalbus_login):
    trecho_classe_id = 1932
    poltrona = 15
    with mock.patch.object(TotalbusAPI, "desbloquear_poltronas") as mock_desbloquear_poltronas:
        totalbus_api.async_desbloquear_poltronas(totalbus_login.company.id, trecho_classe_id, [poltrona])
    mock_desbloquear_poltronas.assert_called_once_with(trecho_classe_id, [poltrona])


def test_atualiza_origens(mock_atualiza_origens, totalbus_api):
    resp = totalbus_api.atualiza_origens()
    assert isinstance(resp, list)
    assert resp == [
        models.Localidade(
            cidade="INHUMAS - GO",
            uf="GO",
            external_cidade_id=5650,
            external_local_id=5650,
            complemento=None,
        ),
        models.Localidade(
            uf="MG",
            cidade="BELO HORIZONTE",
            external_cidade_id=5410,
            external_local_id=5410,
            complemento=None,
        ),
    ]


@pytest.mark.parametrize("mock_buscar_origens_destinos", [{"company_external_id": 42}], indirect=True)
def test_busca_origens_e_destino_empresa(mock_buscar_origens_destinos, totalbus_api):
    totalbus_api.company.company_external_id = 42
    resp = totalbus_api.map_cidades_destinos()
    assert isinstance(resp, dict)
    mocked_response = mocker.BuscaOrigensDestinos.response()
    assert resp == {str(r["origem"]["id"]): {str(d["id"]) for d in r["destinos"]} for r in mocked_response}


def test_busca_origens_e_destino_empresa_company_external_id_none(
    mock_consultar_empresas_usando_base_url_obj,
    mock_buscar_origens_destinos_todas_empresas,
    totalbus_api,
):
    totalbus_api.company.company_external_id = None
    resp = totalbus_api.map_cidades_destinos()
    assert isinstance(resp, dict)
    mocked_response = mocker.BuscaOrigensDestinos.response()
    assert resp == {str(r["origem"]["id"]): {str(d["id"]) for d in r["destinos"]} for r in mocked_response}


@pytest.mark.parametrize("mock_buscar_destinos", [{"origem_external_id": 392412}], indirect=True)
def test_cidades_destino(mock_buscar_destinos, totalbus_api):
    resp = totalbus_api.cidades_destino(392412)
    assert isinstance(resp, list)
    assert resp == [r["id"] for r in mocker.BuscaDestinos.response()]


@pytest.mark.parametrize("mock_buscar_destinos", [{"origem_external_id": 392412}], indirect=True)
def test_cidades_destino_clear_cache(mock_buscar_destinos, totalbus_api):
    totalbus_api.cidades_destino(392412)
    assert mock_buscar_destinos.call_count == 1
    # requisições seguintes aproveitam cache
    totalbus_api.cidades_destino(392412)
    assert mock_buscar_destinos.call_count == 1


def test_buscar_itinerario(mock_buscar_itinerario, totalbus_api):
    resp = totalbus_api.buscar_itinerario(mocker.BuscarItinerarioCorrida.request())
    assert resp.raw == {
        "servico": "1010665",
        "data": "2020-05-26",
        "lsParadas": [
            {
                "localidade": {"id": 2063, "cidade": "FORTALEZA - CE", "uf": "CE"},
                "distancia": "69.7",
                "permanencia": "00:00",
                "data": "2020-05-26",
                "hora": "05:30",
            }
        ],
    }


def test_buscar_itinerario_nao_localizado(mock_buscar_itinerario_nao_localizado, totalbus_api):
    with pytest.raises(TotalbusItinerarioNotFound):
        totalbus_api.buscar_itinerario(mocker.BuscarItinerarioCorrida.request())


def test_buscar_bilhetes_por_numero_sistema(mock_buscar_bilhetes_por_numero_sistema, totalbus_api):
    params = mocker.BuscarBilhetes.request_by_numero_sistema()
    resp = totalbus_api.buscar_bilhetes(params)
    assert isinstance(resp, list)
    assert len(resp) == 2


def test_buscar_bilhetes_por_numero_sistema_nenhum_bilhete_encontrado(
    mock_buscar_bilhetes_por_numero_sistema_nenhum_bilhete_encontrado, totalbus_api
):
    params = mocker.BuscarBilhetes.request_by_numero_sistema()
    with pytest.raises(RodoviariaException, match=r"Não há bilhete para a consulta enviada"):
        totalbus_api.buscar_bilhetes(params)


def test_verifica_poltronas(totalbus_api):
    with (
        mock.patch.object(TotalbusAPI, "get_poltronas_livres", return_value=[30, 31]),
        mock.patch.object(TotalbusAPI, "bloquear_poltronas") as mock_bloquear_poltronas,
    ):
        totalbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=213, passageiros=1))
    mock_bloquear_poltronas.assert_called_once_with(213, [30, 31], Passagem.CategoriaEspecial.NORMAL)


def test_verifica_poltronas_retry(totalbus_api):
    with (
        mock.patch.object(TotalbusAPI, "get_poltronas_livres", return_value=[30, 31]),
        mock.patch.object(TotalbusAPI, "bloquear_poltronas") as mock_bloquear_poltronas,
        pytest.raises(PoltronaJaSelecionadaException),
    ):
        mock_bloquear_poltronas.side_effect = PoltronaJaSelecionadaException("irineu, voce nào sabe nem eu")
        totalbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=213, passageiros=1))
    assert mock_bloquear_poltronas.call_count == 3


def test_get_poltronas_livres_uma_poltrona(
    mock_get_poltronas_livres,
    totalbus_api,
    passagens_registradas,
    totalbus_trechoclasses,
):
    resp = totalbus_api.get_poltronas_livres(totalbus_trechoclasses.ida.trechoclasse_internal_id, 1, "normal")
    assert isinstance(resp, list)
    assert len(resp) == 1


def test_get_poltronas_livres_varias_poltronas(
    mock_get_poltronas_livres,
    totalbus_api,
    passagens_registradas,
    totalbus_trechoclasses,
):
    resp = totalbus_api.get_poltronas_livres(totalbus_trechoclasses.ida.trechoclasse_internal_id, 2, "normal")
    assert isinstance(resp, list)
    assert len(resp) == 2


def test_get_poltronas_livres_varias_poltronas_bordas(
    mock_get_poltronas_livres_bordas,
    totalbus_api,
    passagens_registradas,
    totalbus_trechoclasses,
):
    resp = totalbus_api.get_poltronas_livres(totalbus_trechoclasses.ida.trechoclasse_internal_id, 2, "normal")
    assert isinstance(resp, list)
    assert len(resp) == 2
    assert sorted(resp) == [1, 4]


def test_get_poltronas_livres_poltronas_insuficientes(
    mock_get_poltronas_livres,
    totalbus_api,
    passagens_registradas,
    totalbus_trechoclasses,
):
    with pytest.raises(
        RodoviariaOverbookingException,
        match="Apenas 9 poltronas disponíveis para esta viagem",
    ):
        totalbus_api.get_poltronas_livres(totalbus_trechoclasses.ida.trechoclasse_internal_id, 12, "normal")


def test_get_map_poltronas(mock_get_poltronas_livres, totalbus_api, totalbus_trechoclasses):
    resp = totalbus_api.get_map_poltronas(
        trecho_classe_id=totalbus_trechoclasses.ida.trechoclasse_internal_id, categoria_especial="normal"
    )
    assert resp == {
        # "WC": "ocupada",
        # "04": "ocupada",  categoriaId != -1
        # "32": "ocupada",  categoriaId != -1
        "31": "livre",
        "30": "livre",
        "40": "livre",
        "44": "livre",
        "43": "livre",
        "42": "livre",
        "41": "livre",
        "CF": "ocupada",
        "46": "livre",
        "45": "livre",
        "36": "livre",
    }


def test_get_map_poltronas_check_db_antes(
    mock_get_poltronas_livres,
    totalbus_api,
    totalbus_trechoclasses,
    passagens_registradas,
):
    resp = totalbus_api.get_map_poltronas(
        trecho_classe_id=totalbus_trechoclasses.ida.trechoclasse_internal_id, categoria_especial="normal"
    )

    assert resp == {
        # "WC": "ocupada",
        # "04": "ocupada",  categoriaId != -1
        # "32": "ocupada",  categoriaId != -1
        "31": "livre",
        "30": "livre",
        "40": "livre",
        "44": "livre",
        "43": "livre",
        "42": "ocupada",
        "41": "livre",
        "CF": "ocupada",
        "46": "livre",
        "45": "livre",
        "36": "livre",
    }


def test_get_map_poltronas_check_cache_antes(
    mock_get_poltronas_livres, totalbus_api, totalbus_trechoclasses, cache_mock
):
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    mc.insert_poltrona_indisponivel_cache(totalbus_trechoclasses.ida.trechoclasse_internal_id, "31")
    mc.insert_poltrona_indisponivel_cache(totalbus_trechoclasses.ida.trechoclasse_internal_id, "43")
    resp = totalbus_api.get_map_poltronas(
        trecho_classe_id=totalbus_trechoclasses.ida.trechoclasse_internal_id, categoria_especial="normal"
    )

    assert resp == {
        # "WC": "ocupada",
        # "04": "ocupada",  categoriaId != -1
        # "32": "ocupada",  categoriaId != -1
        "31": "ocupada",
        "30": "livre",
        "40": "livre",
        "44": "livre",
        "43": "ocupada",
        "42": "livre",
        "41": "livre",
        "CF": "ocupada",
        "46": "livre",
        "45": "livre",
        "36": "livre",
    }


def test_get_map_poltronas_trecho_not_found(totalbus_api, totalbus_trechoclasses):
    with pytest.raises(RodoviariaTrechoclasseNotFoundException) as exc:
        totalbus_api.get_map_poltronas(trecho_classe_id=999999, categoria_especial="normal")
    assert str(exc.value) == "Trechoclasse não encontrado no banco de dados"


def test_get_map_poltronas_erro(mock_poltronas_error, totalbus_api, totalbus_trechoclasses):
    with pytest.raises(RodoviariaTrechoclasseFactoryException, match=r"Serviço não encontrado na API"):
        totalbus_api.get_map_poltronas(
            trecho_classe_id=totalbus_trechoclasses.ida.trechoclasse_internal_id, categoria_especial="normal"
        )


def test_connection_error(mock_connection_error, totalbus_api, totalbus_trechoclasses):
    with pytest.raises(
        RodoviariaConnectionError,
        match="^totalbus .* connection error$",
    ):
        totalbus_api.get_map_poltronas(
            trecho_classe_id=totalbus_trechoclasses.ida.trechoclasse_internal_id, categoria_especial="normal"
        )


def test_buscar_todos_servicos(mock_buscar_todos_servicos, totalbus_api):
    cleaned_resp = totalbus_api.buscar_todos_servicos()
    assert cleaned_resp[0]["numservico"] == 1
    assert cleaned_resp[0]["origem"]["id"] == 21918


@pytest.mark.parametrize(
    "company_internal_id, modelo_venda, lista_servicos, expected",
    [
        (313, Company.ModeloVenda.HIBRIDO, [123456, 177788, 888877, 888888, 999977, 999988], [999977, 999988]),
        (
            313,
            Company.ModeloVenda.MARKETPLACE,
            [123456, 177788, 888877, 888888, 999977, 999988],
            [123456, 177788, 888877, 888888],
        ),
        (
            111,
            Company.ModeloVenda.MARKETPLACE,
            [123456, 177788, 888877, 888888, 999977, 999988],
            [123456, 177788, 999977, 999988, 888877, 888888],
        ),
        (novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID, Company.ModeloVenda.MARKETPLACE, [1234, 5678, 9012], [5678]),
        (282, Company.ModeloVenda.MARKETPLACE, [1234, 5678, 9012], [1234, 5678, 9012]),
    ],
)
def test_filtrar_servicos_hibridos_roderotas_totalbus(
    override_config, company_internal_id, modelo_venda, lista_servicos, expected
):
    servicos = [models.ServicoTrechoClasse(numservico=_id) for _id in lista_servicos]
    with override_config(SERVICOS_COMPRA_POLTRONAS_ADAMANTINA="5678,3456"):
        servicos_filtrados = novos_modelos_svc.filter_servicos_novos_modelos(
            servicos, company_internal_id, modelo_venda
        )
    servicos_id = [s.external_id for s in servicos_filtrados]
    assert sorted(servicos_id) == sorted(expected)


def test_buscar_todos_servicos_vazio(mock_buscar_todos_servicos_vazio, totalbus_api):
    with pytest.raises(KeyError, match=r"servicos"):
        resp = totalbus_api.buscar_todos_servicos()
        assert resp.cleaned


def test_buscar_todos_servicos_erro(mock_buscar_todos_servicos_erro, totalbus_api):
    with pytest.raises(
        RodoviariaException,
        match="totalbus 'POST http://totalbus.base/catalogo/buscarServicos' json=None params=None status_code=400",
    ):
        totalbus_api.buscar_todos_servicos()


def test_buscar_servico(mock_buscar_servico, totalbus_api, totalbus_grupos_mockado):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    servico = resp.servicos[0]

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    assert servico.provider_data["poltronasTotal"] == 12
    assert servico.external_datetime_ida == to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))
    assert servico.external_id == "800"
    assert servico.tipo_veiculo is None
    assert servico.preco == D("145.41")
    assert servico.classe == "LEITO CAMA"


def test_buscar_servico_hibrido_mismatch_sem_servico_hibrido(
    mock_buscar_servico, totalbus_api, totalbus_grupos_mockado
):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }

    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.company_internal_id = 313
    totalbus_api.company.save()
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    assert resp == BuscarServicoForm.parse_obj(
        {
            "found": False,
            "servicos": [],
        }
    )


def test_buscar_servico_hibrido_mismatch_com_servico_hibrido(
    mock_buscar_servico_com_hibrido_mismatch, totalbus_api, totalbus_grupos_mockado
):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.company_internal_id = 313
    totalbus_api.company.save()
    servico_hibrido = "999911"
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    assert resp.found is False
    assert len(resp.servicos) == 1
    assert resp.servicos[0].external_id == servico_hibrido


def test_buscar_servico_hibrido_match_com_servico_hibrido(
    mock_buscar_servico_com_hibrido, totalbus_api, totalbus_grupos_mockado
):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.company_internal_id = 313
    totalbus_api.company.save()
    servico_hibrido = "999911"
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    assert resp.found is True
    assert len(resp.servicos) == 1
    assert resp.servicos[0].external_id == servico_hibrido


def test_buscar_servico_match_com_servico_novo_modelo_adamantina(
    requests_mock,
    mock_buscar_servico_com_hibrido,
    mock_consultar_categoria_servico_buser,
    totalbus_api,
    totalbus_grupos_mockado,
    override_config,
):
    baker.make(
        CompanyCategoriaEspecial,
        company=totalbus_api.company,
        categoria_id_external=30,
        categoria_especial=Passagem.CategoriaEspecial.NORMAL,
    )
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    totalbus_api.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    totalbus_api.company.company_internal_id = novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID
    totalbus_api.company.save()
    servico_novo_modelo = "999911"
    with override_config(SERVICOS_COMPRA_POLTRONAS_ADAMANTINA="999911"):
        resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    assert resp.found is True
    assert len(resp.servicos) == 1
    assert resp.servicos[0].external_id == servico_novo_modelo
    assert resp.servicos[0].vagas == 3
    assert resp.servicos[0].capacidade_classe == 4
    # assert usou a data de inicio da viagem ao inves da data buscada
    assert json.loads(requests_mock.calls[1][0].body)["data"] == "2021-05-13"


def test_buscar_servico_retorno_mismatch(totalbus_api):
    timezone = "America/Sao_Paulo"
    servico = ServicoForm(
        external_id="2131",
        preco=D("200.50"),
        external_datetime_ida="2023-01-20T10:15:00",
        classe="semi leito",
        vagas=13,
    )
    retorno = totalbus_api._parse_retorno_buscar_servico([], timezone, [servico])
    assert retorno == BuscarServicoForm.parse_obj(
        {
            "found": False,
            "servicos": [servico],
        }
    )


def test_padrao_buscar_servico_sucesso(totalbus_api, mock_buscar_servicos_detalhado):
    data_inicial = datetime(2024, 1, 25, 19, 0)
    data_final = datetime(2024, 1, 27, 0, 0)
    resp = totalbus_api.buscar_viagens_por_periodo(data_inicial, data_final, 42)

    expected_ids = [int(serv["servicoId"]) for serv in mocker.BuscarServicosDetalhado.response()]
    itinerarios_id = [itinerario.external_id for itinerario in resp]
    assert sorted(itinerarios_id) == sorted(expected_ids)


def test_padrao_buscar_servico_connection_error(totalbus_api, mock_buscar_servicos_detalhado_jdbc_connection_error):
    data_inicial = datetime(2024, 1, 25, 19, 0)
    data_final = datetime(2024, 1, 27, 0, 0)
    with pytest.raises(RodoviariaConnectionError):
        totalbus_api.buscar_viagens_por_periodo(data_inicial, data_final, 42)


def test_padrao_buscar_servico_data_invalida(totalbus_api, mock_padrao_buscar_servico_data_input_invalida):
    company = totalbus_api.company
    company.company_external_id = 42
    company.save()
    with pytest.raises(
        RodoviariaException,
        match="Data inválida. Verifique se a data informada está no padrão YYYY-MM-DD HH:mm",
    ):
        totalbus_api.buscar_viagens_por_periodo(datetime(2024, 1, 25, 19, 0), datetime(2024, 1, 27, 0, 0))


def test_padrao_buscar_servico_sem_company_external_id(totalbus_api):
    company = totalbus_api.company
    company.company_external_id = None
    company.save()
    with pytest.raises(
        ValueError,
        match=(
            "Função deve receber company_external_id como argumento ou empresa deve ter company_external_id cadastrado"
        ),
    ):
        totalbus_api.buscar_viagens_por_periodo(datetime(2024, 1, 25, 19, 0), datetime(2024, 1, 27, 0, 0))


def test_reserva_dict_to_comprar_params_cached_poltrona(totalbus_api, totalbus_trechoclasses, cache_mock):
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    params = _comprar_params(trechoclasse_id, categoria_especial=Passagem.CategoriaEspecial.IDOSO_50, valor_cheio=preco)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(
        transacao="transacao",
        preco=D("167.05"),
        origem="ORIGEM",
        destino="DESTINO",
        data_hora_partida="2023-12-28 22:45",
    )
    for p in params.poltronas:
        mc.set_poltrona_key_cache(
            trechoclasse_id,
            p,
            [bloqueio],
        )
    reserva_params = totalbus_api._reserva_dict_to_comprar_params(totalbus_trechoclasses.ida, params)
    assert reserva_params[0]["confirmar_venda_form"].id_forma_pagamento == 23
    assert reserva_params[0]["confirmar_venda_form"].forma_pagamento == "BUSER"
    assert reserva_params[0]["confirmar_venda_form"].data_nascimento == "10/10/1950"
    assert reserva_params[0]["passagem"].pedido_external_id == "transacao"
    assert reserva_params[0]["passagem"].preco_rodoviaria == D("167.05")
    passagem = Passagem.objects.get(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert passagem.origem == "ORIGEM"
    assert passagem.destino == "DESTINO"
    assert passagem.data_hora_partida == "2023-12-28 22:45"
    assert passagem.status == Passagem.Status.INCOMPLETA
    assert passagem.categoria_especial == Passagem.CategoriaEspecial.IDOSO_50


@pytest.mark.parametrize(
    "preco_rodoviaria,valor_desconto",
    [(D("140.00"), D("40.00")), (D("100.00"), D("0")), (D("60.00"), D("0"))],
)
def test_reserva_dict_to_comprar_params_hibrido_valor_desconto_positivo(
    totalbus_api, totalbus_trechoclasses, cache_mock, preco_rodoviaria, valor_desconto
):
    preco_conexao = preco_rodoviaria - valor_desconto
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.save()
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(
        transacao="transacao", preco=preco_rodoviaria, preco_conexao=preco_conexao, is_conexao=True
    )
    for p in params.poltronas:
        mc.set_poltrona_key_cache(trechoclasse_id, p, [bloqueio])
    reserva_params = totalbus_api._reserva_dict_to_comprar_params(totalbus_trechoclasses.ida, params)
    assert reserva_params[0]["confirmar_venda_form"].valor_desconto == valor_desconto
    assert reserva_params[0]["confirmar_venda_form"].dict(by_alias=True)["valorDesconto"] == valor_desconto


def test_reserva_dict_to_comprar_params_bloqueia_poltrona(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    cache_mock,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    reserva_params = totalbus_api._reserva_dict_to_comprar_params(totalbus_trechoclasses.ida, params)

    assert reserva_params[0]["confirmar_venda_form"].id_forma_pagamento == 23
    assert reserva_params[0]["confirmar_venda_form"].forma_pagamento == "BUSER"
    assert reserva_params[0]["passagem"].pedido_external_id == "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA"
    assert reserva_params[0]["passagem"].preco_rodoviaria == D("589.30")


def test_reserva_dict_to_comprar_params_extra_poltronas(
    totalbus_api,
    totalbus_trechoclasses,
):
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=totalbus_trechoclasses.ida.preco_rodoviaria)
    params.extra_poltronas = [
        models.InfosCacheaveisBloqueioPoltrona(
            transacao="transacao_1", preco=totalbus_trechoclasses.ida.preco_rodoviaria
        ),
        models.InfosCacheaveisBloqueioPoltrona(
            transacao="transacao_2", preco=totalbus_trechoclasses.ida.preco_rodoviaria
        ),
    ]
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    reserva_params = totalbus_api._reserva_dict_to_comprar_params(totalbus_trechoclasses.ida, params)

    assert reserva_params[0]["confirmar_venda_form"].id_forma_pagamento == 23
    assert reserva_params[0]["confirmar_venda_form"].forma_pagamento == "BUSER"
    assert reserva_params[0]["passagem"].pedido_external_id == "transacao_1"
    assert reserva_params[0]["passagem"].preco_rodoviaria == totalbus_trechoclasses.ida.preco_rodoviaria


def test_comprar(totalbus_api, totalbus_trechoclasses, mock_comprar, cache_mock):
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(
        transacao="transacao", preco=totalbus_trechoclasses.ida.preco_rodoviaria
    )
    for p in params.poltronas:
        mc.set_poltrona_key_cache(
            trechoclasse_id,
            p,
            [bloqueio],
        )
    totalbus_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.preco_rodoviaria == totalbus_trechoclasses.ida.preco_rodoviaria
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == int(mocker.ConfirmarVenda.response()["poltrona"])
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "171007"
    assert passagem.company_integracao == totalbus_trechoclasses.ida.grupo.company_integracao
    assert passagem.linha == "BELO HORIZONTE/ARAÇUAÍ"


def assert_compra_erro(trechoclasse_id, erro):
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == erro
    assert passagem.pedido_external_id == "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA"
    assert passagem.status == Passagem.Status.ERRO


@pytest.fixture
def mock_async_desbloquear_poltronas():
    with mock.patch("rodoviaria.api.totalbus.api.async_desbloquear_poltronas.delay") as _mock:
        yield _mock


@pytest_asyncio.fixture
async def mock_envia_poltronas_fila_desbloqueio_async():
    with mock.patch.object(TotalbusAPI, "envia_poltronas_fila_desbloqueio_async") as _mock:
        yield _mock


def test_comprar_erro_categoria_indisponivel(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar_categoria_indisponivel,
    mock_envia_poltronas_fila_desbloqueio_async,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    with pytest.raises(RodoviariaConnectionError):
        totalbus_api.comprar(params)
    assert_compra_erro(trechoclasse_id, "RE-04|Categoria indisponível para esse serviço.")


def test_comprar_erro_asiento(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar_asiento,
    mock_envia_poltronas_fila_desbloqueio_async,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    with pytest.raises(RodoviariaException):
        totalbus_api.comprar(params)
    assert_compra_erro(trechoclasse_id, "A poltrona selecionada para compra não está mais disponível")


def test_comprar_erro_campo_obrigatorio(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar_data_nascimento_obrigatorio,
    mock_envia_poltronas_fila_desbloqueio_async,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    with pytest.raises(CampoObrigatorioException):
        totalbus_api.comprar(params)
    assert_compra_erro(trechoclasse_id, "Data nascimento do passageiro é obrigatório")


def test_comprar_erro_confirmar_venda(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar_com_erro,
    mock_envia_poltronas_fila_desbloqueio_async,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    with pytest.raises(PoltronaExpirada, match=r"A poltrona selecionada para compra não está bloqueada"):
        totalbus_api.comprar(params)
    assert_compra_erro(trechoclasse_id, "A poltrona selecionada para compra não está bloqueada")


def test_comprar_connection_error_confirmar_venda(
    totalbus_api,
    totalbus_trechoclasses,
    mock_bloquear_poltrona_venda_normal,
    mock_envia_poltronas_fila_desbloqueio_async,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, valor_cheio=preco)
    with pytest.raises(
        RodoviariaConnectionError,
        match=r"^totalbus .* connection error$",
    ):
        totalbus_api.comprar(params)
    assert_compra_erro(
        trechoclasse_id,
        "totalbus http://totalbus.base/confirmavenda/confirmarVenda connection error",
    )


def test_mock_comprar_erro_desbloqueia_poltronas_e_cancela_confirmada(
    totalbus_api, totalbus_trechoclasses, mock_comprar_duas_vezes_com_erro_na_segunda
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2, valor_cheio=preco)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(transacao="transacao", preco=D("167.05"))
    for p in params.poltronas:
        mc.set_poltrona_key_cache(trechoclasse_id, p, [bloqueio])
    poltronas = params.poltronas.copy()
    with (
        pytest.raises(RodoviariaException, match=r"A poltrona selecionada para compra não está bloqueada"),
        mock.patch.object(TotalbusAPI, "desbloquear_poltronas") as mock_desbloquear_poltronas,
    ):
        totalbus_api.comprar(params)

    # Se uma compra der erro ela deve:
    # 1. desbloquear as poltronas das passagens que deram erro
    mock_desbloquear_poltronas.assert_called_once_with(trechoclasse_id, [poltronas[1]])
    # 2. cancelar as passagens confirmadas
    passagens_confirmadas = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id,
        status=Passagem.Status.CONFIRMADA,
    )
    assert len(passagens_confirmadas) == 1
    assert "cancelamento_pendente" in passagens_confirmadas.first().tags_set()


def test_buscar_servico_horario_fora_da_tolerancia(mock_buscar_servico, totalbus_api, totalbus_grupos_mockado):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 19:05", "%Y-%m-%d %H:%M"))
    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semileito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert isinstance(resp.servicos, list)
    assert resp.found is False
    assert len(resp.servicos) == 5


def test_buscar_servico_tipo_assento_incorreto(mock_buscar_servico, totalbus_api, totalbus_grupos_mockado):
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:05", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semileito"

    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert isinstance(resp.servicos, list)
    assert resp.found is False
    assert len(resp.servicos) == 5


def _add_pax_params(trecho_classe_internal_id, valor_buseiro=158.6):
    params = SimpleNamespace()
    passenger = SimpleNamespace()

    passenger.buseiro_id = 1972911
    passenger.cpf = "85891347334"
    passenger.name = "Tony Calleri França"
    passenger.rg_number = "5368700"
    passenger.phone = "1291822398"
    passenger.buyer_cpf = "1291822398"

    params.id_destino = 651
    params.id_origem = 903
    params.passenger = passenger
    params.travel_id = 4558441
    params.trechoclasse_id = trecho_classe_internal_id
    params.valor_por_buseiro = valor_buseiro
    return params


def test_add_pax_na_lista_passageiros_viagem_pax_novo(
    mock_bloquear_poltrona_venda_normal,
    mock_comprar,
    totalbus_api,
    totalbus_trechoclasses,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    passagem_count = Passagem.objects.count()
    params = _add_pax_params(totalbus_trechoclasses.ida.trechoclasse_internal_id, valor_buseiro=preco)
    params.poltrona = 1
    resp = totalbus_api.add_pax_na_lista_passageiros_viagem(params)
    new_passagem_count = Passagem.objects.count()

    assert resp == {"sucesso": True, "already_on_api": False}
    assert new_passagem_count == passagem_count + 1


def test_add_pax_na_lista_passageiros_viagem_pax_existente(totalbus_api, totalbus_trechoclasses):
    passagem = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        buseiro_internal_id=1972911,
        travel_internal_id=4558441,
        trechoclasse_integracao__trechoclasse_internal_id=81273,
    )
    passagem_count = Passagem.objects.count()
    params = _add_pax_params(81273)
    resp = totalbus_api.add_pax_na_lista_passageiros_viagem(params)
    new_passagem_count = Passagem.objects.count()

    assert resp == {"sucesso": True, "already_on_api": True}
    assert new_passagem_count == passagem_count
    passagem.delete()


def test_cancelar_venda_por_ultimo_bilhete_ativo_quando_cancelado(
    mock_cancela_venda_bilhete_cancelado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response()

        resp = totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )

    assert resp[0]["multa"] == "0"
    assert buscar_bilhetes_mock.call_args[0][0][
        "data"
    ] == mock_passagem_to_remove_totalbus.trechoclasse_integracao.datetime_ida.strftime("%Y-%m-%d")
    cancelar_venda_por_bilhete_mock.assert_called()
    buscar_bilhetes_mock.assert_called()


def test_cancelar_venda_por_ultimo_bilhete_ativo_com_data_hora_partida(
    requests_mock,
    mock_cancela_venda_bilhete_cancelado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    mock_passagem_to_remove_totalbus.data_hora_partida = "2023-12-29 19:30"
    mock_passagem_to_remove_totalbus.save()
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response()

        resp = totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )

    mock_passagem_to_remove_totalbus.refresh_from_db()
    assert mock_passagem_to_remove_totalbus.status == Passagem.Status.CANCELADA
    assert resp[0]["multa"] == "0"
    data_esperada = "2023-12-29"
    assert mock_passagem_to_remove_totalbus.trechoclasse_integracao.datetime_ida.strftime("%Y-%m-%d") != data_esperada
    assert buscar_bilhetes_mock.call_args[0][0]["data"] == data_esperada
    cancelar_venda_por_bilhete_mock.assert_called()
    buscar_bilhetes_mock.assert_called()


def test_cancelar_venda_por_ultimo_bilhete_ativo_quando_cancelado_troca_de_poltrona(
    mock_cancela_venda_bilhete_troca_de_poltrona,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response()

        resp = totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )

    assert resp[0]["multa"] == "0"
    cancelar_venda_por_bilhete_mock.assert_called()
    buscar_bilhetes_mock.assert_called()


def test_raise_poltronatrocadaexception_cancelar_troca_de_poltrona(
    rf, mock_cancela_bilhete_raise_poltronatrocada, mock_passagem_to_remove_totalbus
):
    request = rf.get("/v1/compra/cancela")
    response = views.efetua_cancelamento(request, travel_id=mock_passagem_to_remove_totalbus.travel_internal_id)
    data = json.loads(response.content)
    assert data == {
        "error": "Não é possível cancelar/devolver um bilhete que possui troca de poltrona",
        "error_type": "passenger_ticket_seat_changed",
    }
    assert response.status_code == HTTPStatus.FORBIDDEN


def test_cancelar_venda_considerado_embarcado(
    mock_cancela_venda_considerado_embarcado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    travel_id = mock_passagem_to_remove_totalbus.travel_internal_id
    trecho_classe_id = mock_passagem_to_remove_totalbus.trechoclasse_integracao.trechoclasse_internal_id
    with pytest.raises(PassengerTicketAlreadyPrintedException):
        totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": trecho_classe_id, "travel_id": travel_id})
        )


def test_cancelar_venda_por_ultimo_bilhete_ativo_quando_nao_encontrado(
    mock_cancela_venda_bilhete_nao_encontrado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response()

        resp = totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )

    assert resp[0]["multa"] == "0"
    cancelar_venda_por_bilhete_mock.assert_called()
    buscar_bilhetes_mock.assert_called()


def test_cancelar_venda_por_ultimo_bilhete_ativo_nenhum_bilhete_encontrado(
    mock_cancela_venda_bilhete_cancelado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
    mock_buscar_bilhetes_por_numero_sistema_nenhum_bilhete_encontrado,
):
    with mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock:
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()

        with pytest.raises(RodoviariaException, match=r"Não há bilhete para a consulta enviada"):
            totalbus_api.cancela_venda(CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441}))

    cancelar_venda_por_bilhete_mock.assert_not_called()


def test_cancelar_venda_por_bilhete(mock_cancelar_venda_por_bilhete, totalbus_api):
    resp = totalbus_api.cancelar_venda_por_bilhete({"numeroBilhete": "4558441"})
    assert resp["multa"] == "0"


def test_cancelar_venda_por_bilhete_erro(mock_cancelar_venda_por_bilhete_erro_generico, totalbus_api_validar_multa):
    with pytest.raises(RodoviariaConnectionError) as ex:
        totalbus_api_validar_multa.cancelar_venda_por_bilhete({"numeroBilhete": "4558441"})
    assert "erro" in error_str(ex)


def test_cancelar_venda_por_bilhete_com_devolucao(
    mock_cancelar_venda_por_bilhete_com_devolucao, totalbus_api_validar_multa
):
    resp = totalbus_api_validar_multa.cancelar_venda_por_bilhete({"numeroBilhete": "4558441"})
    assert resp["multa"] == "9.33"


def test_cancela_venda(
    mock_cancela_venda,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    passagem_count = Passagem.objects.filter(status=Passagem.Status.CONFIRMADA).count()
    resp = totalbus_api.cancela_venda(CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441}))
    new_passagem_count = Passagem.objects.filter(status=Passagem.Status.CONFIRMADA).count()

    assert new_passagem_count == passagem_count - 1
    assert resp[0]["multa"] == "0"


def test_cancela_venda_bilhete_ja_cancelado(
    mock_cancela_venda_bilhete_cancelado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response_with_all_tickets_canceled()

        with pytest.raises(PassengerNotRegistered):
            totalbus_api.cancela_venda(CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441}))

    cancelar_venda_por_bilhete_mock.assert_not_called()
    buscar_bilhetes_mock.assert_called()


def test_cancela_venda_bilhete_ja_cancelado_pax_invalido(
    mock_cancela_venda_bilhete_cancelado,
    totalbus_api,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(TotalbusAPI, "cancelar_venda_por_bilhete") as cancelar_venda_por_bilhete_mock,
        mock.patch.object(TotalbusAPI, "buscar_bilhetes") as buscar_bilhetes_mock,
    ):
        cancelar_venda_por_bilhete_mock.return_value = mocker.CancelarVenda.response()
        buscar_bilhetes_mock.return_value = mocker.BuscarBilhetes.response_with_all_tickets_canceled()

        totalbus_api.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441, "pax_valido": False})
        )

    cancelar_venda_por_bilhete_mock.assert_not_called()
    buscar_bilhetes_mock.assert_called()


def test_cancela_venda_erro(
    mock_cancela_venda_erro_generico,
    totalbus_api_validar_multa,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with pytest.raises(RodoviariaConnectionError) as ex:
        totalbus_api_validar_multa.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )
    assert "erro" in error_str(ex.value)
    mock_passagem_to_remove_totalbus.refresh_from_db()
    assert mock_passagem_to_remove_totalbus.erro_cancelamento == "Um erro genérico."
    assert mock_passagem_to_remove_totalbus.datetime_cancelamento is not None


def test_cancela_venda_read_timeout(
    totalbus_api_validar_multa,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    with (
        mock.patch.object(requests.Session, "request") as mock_request,
        pytest.raises(RodoviariaConnectionError) as ex,
    ):
        mock_request.side_effect = requests.ReadTimeout
        totalbus_api_validar_multa.cancela_venda(
            CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
        )
    expected_error_msg = "totalbus http://totalbus.base/cancelavenda/cancelarVenda connection error"
    assert error_str(ex.value) == expected_error_msg
    mock_passagem_to_remove_totalbus.refresh_from_db()
    assert mock_passagem_to_remove_totalbus.erro_cancelamento == expected_error_msg
    assert mock_passagem_to_remove_totalbus.datetime_cancelamento is not None


def test_cancela_venda_com_devolucao(
    mock_cancela_venda_com_devolucao,
    totalbus_api_validar_multa,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    passagem_count = Passagem.objects.filter(status=Passagem.Status.CONFIRMADA).count()
    resp = totalbus_api_validar_multa.cancela_venda(
        CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 4558441})
    )
    new_passagem_count = Passagem.objects.filter(status=Passagem.Status.CONFIRMADA).count()

    assert new_passagem_count == passagem_count - 1
    assert resp[0]["multa"] == "9.33"


def test_cancela_venda_com_devolucao_2(
    mock_cancela_venda_com_devolucao_2,
    totalbus_api_validar_multa,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    resp = totalbus_api_validar_multa.cancelar_venda({"transacao": "asdasdecascas.dasdeasdasde"}, validar_multa=False)
    assert resp == {"multa": "9.33", "status": True}


def test_cancela_venda_erro_poltrona_duplicada(
    mock_cancela_venda_com_devolucao_2,
    totalbus_api_validar_multa,
    totalbus_trechoclasses,
    mock_passagem_to_remove_totalbus,
):
    resp = totalbus_api_validar_multa.cancelar_venda({"transacao": "asdasdecascas.dasdeasdasde"}, validar_multa=False)
    assert resp == {"multa": "9.33", "status": True}


def test_buscar_itinerario_corrida(totalbus_api, mock_buscar_itinerario_corrida):
    tc_mock = Mock()
    tc_mock.external_id = "SERVICO"
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = to_default_tz(datetime(2021, 9, 22, 10, 0))
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    responses = []
    responses.append(
        totalbus_api.itinerario(tc_mock.external_id, totalbus_api.datetime_ida_from_trecho_classe(tc_mock))
    )
    tc_mock.provider_data = '{"dataCorrida": "2021-09-22"}'
    responses.append(
        totalbus_api.itinerario(tc_mock.external_id, totalbus_api.datetime_ida_from_trecho_classe(tc_mock))
    )
    for response in responses:
        assert 4 == len(response.parsed)
        assert "17674127234fb0ba7a45c7d97289a8e07925900e47" == response.parsed.hash

        checkpoint = response.parsed[1]
        assert 25 * 60 == checkpoint.duracao
        assert 10 * 60 == checkpoint.tempo_embarque
        assert "GASPAR" == checkpoint.local.nome_cidade
        assert "SC" == checkpoint.local.uf
        assert "GASPAR - SC" == checkpoint.local.descricao


def test_buscar_itinerario_corrida_nao_localizado(totalbus_api, mock_buscar_itinerario_nao_localizado):
    tc_mock = Mock()
    tc_mock.external_id = 1
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = datetime(2020, 1, 1, 10, 0)
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    with pytest.raises(TotalbusItinerarioNotFound):
        totalbus_api.itinerario(tc_mock.external_id, totalbus_api.datetime_ida_from_trecho_classe(tc_mock))


def test_buscar_itinerario_nao_encontrado_raise_error(totalbus_api, mock_buscar_itinerario_nao_localizado):
    external_id = 1
    datetime_ida = datetime(2020, 1, 1, 10, 0)
    with pytest.raises(TotalbusItinerarioNotFound, match="Itinerário não encontrado"):
        totalbus_api.itinerario(external_id, datetime_ida)


def test_sanitize_tipo_documento(totalbus_api):
    assert totalbus_api._sanitize_tipo_documento("RG") == "RG"
    assert totalbus_api._sanitize_tipo_documento("CPF") == "CPF"
    assert totalbus_api._sanitize_tipo_documento("Passaporte") == "Passaporte"
    assert totalbus_api._sanitize_tipo_documento(None) == "RG"
    assert totalbus_api._sanitize_tipo_documento("x") == "RG"


def test_buscar_corridas(totalbus_api, mock_buscar_servico_com_hibrido):
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.company_internal_id = 313
    totalbus_api.company.save()
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}
    corridas = totalbus_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 1
    assert corridas[0] == ServicoForm.parse_obj(
        {
            "external_datetime_ida": "2021-05-14T20:00:00",
            "external_id": "999911",
            "linha": 62,
            "preco": 145.41,
            "vagas": 6,
            "external_datetime_chegada": "2021-05-15T00:25:00",
            "classe": "LEITO CAMA",
            "capacidade_classe": 12,
            "distancia": 352.0,
            "rota_external_id": "999911",
            "provider_data": {
                "servico": "999911",
                "rutaId": 202,
                "marcaId": 62,
                "grupo": "VIACA",
                "grupoOrigemId": 21847,
                "grupoDestinoId": 19068,
                "saida": "2021-05-14 20:00",
                "chegada": "2021-05-15 00:25",
                "dataCorrida": "2021-05-13",
                "dataSaida": "2021-05-14",
                "poltronasLivres": 6,
                "poltronasTotal": 12,
                "preco": 145.41,
                "precoOriginal": 159.00,
                "classe": "LEITO CAMA",
                "empresa": "VIACAO LUXOR LTDA",
                "empresaId": 42,
                "vende": True,
                "bpe": False,
                "km": 352.0,
                "cnpj": "26760933000170",
            },
        }
    )


def test_buscar_corridas_hibrido_RodeRotas(totalbus_api, mock_buscar_servico_totalbus):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}

    corridas = totalbus_api.buscar_corridas(request_params).servicos

    assert len(corridas) == 5
    assert corridas[0] == ServicoForm.parse_obj(
        {
            "external_datetime_ida": "2021-05-14T20:05:00",
            "external_id": "712",
            "linha": 62,
            "preco": 142.41,
            "vagas": 6,
            "external_datetime_chegada": "2021-05-15T00:25:00",
            "classe": "LEITO CAMA",
            "capacidade_classe": 20,
            "distancia": 352.0,
            "rota_external_id": "712",
            "provider_data": {
                "bpe": False,
                "chegada": "2021-05-15 00:25",
                "classe": "LEITO CAMA",
                "cnpj": "26760933000170",
                "dataCorrida": "2021-05-14",
                "dataSaida": "2021-05-14",
                "empresa": "VIACAO LUXOR LTDA",
                "empresaId": 42,
                "grupo": "VIACA",
                "grupoDestinoId": 19068,
                "grupoOrigemId": 21847,
                "km": 352.0,
                "marcaId": 62,
                "poltronasLivres": 6,
                "poltronasTotal": 20,
                "preco": 142.41,
                "precoOriginal": 159.0,
                "rutaId": 202,
                "saida": "2021-05-14 20:05",
                "servico": "712",
                "vende": True,
            },
        }
    )


def test_buscar_corridas_conexao_sem_tempo_espera(totalbus_api, mock_buscar_servico_conexao):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}
    corridas = totalbus_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 1


def test_buscar_corridas_conexao_com_tempo_espera(totalbus_api, mock_buscar_servico_conexao_com_tempo_espera):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}
    corridas = totalbus_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 0


def test_rota_id_from_trecho_classe(totalbus_api):
    trecho_classe = baker.make("rodoviaria.TrechoClasse")
    assert totalbus_api.rota_id_from_trecho_classe(trecho_classe) is None
    trecho_classe.external_id = 123321
    assert totalbus_api.rota_id_from_trecho_classe(trecho_classe) == 123321


def test_buscar_corridas_company_especifica(totalbus_api):
    mocked_buscar_corrida_response = mocker.BuscarServico.response()
    mocked_buscar_corrida_response["lsServicos"][0]["empresaId"] = 43
    totalbus_api.company.company_external_id = 42
    totalbus_api.company.save()

    assert len(mocked_buscar_corrida_response["lsServicos"]) == 5
    with mock.patch.object(totalbus_endpoints.BuscarCorridasRequestConfig, "invoke") as mock_buscar_corridas_new:
        mock_buscar_corridas_new.return_value.json.return_value = mocked_buscar_corrida_response
        request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}
        corridas = totalbus_api.buscar_corridas(request_params).servicos
        assert len(corridas) == 4


def test_buscar_corridas_to_many_requests(totalbus_api, mock_buscar_corridas_to_many_requests):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}

    with pytest.raises(
        RodoviariaTooManyRequestsError,
        match="params=None status_code=429",
    ):
        totalbus_api.buscar_corridas(request_params)


def test_buscar_corridas_trecho_nao_localizado(totalbus_api, mock_buscar_servico_nao_localizado):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}

    with pytest.raises(
        RodoviariaTrechoNotFoundException,
    ):
        totalbus_api.buscar_corridas(request_params)


def test_buscar_corridas_trecho_nao_disponivel(totalbus_api, mock_buscar_servico_nao_disponivel):
    request_params = {"origem": 1, "destino": 2, "data": "2021-10-12"}

    with pytest.raises(
        RodoviariaTrechoNotFoundException,
    ):
        totalbus_api.buscar_corridas(request_params)


def test_datetime_ida_from_trecho_classe_vindo_do_grupo(totalbus_api):
    tc_mock = Mock()
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = datetime(2021, 9, 22, 10, 0)
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    date = totalbus_api.datetime_ida_from_trecho_classe(tc_mock)
    assert date == "2021-09-22"


def test_datetime_ida_from_trecho_classe_vindo_do_provider_data(totalbus_api):
    tc_mock = Mock()
    tc_mock.provider_data = '{"dataCorrida": "2021-09-22"}'
    date = totalbus_api.datetime_ida_from_trecho_classe(tc_mock)
    assert date == "2021-09-22"


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_datetime_ida_from_trecho_classe_vindo_do_grupo_erro(totalbus_api):
    trecho_classe = baker.make(
        "rodoviaria.trechoclasse",
        grupo__datetime_ida=now(),
        origem__cidade__timezone="UTA",
    )
    trecho_classe.provider_data = None

    with pytest.raises(RodoviariaException) as exc:
        totalbus_api.datetime_ida_from_trecho_classe(trecho_classe)
    assert "Não foi possível achar o timezone. Verifique se o trecho contém origem e se há timezone na cidade." in str(
        exc.value
    )


def test_datetime_ida_from_trecho_classe_vindo_do_provider_data_erro(totalbus_api):
    tc_mock = Mock()
    tc_mock.provider_data = '{"asdfasdf": "2021-09-22"}'
    with pytest.raises(KeyError) as exc:
        totalbus_api.datetime_ida_from_trecho_classe(tc_mock)
    assert "dataCorrida" in str(exc.value)


def test_confirmar_venda_erro_com_retry(totalbus_api):
    with (
        mock.patch.object(
            totalbus_endpoints.ConfirmarVendaRequestConfig,
            "invoke",
            side_effect=TotalbusCategoriaNotFound("Não foi possível carregar a categoria da corrida"),
        ) as mock_request_config_invoke,
        pytest.raises(TotalbusCategoriaNotFound),
    ):
        params = models.ConfirmarVendaForm(
            nomePassageiro="Fulano de Tal",
            documentoPassageiro="10101001090",
            tipoDocumentoPassageiro="RG",
            telefone="12912121212",
            idFormaPagamento=23,
            formaPagamento="BUSER",
            transacao="CONFIRMAR_VENDA_ERRO_COM_RETRY",
        )
        totalbus_api.confirmar_venda(params)
    assert mock_request_config_invoke.call_count == 3


def test_check_response_401_unauthorized_login(totalbus_mock_unauthorized_login, totalbus_api):
    with pytest.raises(RodoviariaUnauthorizedError) as exc:
        request = totalbus_endpoints.BuscaOrigensConfig(totalbus_api.login)
        executor = get_http_executor()
        request.invoke(executor)

    assert str(exc.value) == "Usuário/Senha inválido"


def test_check_response_423_processando_cache(totalbus_mock_busca_origem_processando_cache, totalbus_api):
    with pytest.raises(RodoviariaException) as exc:
        request = totalbus_endpoints.BuscaOrigensConfig(totalbus_api.login)
        executor = get_http_executor()
        request.invoke(executor)

    assert str(exc.value) == "Processando Cache. Tente novamente."


@pytest.fixture
def mock_fluxo_compra(totalbus_api, totalbus_grupos_mockado):
    grupo_buser_django = totalbus_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = totalbus_api.company.company_internal_id
    trecho_classe_buser_django_infos = totalbus_grupos_mockado.ida.trecho_classe_infos
    trecho_classe_id = 482739
    expected_servico = mocker.BuscarServico.response()["lsServicos"][0]
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    trecho_classe_buser_django_infos.trechoclasse_id = trecho_classe_id
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_tz(
        datetime.strptime(expected_servico["saida"], "%Y-%m-%d %H:%M"), timezone
    )
    trecho_classe_buser_django_infos.tipo_assento = "leito cama"

    cidade_origem, cidade_destino = baker.make(Cidade, company=totalbus_api.company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )

    yield grupo_buser_django, trecho_classe_buser_django_infos, trecho_classe_id


def test_compra_fluxo_completo(
    totalbus_api,
    mock_buscar_servico,
    mock_get_poltronas_livres,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar,
    mock_cancela_venda,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    baker.make(
        Company,
        company_internal_id=totalbus_api.company.company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    with (
        mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django),
        mock.patch(
            "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
            return_value=trecho_classe_buser_django_infos,
        ),
    ):
        params_verifica_poltrona = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(params_verifica_poltrona)
    assert len(poltronas) == 2
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2, valor_cheio=preco)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert passagens_compradas.count() == 2
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == totalbus_api.company.id
    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CONFIRMADA
    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA


def test_lista_empresas_api(totalbus_mock_consultar_empresas):
    login_params = TotalbusNoCompanyLogin(
        user="user_anon",
        password="le_senhe",
        tenant_id="2b1badf7-b1a7-4446-9be8-123618ca4560",
    )

    resp = totalbus_api.lista_empresas_api(login_params)

    assert len(resp) == 3
    for empresa in resp:
        assert "id" in empresa
        assert "nome" in empresa


def test_fetch_formas_pagamento(totalbus_mock_buscar_formas_pagamento):
    login = TotalbusNoCompanyLogin(
        user="user_anon",
        password="le_senhe",
        tenant_id="2b1badf7-b1a7-4446-9be8-123618ca4560",
    )

    resp = totalbus_api.fetch_formas_pagamento(login)

    assert len(resp) == 3
    for forma_pag in resp:
        assert "id" in forma_pag
        assert "descricao" in forma_pag


# def test_buscar_servicos_por_data_erro_mais_30_dias(totalbus_api):
#     data_inicio = datetime(2022, 4, 19)
#     data_fim = datetime(2022, 6, 19)
#     with pytest.raises(ValueError):
#         totalbus_api.buscar_servicos_por_data(data_inicio, data_fim)


def test_buscar_servicos_por_data(totalbus_api, mock_buscar_servicos_detalhado):
    result = totalbus_api.buscar_servicos_por_data(
        data_inicio=today_midnight(), data_fim=today_midnight(), company_external_id=42
    )
    assert len(result) == 3
    assert result[1000] == datetime(2024, 1, 20, 19, 00)
    assert result[1022] == datetime(2024, 1, 20, 6, 00)
    assert result[8012] == datetime(2024, 1, 20, 19, 10)


def test_buscar_servicos_por_data_sem_servicos(totalbus_api, mock_buscar_servicos_detalhado_sem_servicos):
    result = totalbus_api.buscar_servicos_por_data(
        data_inicio=today_midnight(), data_fim=today_midnight(), company_external_id=42
    )
    assert result == {}


def test_buscar_servicos_por_data_pega_max_data(totalbus_api):
    external_id = 3469
    retorno_mock = [
        {
            "servicoId": external_id,
            "empresaId": 1,
            "dataSaida": "",
            "dataChegada": "",
            "dataHoraServico": datetime(2022, 7, 14, 19),
            "trechos": [],
        },
        {
            "servicoId": external_id,
            "empresaId": 1,
            "dataSaida": "",
            "dataChegada": "",
            "dataHoraServico": datetime(2022, 7, 15, 19),
            "trechos": [],
        },
        {
            "servicoId": external_id,
            "empresaId": 1,
            "dataSaida": "",
            "dataChegada": "",
            "dataHoraServico": datetime(2022, 7, 15, 19, 1),
            "trechos": [],
        },
    ]
    with mock.patch.object(totalbus_endpoints.BuscarServicosPeriodoConfig, "invoke") as mock_buscar_servicos_new:
        mock_buscar_servicos_new.return_value.json.side_effect = [retorno_mock, None]
        result = totalbus_api.buscar_servicos_por_data(
            data_inicio=today_midnight(),
            data_fim=today_midnight(),
            company_external_id=42,
        )
    assert len(result) == 1
    assert result[external_id] == datetime(2022, 7, 15, 19, 1)


def test_buscar_servicos_retry_badrequest_msgerror_none_retry(
    totalbus_api, mock_padrao_buscar_servicos_bad_request_no_response_with_retry
):
    data_inicial = datetime(2024, 1, 25, 19, 0)
    data_final = datetime(2024, 1, 27, 0, 0)
    resp = totalbus_api.buscar_viagens_por_periodo(data_inicial, data_final, 42)
    assert len(resp) == 3


def test_fetch_data_limite_servicos(totalbus_api, mock_consultar_empresas_usando_base_url_obj):
    retorno_mock = {
        12: datetime(2022, 1, 1, 1, 1),
        13: datetime(2022, 1, 1, 1, 1),
    }
    retorno_mock_segunda_rodada = {
        12: datetime(2022, 2, 1, 1, 1),
        13: datetime(2022, 1, 1, 1, 1),
    }

    with mock.patch.object(TotalbusAPI, "buscar_servicos_por_data") as mock_buscar_servicos:
        mock_buscar_servicos.side_effect = [
            retorno_mock,
            retorno_mock_segunda_rodada,
            None,
            None,
            None,
        ]
        result = totalbus_api.fetch_data_limite_servicos(today_midnight())
    assert len(result) == 2
    assert result[13] == datetime(2022, 1, 1, 1, 1)


def test_salva_provider_da_compra(totalbus_api, totalbus_trechoclasses, mock_comprar, cache_mock):
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueio = models.InfosCacheaveisBloqueioPoltrona(
        transacao="transacao", preco=totalbus_trechoclasses.ida.preco_rodoviaria
    )
    for p in params.poltronas:
        mc.set_poltrona_key_cache(
            trechoclasse_id,
            p,
            [bloqueio],
        )
    totalbus_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)

    # Verifica se comprou a passagem
    assert len(passagens) == 1
    passagem = passagens.first()

    # Verifica se os dados específicos da passagens corresponde apenas a passagem do pax
    assert passagem.provider_data == mocker.ConfirmarVenda.response()


def test_get_atualizacao_passagem_api_parceiro_totalbus(
    totalbus_login,
    totalbus_api,
    mock_buscar_bilhetes_por_numero_sistema,
    totalbus_trechoclasses,
):
    trechoclasse = totalbus_trechoclasses.ida
    trechoclasse.datetime_ida = to_default_tz(datetime.strptime("2021-09-22 23:50", "%Y-%m-%d %H:%M"))
    passagemOriginalParaConsulta = baker.make(
        "rodoviaria.Passagem",
        numero_passagem="88570",
        trechoclasse_integracao=trechoclasse,
        status="confirmada",
        numero_bilhete="10000000706966",
        localizador="010000930269",
        poltrona_external_id="24",
    )
    passagemConsultadaComDadosAtualizados = totalbus_api.get_atualizacao_passagem_api_parceiro(
        passagemOriginalParaConsulta
    )
    resp = passagemConsultadaComDadosAtualizados.dict()
    assert resp["integracao"] == "Totalbus"
    assert resp["numero_passagem"] == passagemOriginalParaConsulta.numero_passagem
    assert resp["localizador"] == passagemOriginalParaConsulta.localizador
    assert resp["numero_bilhete"] == passagemOriginalParaConsulta.numero_bilhete
    assert resp["status"] == passagemOriginalParaConsulta.status
    assert resp["data_partida"] == passagemOriginalParaConsulta.trechoclasse_integracao.datetime_ida.strftime(
        "%d/%m/%Y %H:%M"
    )

    ## informações atualizadas e vindas da consulta vindo da mocker.py

    assert passagemOriginalParaConsulta.poltrona_external_id == "24"
    assert resp["numero_assento"] != passagemOriginalParaConsulta.poltrona_external_id
    assert resp["numero_assento"] == 22


def test_get_atualizacao_passagem_api_parceiro_totalbus_cancelada(
    totalbus_login,
    totalbus_api,
    mock_buscar_bilhetes_por_numero_sistema_todos_cancelados,
    totalbus_trechoclasses,
):
    trechoclasse = totalbus_trechoclasses.ida
    trechoclasse.datetime_ida = to_default_tz(datetime.strptime("1994-11-04 23:30", "%Y-%m-%d %H:%M"))
    passagemOriginalParaConsulta = baker.make(
        "rodoviaria.Passagem",
        numero_passagem="010000930269",
        trechoclasse_integracao=trechoclasse,
        status="confirmada",
        numero_bilhete="10000000706966",
    )
    passagemConsultadaComDadosAtualizados = totalbus_api.get_atualizacao_passagem_api_parceiro(
        passagemOriginalParaConsulta
    )
    resp = passagemConsultadaComDadosAtualizados.dict()
    assert resp["integracao"] == "Totalbus"
    assert resp["numero_bilhete"] == passagemOriginalParaConsulta.numero_bilhete

    ## informações atualizadas e vindas da consulta vindo da mocker.py

    assert passagemOriginalParaConsulta.status == "confirmada"
    assert resp["status"] == "cancelada"
    assert resp["status"] != passagemOriginalParaConsulta.status


def test_get_atualizacao_passagem_api_parceiro_totalbus_nenhum_bilhete_encontrado(
    totalbus_login,
    totalbus_api,
    mock_buscar_bilhetes_por_numero_sistema_nenhum_bilhete_encontrado,
    totalbus_trechoclasses,
):
    trechoclasse = totalbus_trechoclasses.ida
    trechoclasse.datetime_ida = to_default_tz(datetime.strptime("1994-11-04 23:30", "%Y-%m-%d %H:%M"))
    passagemOriginalParaConsulta = baker.make(
        "rodoviaria.Passagem",
        numero_passagem="010000930269",
        trechoclasse_integracao=trechoclasse,
        status="confirmada",
        numero_bilhete="10000000706966",
    )
    with pytest.raises(RodoviariaException, match=r"Não há bilhete para a consulta enviada"):
        totalbus_api.get_atualizacao_passagem_api_parceiro(passagemOriginalParaConsulta)


def test_get_atualizacao_passagem_api_parceiro_totalbus_timezone(
    totalbus_login,
    totalbus_api,
    mock_buscar_bilhetes_por_numero_sistema_conferir_params,
    totalbus_trechoclasses,
):
    trechoclasse = totalbus_trechoclasses.ida
    original_datetime = datetime.strptime("2021-09-22 23:50", "%Y-%m-%d %H:%M")
    trechoclasse.datetime_ida = to_default_tz(original_datetime)
    passagemOriginalParaConsulta = baker.make(
        "rodoviaria.Passagem",
        numero_passagem="010000930269",
        trechoclasse_integracao=trechoclasse,
    )
    resp = totalbus_api.get_atualizacao_passagem_api_parceiro(passagemOriginalParaConsulta)

    assert resp.data_partida == original_datetime.strftime("%d/%m/%Y %H:%M")


@mock.patch.object(TotalbusAPI, "buscar_bilhetes", return_value=[])
def test_get_atualizacao_passagem_api_parceiro_totalbus_erro_passsagem_nao_encontrada(
    totalbus_login, totalbus_api, totalbus_trechoclasses
):
    trechoclasse = totalbus_trechoclasses.ida
    trechoclasse.datetime_ida = to_default_tz(datetime.strptime("2021-09-22 23:50", "%Y-%m-%d %H:%M"))
    passagem = baker.make(
        "rodoviaria.Passagem",
        numero_passagem="88570",
        trechoclasse_integracao=trechoclasse,
        status="confirmada",
        numero_bilhete="10000000706966",
        localizador="010000930269",
        poltrona_external_id="24",
    )
    with pytest.raises(
        PassengerNotRegistered,
        match=f"Totalbus - Não foi possível encontrar a passagem {passagem.numero_passagem}",
    ):
        totalbus_api.get_atualizacao_passagem_api_parceiro(passagem)


def test_buscar_servico_conexao(mock_buscar_servico_conexao, totalbus_api, totalbus_grupos_mockado):
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.save()
    corrida_api = mocker.BuscarServico.response_conexao()["lsServicos"][0]
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime(corrida_api["saida"], "%Y-%m-%d %H:%M"))
    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = class_match_svc.buser_class(corrida_api["classe"])

    servico_id = corrida_api["servico"]
    corrida_api["conexao"]["servicoConexao"]

    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    servico = resp.servicos[0]
    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    assert servico.external_id == servico_id
    assert servico.preco == D(str(corrida_api["preco"]))


def test_buscar_servico_conexao_nao_eh_hibrido(mock_buscar_servico_conexao, totalbus_api, totalbus_grupos_mockado):
    totalbus_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_api.company.company_internal_id = 313
    totalbus_api.company.save()
    corrida_api = mocker.BuscarServico.response_conexao()["lsServicos"][0]
    trecho_classe = totalbus_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime(corrida_api["saida"], "%Y-%m-%d %H:%M"))
    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = class_match_svc.buser_class(corrida_api["classe"])

    corrida_api["servico"]

    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = totalbus_api.buscar_corridas(mocker.BuscarServico.request(), match_params)
    assert resp.found is False


def test_bloquear_poltrona_conexao(
    http_mock,
    mock_bloquear_poltrona_venda_normal,
    mock_get_poltronas_livres,
    totalbus_api,
    totalbus_trechoclasses,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    totalbus_trechoclasses.ida.provider_data = json.dumps(mocker.BuscarServico.response_conexao()["lsServicos"][0])
    totalbus_trechoclasses.ida.save()
    poltronas = [18]
    totalbus_api.bloquear_poltronas(trecho_classe_id, poltronas)
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    assert mc.get_poltrona_key_cache(trecho_classe_id, 18) == parse_obj_as(
        list[models.InfosCacheaveisBloqueioPoltrona],
        [
            {
                "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
                "preco": D("589.30"),
                "origem": "FORTALEZA - CE",
                "destino": "SOBRAL - CE",
                "data_hora_partida": "2023-12-28 22:45",
                "preco_conexao": D("111.75"),
                "is_conexao": True,
            },
            {
                "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
                "preco": D("589.30"),
                "origem": "FORTALEZA - CE",
                "destino": "SOBRAL - CE",
                "data_hora_partida": "2023-12-28 22:45",
                "preco_conexao": D("241.03"),
                "is_conexao": True,
            },
        ],
    )
    expected_data_primeiro_trecho = mocker.BuscarServico.response_conexao()["lsServicos"][0]["conexao"][
        "primeiroTrechoDataCorrida"
    ]
    expected_data_primeiro_trecho = datetime.strptime(expected_data_primeiro_trecho, "%d/%m/%Y").strftime("%Y-%m-%d")
    expected_data_segundo_trecho = mocker.BuscarServico.response_conexao()["lsServicos"][0]["conexao"][
        "segundoTrechoDataCorrida"
    ]
    expected_data_segundo_trecho = datetime.strptime(expected_data_segundo_trecho, "%d/%m/%Y").strftime("%Y-%m-%d")
    assert http_mock.assert_call_count(
        totalbus_endpoints.BloquearPoltronaVendaNormalConfig(totalbus_api.login).url,
        2,
    )
    assert json.loads(http_mock.get_requests()[0].content)["data"] == expected_data_primeiro_trecho
    assert json.loads(http_mock.get_requests()[1].content)["data"] == expected_data_segundo_trecho


def _assert_compra_conexao(totalbus_api, totalbus_trechoclasses, trechoclasse_id, mocker):
    # Instanciação do ComprarForm
    params = ComprarForm(
        trechoclasse_id=trechoclasse_id,
        travel_id=1521,
        valor_cheio=D("500.00"),
        preco_rodoviaria=D("650.00"),
        poltronas=[11],
        passageiros=[
            PassageiroForm(
                id=15,
                name="Buseiro de conexao",
                rg_number="*********",
                phone="12912121212",
                cpf="10101001090",
            )
        ],
    )

    # Instanciação do TotalbusMC
    mc = TotalbusMC(totalbus_api.company.company_internal_id)

    bloqueios_params = [
        {
            "transacao": "transacao",
            "preco": D("349.00"),
            "preco_conexao": D("249.00"),
            "origem": "ORIGEM",
            "destino": "PARADA",
            "data_hora_partida": "2023-12-28 22:45",
            "is_conexao": True,
        },
        {
            "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
            "preco": D("301.00"),
            "preco_conexao": D("251.00"),
            "origem": "PARADA",
            "destino": "DESTINO",
            "data_hora_partida": "2023-12-28 22:45",
            "is_conexao": True,
        },
    ]
    bloqueios = parse_obj_as(list[models.InfosCacheaveisBloqueioPoltrona], bloqueios_params)

    for p in params.poltronas:
        mc.set_poltrona_key_cache(
            trechoclasse_id,
            p,
            bloqueios,
        )

    # Efetuar a compra
    totalbus_api.comprar(params)

    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id,
        status=Passagem.Status.CONFIRMADA,
    )

    assert len(passagens) == 2

    for p in passagens:
        assert p.travel_internal_id == params.travel_id
        assert p.buseiro_internal_id == params.passageiros[0].id

    # Verifica que o endpoint de compra foi chamado
    assert mocker.assert_call_count(totalbus_endpoints.ConfirmarVendaRequestConfig(totalbus_api.login).url, 2)

    # Validação dos descontos
    passagem_primeira_perna = passagens.get(origem="ORIGEM", destino="PARADA")
    passagem_segunda_perna = passagens.get(origem="PARADA", destino="DESTINO")

    valor_cheio = passagem_primeira_perna.valor_cheio
    assert passagem_primeira_perna.desconto == D("100.00")
    assert passagem_primeira_perna.preco_rodoviaria == D("249.00")
    assert passagem_segunda_perna.desconto == D("50.00")
    assert passagem_segunda_perna.preco_rodoviaria == D("251.00")
    assert passagem_primeira_perna.preco_rodoviaria + passagem_segunda_perna.preco_rodoviaria == valor_cheio


def test_comprar_conexao_async(http_mock, totalbus_api, totalbus_trechoclasses, mock_comprar, cache_mock):
    # Configuração inicial para o teste assíncrono
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    totalbus_trechoclasses.ida.provider_data = json.dumps({"conexao": None})
    totalbus_trechoclasses.ida.save()

    # Verificação de inexistência de passagens antes da compra
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()

    # Chamada da função auxiliar de asserção
    _assert_compra_conexao(totalbus_api, totalbus_trechoclasses, trechoclasse_id, http_mock)


def test_comprar_conexao_sync(requests_mock, totalbus_api, totalbus_trechoclasses, cache_mock, override_config):
    # Mock da requisição síncrona
    requests_mock.add(
        responses.POST,
        totalbus_endpoints.ConfirmarVendaRequestConfig(totalbus_api.login).url,
        json=mocker.ConfirmarVenda.response(),
    )

    # Configuração inicial para o teste síncrono
    trechoclasse_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    totalbus_trechoclasses.ida.provider_data = json.dumps({"conexao": {"servicoConexao": "123"}})
    totalbus_trechoclasses.ida.save()

    # Uso da configuração de sincronização de conexão
    with override_config(EMPRESAS_COM_EMISSAO_CONEXAO_SYNC=f"{totalbus_api.company.id}"):
        # Chamada da função auxiliar de asserção
        _assert_compra_conexao(totalbus_api, totalbus_trechoclasses, trechoclasse_id, requests_mock)


def test_cancelar_conexao(requests_mock, mock_cancela_venda, totalbus_api, totalbus_trechoclasses):
    passagens = baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=13,
        buseiro_internal_id=21,
        pedido_external_id="transacao_1",
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        _quantity=2,
    )
    passagens[1].pedido_external_id = "transacao_2"
    passagens[1].save()
    totalbus_api.cancela_venda(CancelaVendaForm.parse_obj({"trechoclasse_id": 4558441, "travel_id": 13}))
    assert requests_mock.assert_call_count(totalbus_endpoints.CancelarVendaConfig(totalbus_api.login).url, 2)
    transacoes_canceladas = {json.loads(call[0].body)["transacao"] for call in requests_mock.calls}
    pedidos_external_id = set()
    for passagem in passagens:
        passagem.refresh_from_db()
        pedidos_external_id.add(passagem.pedido_external_id)
        assert passagem.status == Passagem.Status.CANCELADA
    assert pedidos_external_id == transacoes_canceladas


def test_desbloquear_poltrona_conexao(requests_mock, mock_desbloquear_poltrona, totalbus_api):
    trecho_classe_id = 1932
    poltrona = 15
    mc = TotalbusMC(totalbus_api.company.company_internal_id)
    bloqueios_params = [
        {"transacao": "transacao_1", "preco": D("12.00")},
        {"transacao": "transacao_2", "preco": D("42")},
    ]
    bloqueios = parse_obj_as(list[models.InfosCacheaveisBloqueioPoltrona], bloqueios_params)
    mc.set_poltrona_key_cache(
        trecho_classe_id,
        poltrona,
        bloqueios,
    )
    resp = totalbus_api.desbloquear_poltronas(trecho_classe_id, [poltrona])
    assert requests_mock.assert_call_count(totalbus_endpoints.DesbloquearPoltronasConfig(totalbus_api.login).url, 2)
    assert resp == {}
    assert not mc.get_poltrona_key_cache(trecho_classe_id, poltrona)


def test_compra_fluxo_completo_conexao(
    http_mock,
    requests_mock,
    totalbus_api,
    mock_buscar_servico_conexao,
    mock_get_poltronas_livres,
    mock_bloquear_poltrona_venda_normal,
    mock_comprar,
    mock_cancela_venda,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    baker.make(
        Company,
        company_internal_id=totalbus_api.company.company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    corrida_api = mocker.BuscarServico.response_conexao()["lsServicos"][0]
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_default_tz(
        datetime.strptime(corrida_api["saida"], "%Y-%m-%d %H:%M")
    )
    trecho_classe_buser_django_infos.tipo_assento = class_match_svc.buser_class(corrida_api["classe"])
    with (
        mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django),
        mock.patch(
            "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
            return_value=trecho_classe_buser_django_infos,
        ),
    ):
        params_verifica_poltrona = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(params_verifica_poltrona)
    assert len(poltronas) == 2
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2, valor_cheio=preco)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id
    ).order_by("buseiro_internal_id")
    assert passagens_compradas.count() == 4
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == totalbus_api.company.id
        assert p.travel_internal_id == comprar_params.travel_id
    passageiros = sorted(comprar_params.passageiros, key=lambda k: k.id)
    assert passagens_compradas[0].buseiro_internal_id == passageiros[0].id
    assert passagens_compradas[1].buseiro_internal_id == passageiros[0].id
    assert passagens_compradas[2].buseiro_internal_id == passageiros[1].id
    assert passagens_compradas[3].buseiro_internal_id == passageiros[1].id
    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA
    assert passagens_compradas[2].status == Passagem.Status.CONFIRMADA
    assert passagens_compradas[3].status == Passagem.Status.CONFIRMADA
    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[2].status == Passagem.Status.CANCELADA
    assert passagens_compradas[3].status == Passagem.Status.CANCELADA
    assert requests_mock.assert_call_count(totalbus_endpoints.BuscarCorridasRequestConfig(totalbus_api.login).url, 1)
    assert requests_mock.assert_call_count(totalbus_endpoints.RetornaPoltronasConfig(totalbus_api.login).url, 2)
    assert http_mock.assert_call_count(
        totalbus_endpoints.BloquearPoltronaVendaNormalConfig(totalbus_api.login).url,
        4,
    )
    assert http_mock.assert_call_count(totalbus_endpoints.ConfirmarVendaRequestConfig(totalbus_api.login).url, 4)
    assert requests_mock.assert_call_count(totalbus_endpoints.CancelarVendaConfig(totalbus_api.login).url, 4)


def test_get_desenho_mapa_poltronas(
    mock_get_poltronas_livres,
    mock_consultar_categoria_servico,
    totalbus_api,
    totalbus_trechoclasses,
    company_categorias_especiais,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    totalbus_trechoclasses.ida.provider_data = json.dumps({"classe": "leito_especial", "dataCorrida": "2024-01-10"})
    totalbus_trechoclasses.ida.save()
    mapa_poltronas = totalbus_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito individual"
    normal = [Passagem.CategoriaEspecial.NORMAL]
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "y": 1,
                        "x": 4,
                        "livre": False,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": [Passagem.CategoriaEspecial.ESPACO_MULHER],
                        "preco": None,
                    },
                    {
                        "y": 8,
                        "x": 4,
                        "livre": True,
                        "numero": 32,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": [Passagem.CategoriaEspecial.PCD],
                        "preco": None,
                    },
                    {
                        "y": 8,
                        "x": 5,
                        "livre": True,
                        "numero": 31,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 8,
                        "x": 2,
                        "livre": True,
                        "numero": 30,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 10,
                        "x": 4,
                        "livre": True,
                        "numero": 40,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 4,
                        "livre": True,
                        "numero": 44,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 5,
                        "livre": True,
                        "numero": 43,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 2,
                        "livre": True,
                        "numero": 42,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 1,
                        "livre": True,
                        "numero": 41,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 12,
                        "x": 2,
                        "livre": True,
                        "numero": 46,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 12,
                        "x": 1,
                        "livre": True,
                        "numero": 45,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 9,
                        "x": 4,
                        "livre": True,
                        "numero": 36,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_get_desenho_mapa_poltronas_ignorado(
    mock_get_poltronas_livres,
    mock_consultar_categoria_servico,
    totalbus_api,
    totalbus_trechoclasses,
    company_categorias_especiais,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    for cat in company_categorias_especiais:
        cat.categoria_especial = Passagem.CategoriaEspecial.IGNORADO
        cat.save()

    totalbus_trechoclasses.ida.provider_data = json.dumps({"classe": "leito_especial", "dataCorrida": "2024-01-10"})
    totalbus_trechoclasses.ida.save()
    mapa_poltronas = totalbus_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito individual"
    normal = [Passagem.CategoriaEspecial.NORMAL]
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "y": 8,
                        "x": 5,
                        "livre": True,
                        "numero": 31,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 8,
                        "x": 2,
                        "livre": True,
                        "numero": 30,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 10,
                        "x": 4,
                        "livre": True,
                        "numero": 40,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 4,
                        "livre": True,
                        "numero": 44,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 5,
                        "livre": True,
                        "numero": 43,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 2,
                        "livre": True,
                        "numero": 42,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 11,
                        "x": 1,
                        "livre": True,
                        "numero": 41,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 12,
                        "x": 2,
                        "livre": True,
                        "numero": 46,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 12,
                        "x": 1,
                        "livre": True,
                        "numero": 45,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "y": 9,
                        "x": 4,
                        "livre": True,
                        "numero": 36,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_get_desenho_mapa_poltronas_classe_nao_mapeada(
    mock_get_poltronas_livres,
    mock_consultar_categoria_servico,
    totalbus_api,
    totalbus_trechoclasses,
    company_categorias_especiais,
):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    totalbus_trechoclasses.ida.provider_data = json.dumps({"classe": "banco de praça", "dataCorrida": "2024-01-10"})
    totalbus_trechoclasses.ida.save()
    with pytest.raises(ValidationError):
        totalbus_api.get_desenho_mapa_poltronas(trecho_classe_id)


def test__get_pernas_conexao(totalbus_api):
    conexao = models.ConexaoModel.parse_obj(
        {
            "servicoConexao": "131024",
            "localidadeConexao": "CALDAS NOVAS - GO",
            "localidadeConexaoId": 3649,
            "empresa": "RODE ROTAS",
            "empresaId": 2,
            "rutaId": 3629,
            "marcaId": 22,
            "dataCorridaConexao": "2024-07-31",
            "dataSaidaConexao": "2024-07-31",
            "primeiroTrechoPoltronasLivres": 42,
            "primeiroTrechoPoltronasTotal": 42,
            "primeiroTrechoClasse": "EXECUTIVO",
            "primeiroTrechoDataCorrida": "30/07/2024",
            "primeiroTrechoDataSaida": "30/07/2024",
            "primeiroTrechoDataChegada": "31/07/2024",
            "primeiroTrechoHoraSaida": "19:00",
            "primeiroTrechoHoraChegada": "06:50",
            "primeiroTrechoPreco": "140.56",
            "primeiroTrechoPrecoOriginal": "26.48",
            "primeiroTrechoServico": "133014",
            "primeiroTrechoLinha": 4147,
            "primeiroTrechoEmpresa": "RODE ROTAS",
            "primeiroTrechoEmpresaId": 2,
            "primeiroTrechoMarca": 22,
            "primeiroTrechoOrigem": 18697,
            "primeiroTrechoOrigemDescricao": "SAO PAULO (TIETE) - SP",
            "primeiroTrechoDestino": 3649,
            "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
            "primeiroTrechoVende": True,
            "primeiroTrechoIsBpe": True,
            "primeiroTrechoSequencia": 1,
            "segundoTrechoPoltronasLivres": 42,
            "segundoTrechoPoltronasTotal": 42,
            "segundoTrechoClasse": "EXECUTIVO",
            "segundoTrechoDataCorrida": "31/07/2024",
            "segundoTrechoDataSaida": "31/07/2024",
            "segundoTrechoDataChegada": "31/07/2024",
            "segundoTrechoHoraSaida": "06:50",
            "segundoTrechoHoraChegada": "12:00",
            "segundoTrechoPreco": "78.75",
            "segundoTrechoPrecoOriginal": "154.95",
            "segundoTrechoServico": "131024",
            "segundoTrechoLinha": 3629,
            "segundoTrechoEmpresa": "RODE ROTAS",
            "segundoTrechoEmpresaId": 2,
            "segundoTrechoMarca": 22,
            "segundoTrechoOrigem": 3649,
            "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
            "segundoTrechoDestino": 2772,
            "segundoTrechoDestinoDescricao": "BRASILIA - DF",
            "segundoTrechoVende": True,
            "segundoTrechoIsBpe": True,
            "segundoTrechoSequencia": 2,
            "terceiroTrechoVende": False,
            "terceiroTrechoIsBpe": False,
            "vende": True,
            "km": 0.0,
            "cnpj": "18449504000159",
            "conexionCtrlId": 131495,
            "conexionGrupo": 1131425,
            "bpe": True,
        }
    )
    trecho_1, trecho_2 = totalbus_api._get_pernas_conexao(conexao)
    # Em caso de conexões onde a data de CHEGADA do primeiro trecho é maior que a data de saída, a data
    # de saída do primeiro trecho precisa ser menor que a data de saída do segundo trecho
    assert trecho_1.data < trecho_2.data
    assert trecho_1.data == date(2024, 7, 30)
    assert trecho_2.data == date(2024, 7, 31)


def test_consultar_categoria_corrida(mock_consultar_categoria_servico, totalbus_api):
    params = {"origem": "7554", "destino": "19030", "data": "2024-08-05", "servico": 111725}
    response = totalbus_api.consultar_categoria_corrida_request(params)
    assert isinstance(response[0], models.AssentoCategoriaEspecial)
    assert sorted([categoria.desccategoria for categoria in response]) == ["ESPAÇO MULHER", "ESPAÇO PET", "NORMAL"]


def test_consultar_categoria_corrida_connection_error_retry(mock_consultar_categoria_servico_with_retry, totalbus_api):
    params = {"origem": "7554", "destino": "19030", "data": "2024-08-05", "servico": 111725}
    response = totalbus_api.consultar_categoria_corrida_request(params)
    assert isinstance(response[0], models.AssentoCategoriaEspecial)
    assert sorted([categoria.desccategoria for categoria in response]) == ["ESPAÇO MULHER", "ESPAÇO PET", "NORMAL"]


def test_totalbus_consultar_categoria_empresa(mock_consultar_categoria_empresa, totalbus_login):
    response = totalbus_endpoints.ConsultarCategoriaRequestConfig(totalbus_login).invoke(get_http_executor())
    response_json = response.json()
    assert response_json == mocker.ConsultarCategoriaEmpresa.response()


def test_bloquear_poltronas_conexao_erro_trecho_bloqueado(totalbus_api, mocker):
    mocker.patch.object(TotalbusAPI, "_bloquear_poltronas_async", side_effect=RodoviariaTrechoBloqueadoException())
    with pytest.raises(RodoviariaTrechoBloqueadoException):
        totalbus_api._bloquear_poltronas_conexao(
            {
                "servicoConexao": "131024",
                "localidadeConexao": "CALDAS NOVAS - GO",
                "localidadeConexaoId": 3649,
                "empresa": "RODE ROTAS",
                "empresaId": 2,
                "rutaId": 3629,
                "marcaId": 22,
                "dataCorridaConexao": "2024-07-31",
                "dataSaidaConexao": "2024-07-31",
                "primeiroTrechoPoltronasLivres": 42,
                "primeiroTrechoPoltronasTotal": 42,
                "primeiroTrechoClasse": "EXECUTIVO",
                "primeiroTrechoDataCorrida": "30/07/2024",
                "primeiroTrechoDataSaida": "30/07/2024",
                "primeiroTrechoDataChegada": "31/07/2024",
                "primeiroTrechoHoraSaida": "19:00",
                "primeiroTrechoHoraChegada": "06:50",
                "primeiroTrechoPreco": "140.56",
                "primeiroTrechoPrecoOriginal": "26.48",
                "primeiroTrechoServico": "133014",
                "primeiroTrechoLinha": 4147,
                "primeiroTrechoEmpresa": "RODE ROTAS",
                "primeiroTrechoEmpresaId": 2,
                "primeiroTrechoMarca": 22,
                "primeiroTrechoOrigem": 18697,
                "primeiroTrechoOrigemDescricao": "SAO PAULO (TIETE) - SP",
                "primeiroTrechoDestino": 3649,
                "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
                "primeiroTrechoVende": True,
                "primeiroTrechoIsBpe": True,
                "primeiroTrechoSequencia": 1,
                "segundoTrechoPoltronasLivres": 42,
                "segundoTrechoPoltronasTotal": 42,
                "segundoTrechoClasse": "EXECUTIVO",
                "segundoTrechoDataCorrida": "31/07/2024",
                "segundoTrechoDataSaida": "31/07/2024",
                "segundoTrechoDataChegada": "31/07/2024",
                "segundoTrechoHoraSaida": "06:50",
                "segundoTrechoHoraChegada": "12:00",
                "segundoTrechoPreco": "78.75",
                "segundoTrechoPrecoOriginal": "154.95",
                "segundoTrechoServico": "131024",
                "segundoTrechoLinha": 3629,
                "segundoTrechoEmpresa": "RODE ROTAS",
                "segundoTrechoEmpresaId": 2,
                "segundoTrechoMarca": 22,
                "segundoTrechoOrigem": 3649,
                "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
                "segundoTrechoDestino": 2772,
                "segundoTrechoDestinoDescricao": "BRASILIA - DF",
                "segundoTrechoVende": True,
                "segundoTrechoIsBpe": True,
                "segundoTrechoSequencia": 2,
                "terceiroTrechoVende": False,
                "terceiroTrechoIsBpe": False,
                "vende": True,
                "km": 0.0,
                "cnpj": "18449504000159",
                "conexionCtrlId": 131495,
                "conexionGrupo": 1131425,
                "bpe": True,
            },
            1,
            [1],
            "-1",
        )


@pytest.mark.parametrize(
    "bloqueio_poltronas, expected",
    [
        ({1: SimpleNamespace(duracao=80)}, 80),
        (({1: SimpleNamespace(duracao=60)}, {1: SimpleNamespace(duracao=80)}), 60),
        ({}, 10),
    ],
)
def test_get_tempo_limite_bloqueio(totalbus_api, bloqueio_poltronas, expected):
    assert totalbus_api._get_tempo_limite_bloqueio(bloqueio_poltronas) == expected


def test_get_map_categoria_especial_empresa_buser(
    totalbus_api, mock_consultar_categoria_servico, company_categorias_especiais
):
    tc = baker.prepare(
        TrechoClasse,
        origem__id_external="12",
        destino__id_external="13",
        external_id="100",
        provider_data='{"dataCorrida": "2025-01-01"}',
    )
    result = totalbus_api._get_map_categoria_especial_empresa_buser(tc)
    assert result == {"1": "normal", "30": "espaco_mulher", "33": "pcd", "-1": "normal"}


def test_get_map_categoria_especial_empresa_buser_check_params(totalbus_api, mocker, company_categorias_especiais):
    mock_request = mocker.patch.object(TotalbusAPI, "consultar_categoria_corrida_request", return_value=[])
    tc = baker.prepare(
        TrechoClasse,
        origem__id_external="12",
        destino__id_external="13",
        external_id="100",
        provider_data='{"dataCorrida": "2025-01-01"}',
    )
    result = totalbus_api._get_map_categoria_especial_empresa_buser(tc)
    mock_request.assert_called_with(
        models.ServicoTrecho.parse_obj(
            {
                "origem": 12,
                "destino": 13,
                "data": "2025-01-01",
                "servico": 100,
                "preco_conexao": None,
                "sequencia": None,
                "conexao_id": None,
                "conexao_grupo_id": None,
            }
        )
    )
    assert result == {"1": "normal", "30": "espaco_mulher", "33": "pcd", "-1": "normal"}


def test_get_map_categoria_especial_empresa_buser_check_params_conexao(
    totalbus_api, mocker, company_categorias_especiais
):
    mock_request = mocker.patch.object(TotalbusAPI, "consultar_categoria_corrida_request", return_value=[])
    provider_data = {
        "dataCorrida": "2025-01-01",
        "conexao": {
            "servicoConexao": "131024",
            "localidadeConexao": "CALDAS NOVAS - GO",
            "localidadeConexaoId": 3649,
            "empresa": "RODE ROTAS",
            "empresaId": 2,
            "rutaId": 3629,
            "marcaId": 22,
            "dataCorridaConexao": "2024-07-31",
            "dataSaidaConexao": "2024-07-31",
            "primeiroTrechoPoltronasLivres": 42,
            "primeiroTrechoPoltronasTotal": 42,
            "primeiroTrechoClasse": "EXECUTIVO",
            "primeiroTrechoDataCorrida": "30/07/2024",
            "primeiroTrechoDataSaida": "30/07/2024",
            "primeiroTrechoDataChegada": "31/07/2024",
            "primeiroTrechoHoraSaida": "19:00",
            "primeiroTrechoHoraChegada": "06:50",
            "primeiroTrechoPreco": "140.56",
            "primeiroTrechoPrecoOriginal": "26.48",
            "primeiroTrechoServico": "133014",
            "primeiroTrechoLinha": 4147,
            "primeiroTrechoEmpresa": "RODE ROTAS",
            "primeiroTrechoEmpresaId": 2,
            "primeiroTrechoMarca": 22,
            "primeiroTrechoOrigem": 18697,
            "primeiroTrechoOrigemDescricao": "SAO PAULO (TIETE) - SP",
            "primeiroTrechoDestino": 3649,
            "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
            "primeiroTrechoVende": True,
            "primeiroTrechoIsBpe": True,
            "primeiroTrechoSequencia": 1,
            "segundoTrechoPoltronasLivres": 42,
            "segundoTrechoPoltronasTotal": 42,
            "segundoTrechoClasse": "EXECUTIVO",
            "segundoTrechoDataCorrida": "31/07/2024",
            "segundoTrechoDataSaida": "31/07/2024",
            "segundoTrechoDataChegada": "31/07/2024",
            "segundoTrechoHoraSaida": "06:50",
            "segundoTrechoHoraChegada": "12:00",
            "segundoTrechoPreco": "78.75",
            "segundoTrechoPrecoOriginal": "154.95",
            "segundoTrechoServico": "131024",
            "segundoTrechoLinha": 3629,
            "segundoTrechoEmpresa": "RODE ROTAS",
            "segundoTrechoEmpresaId": 2,
            "segundoTrechoMarca": 22,
            "segundoTrechoOrigem": 3649,
            "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
            "segundoTrechoDestino": 2772,
            "segundoTrechoDestinoDescricao": "BRASILIA - DF",
            "segundoTrechoVende": True,
            "segundoTrechoIsBpe": True,
            "segundoTrechoSequencia": 2,
            "terceiroTrechoVende": False,
            "terceiroTrechoIsBpe": False,
            "vende": True,
            "km": 0.0,
            "cnpj": "18449504000159",
            "conexionCtrlId": 131495,
            "conexionGrupo": 1131425,
            "bpe": True,
        },
    }
    tc = baker.prepare(
        TrechoClasse,
        origem__id_external="12",
        destino__id_external="13",
        external_id="100",
        provider_data=json.dumps(provider_data),
    )
    result = totalbus_api._get_map_categoria_especial_empresa_buser(tc)
    mock_request.assert_called_with(
        models.ServicoTrecho.parse_obj(
            {
                "origem": 18697,
                "destino": 3649,
                "data": "2024-07-30",
                "servico": 133014,
                "preco_conexao": "140.56",
                "sequencia": 1,
                "conexao_id": 131495,
                "conexao_grupo_id": 1131425,
            }
        )
    )
    assert result == {"1": "normal", "30": "espaco_mulher", "33": "pcd", "-1": "normal"}
