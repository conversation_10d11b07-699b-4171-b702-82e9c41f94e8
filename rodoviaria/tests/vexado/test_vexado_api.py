import json
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from decimal import Decimal as D
from types import SimpleNamespace
from unittest.mock import Mock, patch

import pytest
import time_machine
from django.db import connections
from django.utils import timezone as tz
from model_bakery import baker
from pydantic import parse_obj_as
from responses.matchers import query_param_matcher
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from commons.django_utils import error_str
from commons.taggit_utils import Tags
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.vexado import api as vexado_api
from rodoviaria.api.vexado import endpoints, utils
from rodoviaria.api.vexado import models as vexado_models
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.api.vexado.auth import VexadoAuth
from rodoviaria.api.vexado.exceptions import VexadoAPIError
from rodoviaria.api.vexado.models import PassageiroForm
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.forms.motorista_forms import Motorista as MotoristaForm
from rodoviaria.forms.staff_forms import CheckPaxMultipleForm, VexadoAnonymousLogin
from rodoviaria.models.core import Cidade, Company, LocalEmbarque, Passagem, TipoAssento, TrechoClasse
from rodoviaria.models.vexado import VexadoGrupoClasse, VexadoLogin
from rodoviaria.service.cadastrar_grupos_hibridos_svc import CadastrarGrupoTaskParams
from rodoviaria.service.exceptions import (
    HibridoEmissaoForaDaData,
    PassengerNotRegistered,
    PoltronaJaSelecionadaException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaLocalEmbarqueException,
    RodoviariaOverbookingException,
)
from rodoviaria.tests.utils_testes import _comprar_params
from rodoviaria.tests.vexado import mocker as mocker_vexado


def test_repr(vexado_api):
    assert (
        str(endpoints.BuscarItinerarioCorrida(vexado_api.login))
        == f"VEXADO_BuscarItinerarioCorrida_{vexado_api.company.id}_{vexado_api.company.modelo_venda}"
    )


def test_login(vexado_company):
    with pytest.raises(VexadoLogin.DoesNotExist, match="VexadoLogin matching query does not exist"):
        VexadoAPI(vexado_company)


def test_listar_empresas(vexado_api, mock_login):
    resp = vexado_api.listar_empresas()
    assert isinstance(resp, list)
    assert len(resp) == 4
    assert resp[0] == {"name": "VEXADO APP / VEXADO APP", "id": 1}


def test_lista_empresas_api_marketplace(vexado_company, mock_login):
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.save()
    resp = vexado_api.lista_empresas_api(VexadoAnonymousLogin(modelo_venda="marketplace"))
    assert isinstance(resp, list)
    assert len(resp) == 4
    assert resp[0] == {"name": "VEXADO APP / VEXADO APP", "id": 1}


def test_buscar_servico(mock_buscar_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 10:20", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    servico = resp.servicos[0]
    assert servico.external_id == "21136"
    assert servico.veiculo_andar == 2
    assert servico.veiculo_id == 543
    assert servico.preco == 125.75
    assert servico.vagas == 8
    assert servico.rota_external_id == 1427
    assert servico.classe == "Leito"


def test_buscar_servico_meia_noite(
    mock_buscar_servico,
    mock_buscar_servico_meia_noite,
    vexado_api,
    vexado_grupos_mockado,
):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_tz(to_default_tz(datetime.strptime("2021-11-11 23:59", "%Y-%m-%d %H:%M")), "UTC")

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    servico = resp.servicos[0]
    assert servico.external_id == "21136"
    assert servico.veiculo_andar == 2
    assert servico.veiculo_id == 543
    assert servico.preco == 125.75
    assert servico.vagas == 8
    assert servico.rota_external_id == 1427
    assert servico.provider_data["dataHoraPartida"] == "2021-11-12T00:00:00"


def test_buscar_servico_com_match_de_30_min_no_dia_seguinte(
    mock_buscar_servico,
    mock_buscar_servico_meia_noite,
    vexado_api_marketplace,
    vexado_grupos_mockado,
):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_tz(to_default_tz(datetime.strptime("2021-11-11 23:31", "%Y-%m-%d %H:%M")), "UTC")

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api_marketplace.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    servico = resp.servicos[0]
    assert servico.external_id == "21136"
    assert servico.veiculo_andar == 2
    assert servico.veiculo_id == 543
    assert servico.preco == 125.75
    assert servico.vagas == 8
    assert servico.rota_external_id == 1427
    assert servico.provider_data["dataHoraPartida"] == "2021-11-12T00:00:00"


def test_buscar_servico_nenhum_servico(mock_nenhum_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 22:50", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert resp.servicos == []


def test_buscar_servico_horario_fora_da_tolerancia(mock_buscar_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 21:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 4


def test_buscar_servico_tipo_assento_incorreto(mock_buscar_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 10:20", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 4


def test_buscar_servico_dois_matchs_match_por_classe(
    mock_buscar_servico, vexado_api_marketplace, vexado_grupos_mockado
):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 19:50", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    vexado_api_marketplace.company.company_external_id = 5
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api_marketplace.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    servico = resp.servicos[0]
    assert servico.external_id == "19553"
    assert servico.veiculo_andar == 1
    assert servico.veiculo_id == 972
    assert servico.preco == 125.75
    assert servico.vagas == 48
    assert servico.rota_external_id == 1427
    assert servico.external_datetime_ida == to_default_tz(datetime.strptime("2021-11-11 19:55", "%Y-%m-%d %H:%M"))


def test_buscar_servico_grupo_cancelado(mock_buscar_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 10:20", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito"
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        status=VexadoGrupoClasse.Status.CANCELADO,
        grupo_classe_external_id=21136,  # valor retirado do mocker
    )
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 4


def test_buscar_servico_grupo_fechado(mock_buscar_servico, vexado_api, vexado_grupos_mockado):
    trecho_classe = vexado_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-11-11 10:20", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito"
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        status=VexadoGrupoClasse.Status.FECHADO,
        grupo_classe_external_id=21136,  # valor retirado do mocker
    )
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = vexado_api.buscar_corridas(mocker_vexado.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 4


def test_buscar_servico_retorno_mismatch(vexado_api):
    timezone = "America/Sao_Paulo"
    servico = {
        "idItinerario": "2131",
        "preco": "200.50",
        "taxaEmbarque": "1.50",
        "dataHoraPartida": "2023-01-20T10:15:00",
        "tipoVeiculo": "semi leito",
        "assentosDisponiveis": 13,
    }
    retorno = vexado_api._parse_retorno_buscar_servico([], timezone, [servico])
    assert retorno == BuscarServicoForm.parse_obj(
        {
            "found": False,
            "servicos": [
                {
                    "external_id": servico["idItinerario"],
                    "preco": D("202"),
                    "external_datetime_ida": to_tz(
                        datetime.strptime(servico["dataHoraPartida"], "%Y-%m-%dT%H:%M:%S"),
                        timezone,
                    ),
                    "classe": servico["tipoVeiculo"],
                    "external_company_id": str(vexado_api.company.company_external_id),
                    "vagas": servico["assentosDisponiveis"],
                    "provider_data": servico,
                }
            ],
        }
    )


def test_get_poltronas_livres_uma_poltrona(mock_get_poltronas_livres, mock_login, vexado_api):
    params = mocker_vexado.GetPoltronasLivres.request()
    trecho_classe = baker.make(TrechoClasse)
    resp = vexado_api.get_poltronas_livres(params, trecho_classe)
    assert isinstance(resp, list)
    assert len(resp) == 1


def test_get_poltronas_livres_varias_poltronas(mock_get_poltronas_livres, mock_login, vexado_api):
    params = mocker_vexado.GetPoltronasLivres.request_for_n_poltronas(2)
    trecho_classe = baker.make(TrechoClasse)
    resp = vexado_api.get_poltronas_livres(params, trecho_classe)
    assert isinstance(resp, list)
    assert len(resp) == 2
    assert resp == [10, 11]


def test_get_poltronas_livres_poltronas_insuficientes_hibrido(mock_get_poltronas_livres, mock_login, vexado_api):
    vexado_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_api.company.save()
    params = mocker_vexado.GetPoltronasLivres.request_for_n_poltronas(9)
    trecho_classe = baker.make(TrechoClasse)
    with pytest.raises(
        RodoviariaOverbookingException,
        match="Apenas 8 poltronas disponíveis para esta viagem",
    ):
        vexado_api.get_poltronas_livres(params, trecho_classe)
    assert TrechoClasse.objects.get(id=trecho_classe.id, tags__name=Tags.OVERBOOKING)


def test_get_poltronas_livres_poltronas_insuficientes_marketplace(mock_get_poltronas_livres, mock_login, vexado_api):
    vexado_api.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_api.company.save()
    params = mocker_vexado.GetPoltronasLivres.request_for_n_poltronas(9)
    trecho_classe = baker.make(TrechoClasse)
    with pytest.raises(
        RodoviariaOverbookingException,
        match="Apenas 8 poltronas disponíveis para esta viagem",
    ):
        vexado_api.get_poltronas_livres(params, trecho_classe)
    assert not TrechoClasse.objects.filter(id=trecho_classe.id, tags__name=Tags.OVERBOOKING).exists()


def test_get_map_poltronas(mock_get_poltronas_livres, vexado_api, mock_login, vexado_trechoclasses):
    resp = vexado_api.get_map_poltronas(trechoclasse_id=vexado_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {
        10: "livre",
        11: "livre",
        47: "livre",
        48: "livre",
        49: "ocupada",
        50: "livre",
        51: "livre",
        53: "livre",
        54: "livre",
    }


def test_get_map_poltronas_classe_mista(
    mock_get_poltronas_livres_classe_mista, vexado_api, mock_login, vexado_trechoclasses
):
    vexado_trechoclasses.ida.provider_data = '{"tipoVeiculo": "LEITO"}'
    vexado_trechoclasses.ida.save()
    resp = vexado_api.get_map_poltronas(trechoclasse_id=vexado_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {8: "livre", 9: "ocupada"}
    vexado_trechoclasses.ida.provider_data = '{"tipoVeiculo": "LEITO_INDIVIDUAL"}'
    vexado_trechoclasses.ida.save()
    resp = vexado_api.get_map_poltronas(trechoclasse_id=vexado_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {3: "livre", 4: "ocupada"}


def test_reserva_dict_to_comprar_params(vexado_api, mock_get_poltronas_livres):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params, passagens = vexado_api._reserva_dict_to_comprar_params(tc, params_comprar, usar_preco_api=False)
    assert params["formasPagamentoDto"][0]["valor"] == params_comprar.valor_cheio * 2
    assert params["reservas"][0]["trechoOrigemId"] == 9123
    assert params["reservas"][0]["trechoDestinoId"] == 1236
    assert list(passagens.values())[0].valor_cheio == params_comprar.valor_cheio


def test_reserva_dict_to_comprar_params_sem_rg(vexado_api, mock_get_poltronas_livres):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    params_comprar.passageiros[0].rg_number = ""
    params, _ = vexado_api._reserva_dict_to_comprar_params(tc, params_comprar, usar_preco_api=False)
    assert params["reservas"][0]["passageiroDto"]["documentoComFoto"] == params_comprar.passageiros[0].cpf


def test_reserva_dict_to_comprar_params_usar_preco_api(vexado_api, mock_get_poltronas_livres):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    preco_rodoviaria = 125.25
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=preco_rodoviaria,
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    quant_passageiros = 2
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=quant_passageiros)
    params, passagens = vexado_api._reserva_dict_to_comprar_params(tc, params_comprar, usar_preco_api=True)
    assert params["formasPagamentoDto"][0]["valor"] == preco_rodoviaria * 2
    assert list(passagens.values())[0].valor_cheio == params_comprar.valor_cheio
    assert Passagem.objects.filter(tags__name="pago_preco_api", trechoclasse_integracao=tc).count() == quant_passageiros


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_comprar(mock_comprar, vexado_api, mock_get_poltronas_livres):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    response = vexado_api.comprar(params_comprar, from_add_pax=True)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    assert passagens.count() == 2
    poltronas = [55, 52]  # valores vem do mocker da resposta do mapa de poltronas
    localizadores = [170887, 170886]
    assert isinstance(response["passagens"], list)
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.pedido_external_id == "116896"
        assert passagem_buser.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
        assert passagem_buser.poltrona_external_id == poltronas[index] == passagem["poltrona"]
        assert int(passagem_buser.localizador) == localizadores[index] == passagem["localizador"]
        assert passagem_buser.buseiro_internal_id == 15 + index == passagem["buseiro_id"]
        assert not passagem_buser.erro
        assert passagem_buser.status == Passagem.Status.CONFIRMADA == passagem["status"]
        assert passagem_buser.id == passagem["id"]


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_comprar_marketplace(
    mock_comprar,
    vexado_api_marketplace,
    mock_bloquear_poltrona,
    mock_get_poltronas_livres,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    response = vexado_api_marketplace.comprar(params_comprar)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    assert passagens.count() == 2
    assert isinstance(response["passagens"], list)
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.status == Passagem.Status.CONFIRMADA == passagem["status"]


def test_comprar_marketplace_extra_poltronas(
    mock_comprar_unica_passagem,
    vexado_api_marketplace,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    params_comprar.extra_poltronas = {
        "id": 570555,
        "poltronaId": 793227,
        "uuid": "6c5729b1-db8a-4a46-a397-2e942d1a8ecf",
        "preco": "620.89",
    }
    response = vexado_api_marketplace.comprar(params_comprar)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    assert passagens.count() == 1
    assert isinstance(response["passagens"], list)
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.status == Passagem.Status.CONFIRMADA == passagem["status"]


def test_comprar_conexao_mesma_poltrona_extra_poltronas(mock_comprar_conexao_mesma_poltrona, vexado_api_marketplace):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    params_comprar.extra_poltronas = {
        "id": 570555,
        "poltronaId": 793227,
        "uuid": "6c5729b1-db8a-4a46-a397-2e942d1a8ecf",
        "preco": "620.89",
    }
    response = vexado_api_marketplace.comprar(params_comprar)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    assert passagens.count() == 2
    poltrona = 55  # valores vem do mocker da resposta do mapa de poltronas
    localizadores = [1131873, 1131874]
    assert isinstance(response["passagens"], list)
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.pedido_external_id == "116896"
        assert passagem_buser.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
        assert passagem_buser.poltrona_external_id == poltrona == passagem["poltrona"]
        assert int(passagem_buser.localizador) == localizadores[index] == passagem["localizador"]
        assert passagem_buser.buseiro_internal_id == passagem["buseiro_id"]
        assert not passagem_buser.erro
        assert passagem_buser.status == Passagem.Status.CONFIRMADA == passagem["status"]
        assert passagem_buser.id == passagem["id"]
    assert passagens[0].origem != passagens[1].destino
    assert passagens[0].destino == passagens[1].origem
    assert passagens[0].valor_cheio == passagens[1].valor_cheio


def test_comprar_conexao_mesma_poltrona(mock_comprar_conexao_mesma_poltrona, vexado_api, mock_get_poltronas_livres):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    response = vexado_api.comprar(params_comprar, from_add_pax=True)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    assert passagens.count() == 2
    poltrona = 55  # valores vem do mocker da resposta do mapa de poltronas
    localizadores = [1131873, 1131874]
    assert isinstance(response["passagens"], list)
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.pedido_external_id == "116896"
        assert passagem_buser.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
        assert passagem_buser.poltrona_external_id == poltrona == passagem["poltrona"]
        assert int(passagem_buser.localizador) == localizadores[index] == passagem["localizador"]
        assert passagem_buser.buseiro_internal_id == passagem["buseiro_id"]
        assert not passagem_buser.erro
        assert passagem_buser.status == Passagem.Status.CONFIRMADA == passagem["status"]
        assert passagem_buser.id == passagem["id"]
    assert passagens[0].origem != passagens[1].destino
    assert passagens[0].destino == passagens[1].origem
    assert passagens[0].valor_cheio == passagens[1].valor_cheio


def test_comprar_modelo_hibrido_from_add_pax(vexado_api, vexado_company):
    trecho_classe_id = 553
    tc = baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=2),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    with patch.object(
        VexadoAPI,
        "_reserva_dict_to_comprar_params",
        return_value=("request_params", "passagens_cadastradas"),
    ) as mock_reserva_dict_to_comprar_params, patch.object(VexadoAPI, "_efetuar_compra") as mock_efetuar_compra:
        vexado_api.comprar(params, from_add_pax=True)
    mock_reserva_dict_to_comprar_params.assert_called_once_with(tc, params, False, None)
    mock_efetuar_compra.assert_called_once_with(trecho_classe_id, "request_params", "passagens_cadastradas", None)


def test_comprar_modelo_hibrido_ja_passou_do_horario_da_viagem(vexado_api, vexado_company):
    trecho_classe_id = 553
    tc = baker.make(
        TrechoClasse,
        datetime_ida=tz.now() - timedelta(hours=2),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    with patch.object(
        VexadoAPI,
        "_reserva_dict_to_comprar_params",
        return_value=("request_params", "passagens_cadastradas"),
    ) as mock_reserva_dict_to_comprar_params, patch.object(VexadoAPI, "_efetuar_compra") as mock_efetuar_compra:
        vexado_api.comprar(params, from_add_pax=True)
    mock_reserva_dict_to_comprar_params.assert_called_once_with(tc, params, False, None)
    mock_efetuar_compra.assert_called_once_with(trecho_classe_id, "request_params", "passagens_cadastradas", None)


def test_comprar_modelo_hibrido_not_from_add_pax(vexado_api, vexado_company):
    trecho_classe_id = 553
    baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=2),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    with patch.object(
        VexadoAPI,
        "_reserva_dict_to_comprar_params",
        return_value=("request_params", "passagens_cadastradas"),
    ) as mock_reserva_dict_to_comprar_params, patch.object(VexadoAPI, "_efetuar_compra") as mock_efetuar_compra:
        vexado_api.comprar(params, from_add_pax=False)
    mock_reserva_dict_to_comprar_params.assert_not_called()
    mock_efetuar_compra.assert_not_called()


def test_comprar_modelo_hibrido_com_mais_de_tres_horas(vexado_api, vexado_company):
    trecho_classe_id = 553
    baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=8),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    with patch.object(
        VexadoAPI,
        "_reserva_dict_to_comprar_params",
        return_value=("request_params", "passagens_cadastradas"),
    ) as mock_reserva_dict_to_comprar_params, patch.object(
        VexadoAPI, "_efetuar_compra"
    ) as mock_efetuar_compra, pytest.raises(
        HibridoEmissaoForaDaData,
        match="Emissão do modelo Hibrido só pode ser feita faltando menos de 3h para o embarque",
    ):
        vexado_api.comprar(params, from_add_pax=True)
    mock_reserva_dict_to_comprar_params.assert_not_called()
    mock_efetuar_compra.assert_not_called()


def test_comprar_modelo_hibrido_com_mais_de_tres_horas_permite_excecao(vexado_api, vexado_company, override_config):
    trecho_classe_id = 553
    tc = baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=8),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    with patch.object(
        VexadoAPI,
        "_reserva_dict_to_comprar_params",
        return_value=("request_params", "passagens_cadastradas"),
    ) as mock_reserva_dict_to_comprar_params, patch.object(
        VexadoAPI, "_efetuar_compra"
    ) as mock_efetuar_compra, override_config(TRAVELS_PERMITE_EMISSAO_MENOS_DE_3_HORAS="123"):
        vexado_api.comprar(params, from_add_pax=True)
    mock_reserva_dict_to_comprar_params.assert_called_once_with(tc, params, False, None)
    mock_efetuar_compra.assert_called_once_with(trecho_classe_id, "request_params", "passagens_cadastradas", None)


def test_comprar_modelo_marketplace(vexado_api, vexado_company):
    trecho_classe_id = 553
    tc = baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=2),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.save()
    with patch.object(
        VexadoAPI, "_reserva_dict_to_comprar_params"
    ) as mock_reserva_dict_to_comprar_params, patch.object(VexadoAPI, "_efetuar_compra") as mock_efetuar_compra:
        mock_reserva_dict_to_comprar_params.return_value = (
            "request_params",
            "passagens_cadastradas",
        )
        vexado_api.comprar(params)
    mock_reserva_dict_to_comprar_params.assert_called_once_with(tc, params, False, None)
    mock_efetuar_compra.assert_called_once_with(trecho_classe_id, "request_params", "passagens_cadastradas", None)


def test_comprar_modelo_marketplace_usar_preco_api(vexado_api, vexado_company):
    trecho_classe_id = 553
    tc = baker.make(
        TrechoClasse,
        datetime_ida=tz.now() + timedelta(hours=2),
        trechoclasse_internal_id=trecho_classe_id,
    )
    params = SimpleNamespace(trechoclasse_id=trecho_classe_id, travel_id=123, extra_poltronas=None)
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.features = [Company.Feature.USAR_PRECO_API]
    vexado_company.save()
    with patch.object(
        VexadoAPI, "_reserva_dict_to_comprar_params"
    ) as mock_reserva_dict_to_comprar_params, patch.object(VexadoAPI, "_efetuar_compra") as mock_efetuar_compra:
        mock_reserva_dict_to_comprar_params.return_value = (
            "request_params",
            "passagens_cadastradas",
        )
        vexado_api.comprar(params)
    mock_reserva_dict_to_comprar_params.assert_called_once_with(tc, params, True, None)
    mock_efetuar_compra.assert_called_once_with(trecho_classe_id, "request_params", "passagens_cadastradas", None)


def test_comprar_error_nao_encontra_passagem_na_api(
    mock_comprar_error,
    vexado_api,
    mock_get_poltronas_livres,
    mock_lista_reservas_viagem_detalhadas_nenhuma_reserva,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    with pytest.raises(RodoviariaException) as exc:
        vexado_api.comprar(params_comprar, from_add_pax=True)
    expected_error_msg = "Vexado erro: Ocorreu um erro interno"
    assert error_str(exc.value) == expected_error_msg
    passagens = Passagem.objects.filter(travel_internal_id=1521)  # valor vem do _comprar_params
    for passagem in passagens:
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro == expected_error_msg


@pytest.mark.parametrize("mock_recuperar_pedido", [(406037, 5)], indirect=True)
def test_comprar_timeout_encontra_passagem_na_api(
    mock_comprar_no_json_error_504,
    vexado_api,
    mock_get_poltronas_livres,
    mock_lista_reservas_viagem_detalhadas,
    mock_recuperar_pedido,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    vexado_api.comprar(params_comprar, from_add_pax=True)
    passagens = Passagem.objects.filter(travel_internal_id=params_comprar.travel_id)  # valor vem do _comprar_params
    assert len(passagens) == 2
    for passagem in passagens:
        assert passagem.status == Passagem.Status.CONFIRMADA


def test_compra_poltrona_ja_selecionada(
    vexado_api,
    mock_comprar_poltrona_ja_reservada,
    mock_lista_reservas_viagem_detalhadas_nenhuma_reserva,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
        active=True,
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params_comprar.passageiros[0].name = "Passageiro com Poltrona Bloqueada"
    response = vexado_api.comprar(params_comprar, from_add_pax=True)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    poltronas = [11, 47]
    for index, passagem in enumerate(response["passagens"]):
        passagem_buser = passagens[index]
        assert passagem_buser.poltrona_external_id == poltronas[index] == passagem["poltrona"]
        assert passagem_buser.status == Passagem.Status.CONFIRMADA


def test_compra_erro_poltrona_ja_bloqueada(
    vexado_api_marketplace,
    mock_bloquear_poltrona_ja_bloqueada_segunda_tentativa,
    mock_desbloquear_poltrona,
    mock_get_poltronas_livres,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params_comprar.passageiros[0].name = "Passageiro com Poltrona Bloqueada"
    with pytest.raises(
        PoltronaJaSelecionadaException,
        match="Não foi possível reservar a poltrona, porque já foi bloqueado temporariamente para venda!",
    ):
        vexado_api_marketplace.comprar(params_comprar)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "id"
    )
    for passagem in passagens:
        passagem.refresh_from_db()
        assert passagem.status == Passagem.Status.INCOMPLETA


def test_compra_erro_selecionar_poltrona(
    vexado_api,
    mock_comprar_erro_selecionar_poltronas,
    mock_lista_reservas_viagem_detalhadas_nenhuma_reserva,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params_comprar.passageiros[0].name = "Passageiro com Poltrona Bloqueada"
    with pytest.raises(RodoviariaException, match="Erro ao selecionar poltronas"):
        vexado_api.comprar(params_comprar, from_add_pax=True)


def test_compra_erro_selecionar_poltronas_insuficientes(
    vexado_api,
    mock_comprar_erro_selecionar_poltronas_insuficientes,
    mock_lista_reservas_viagem_detalhadas_nenhuma_reserva,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params_comprar.passageiros[0].name = "Passageiro com Poltrona Bloqueada"
    with pytest.raises(RodoviariaException, match="Erro ao selecionar poltronas"):
        vexado_api.comprar(params_comprar, from_add_pax=True)


def test_compra_com_erro_no_recuperar_pedido(
    vexado_api, mock_get_poltronas_livres, mock_comprar_com_erro_recuperar_pedido
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    params_comprar.passageiros[0].name = "Passageiro com Poltrona Bloqueada"
    with pytest.raises(RodoviariaException, match="Vexado erro: Empresa 495 bloqueada."):
        vexado_api.comprar(params_comprar, from_add_pax=True)
    expected_error_msg = "Vexado erro: Empresa 495 bloqueada."
    passagens = Passagem.objects.filter(travel_internal_id=1521)  # valor vem do _comprar_params
    for passagem in passagens:
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro == expected_error_msg
        assert passagem.tags_set() == {"passagem_com_erro_confirmada_na_api"}


def test_atribui_bpe(vexado_api):
    passagem = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=baker.make("rodoviaria.TrechoClasse", grupo=baker.make("rodoviaria.Grupo")),
    )
    dados = mocker_vexado.MockRecuperarPedido.response()
    dados_reserva = dados.get("reservas")[0]
    vexado_api._atribui_bpe(passagem, dados_reserva)
    passagem.refresh_from_db()
    assert (
        passagem.bpe_qrcode
        == "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088711341082995&tpAmb=2"
    )
    assert passagem.bpe_em_contingencia is True
    assert passagem.data_autorizacao == datetime(2020, 6, 17, 20, 54, 33, tzinfo=timezone.utc)
    assert passagem.preco_base == D("125.25")
    assert passagem.taxa_embarque == D("0.00")
    assert passagem.outras_taxas == D("4.23")
    assert passagem.preco_rodoviaria == D("125.25")
    assert passagem.outros_tributos == "ICMS 15.03 (12,00%) OUTROS TRIB: 1.99"
    assert passagem.chave_bpe == "53211012402506000106630000017088711341082995"
    assert passagem.numero_bpe == "08871"
    assert passagem.serie_bpe == "000"
    assert passagem.seguro == D("0.00")
    assert passagem.pedagio == D("0.00")
    assert passagem.protocolo_autorizacao == "131200000185981"
    assert passagem.prefixo == "*********"
    assert passagem.cnpj == "12.402.506/0001-06"
    assert passagem.linha == "Taguatinga-DF à Itacarambi-MG"
    assert passagem.trechoclasse_integracao.grupo.linha == "Taguatinga-DF à Itacarambi-MG"
    assert passagem.endereco_empresa == "Rua Francisco Londres , ***, Varadouro - João Pessoa-PB, CEP: 58010-150"
    assert passagem.inscricao_estadual == "133223091115"
    assert passagem.nome_agencia == "buser integração"
    assert passagem.bpe_monitriip_code == "41250508336161000162630000197851011"


def test_verifica_poltrona_marketplace(
    vexado_api_marketplace, mock_login, mock_get_poltronas_livres, mock_bloquear_poltrona
):
    # precisam ser esses valores para o mock do get_poltronas funcionar
    id_itinerario = 21136
    origem_id = 1
    destino_id = 2932
    origem = baker.make(
        "rodoviaria.LocalEmbarque", id_external=origem_id
    )  # precisa ser esse valor par o mock do get_poltronas funcionar
    destino = baker.make(
        "rodoviaria.LocalEmbarque", id_external=destino_id
    )  # precisa ser esse valor par o mock do get_poltronas funcionar
    baker.make(
        "rodoviaria.TrechoClasse",
        origem=origem,
        destino=destino,
        trechoclasse_internal_id=212312312,
        provider_data='{"data": "2021-12-12", "trechoOrigemId": 1, "trechoDestinoId": 2}',
        external_id=id_itinerario,
    )
    poltronas = vexado_api_marketplace.verifica_poltrona(
        VerificarPoltronaForm(trechoclasse_id=212312312, passageiros=2)
    )
    assert poltronas == [10, 11]


def test_verifica_poltrona_hibrido(vexado_api, mock_login, mock_get_poltronas_livres):
    # precisam ser esses valores para o mock do get_poltronas funcionar
    id_itinerario = 21136
    origem_id = 1
    destino_id = 2932
    origem = baker.make(
        "rodoviaria.LocalEmbarque", id_external=origem_id
    )  # precisa ser esse valor par o mock do get_poltronas funcionar
    destino = baker.make(
        "rodoviaria.LocalEmbarque", id_external=destino_id
    )  # precisa ser esse valor par o mock do get_poltronas funcionar
    baker.make(
        "rodoviaria.TrechoClasse",
        origem=origem,
        destino=destino,
        trechoclasse_internal_id=212312312,
        provider_data='{"data": "2021-12-12", "trechoOrigemId": 1, "trechoDestinoId": 2}',
        external_id=id_itinerario,
    )
    poltronas = vexado_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=212312312, passageiros=2))
    assert poltronas == [10, 11]


def test_buscar_itinerario_corrida(vexado_api, mock_recuperar_itinerario):
    tc_mock = Mock()
    tc_mock.external_id = "23123"  # valor vem do conftest mock_recuperar_itinerario
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = datetime(2021, 9, 22, 10, 0)
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    itinerario = vexado_api.itinerario(tc_mock.external_id)
    assert 14 == len(itinerario.parsed)
    assert "127960acb1a47f906219138b320d1c161b775" == itinerario.parsed.hash

    checkpoint = itinerario.parsed[0]
    assert 0 == checkpoint.duracao
    assert 0 == checkpoint.tempo_embarque
    assert Decimal("0") == checkpoint.distancia
    assert "Brasília" == checkpoint.local.nome_cidade
    assert "DF" == checkpoint.local.uf
    assert "Brasília - DF" == checkpoint.local.descricao

    checkpoint = itinerario.parsed[1]
    assert 30 * 60 == checkpoint.duracao
    assert 10 * 60 == checkpoint.tempo_embarque
    assert Decimal("90.50") == checkpoint.distancia
    assert "Valparaíso de Goiás" == checkpoint.local.nome_cidade
    assert "GO" == checkpoint.local.uf
    assert "Valparaíso de Goiás - GO" == checkpoint.local.descricao

    checkpoint = itinerario.parsed[2]
    assert 5 * 60 == checkpoint.duracao
    assert 0 * 60 == checkpoint.tempo_embarque  # parada rapida
    assert Decimal("100") == checkpoint.distancia
    assert "Luziânia" == checkpoint.local.nome_cidade
    assert "GO" == checkpoint.local.uf
    assert "Luziânia - GO" == checkpoint.local.descricao


def test_buscar_itinerario_origem_e_destino_repetidos(vexado_api, mock_recuperar_itinerario_com_locais_repetidos):
    tc_mock = Mock()
    tc_mock.external_id = "23123"  # valor vem do conftest mock_recuperar_itinerario
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = datetime(2021, 9, 22, 10, 0)
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    itinerario = vexado_api.itinerario(tc_mock.external_id)
    assert 2 == len(itinerario.parsed)
    assert "1272620ccac08d619c8620c284d7e939098e" == itinerario.parsed.hash

    checkpoint = itinerario.parsed[0]
    assert 0 == checkpoint.duracao
    assert 0 == checkpoint.tempo_embarque
    assert Decimal("0") == checkpoint.distancia
    assert "Brasília" == checkpoint.local.nome_cidade
    assert "DF" == checkpoint.local.uf
    assert "Brasília - DF" == checkpoint.local.descricao

    checkpoint = itinerario.parsed[1]
    assert 40 * 60 == checkpoint.duracao
    assert 0 == checkpoint.tempo_embarque
    assert Decimal("90.50") == checkpoint.distancia
    assert "Valparaíso de Goiás" == checkpoint.local.nome_cidade
    assert "GO" == checkpoint.local.uf
    assert "Valparaíso de Goiás - GO" == checkpoint.local.descricao


def _tc_mock():
    tc_mock = Mock()
    tc_mock.external_id = 23123
    tc_mock.provider_data = None
    tc_mock.grupo.datetime_ida = datetime(2020, 1, 1, 10, 0)
    tc_mock.origem.cidade.timezone = "America/Sao_Paulo"
    return tc_mock


def test_buscar_itinerario_corrida_nao_localizado(vexado_api, mock_recuperar_itinerario_nao_encontrado):
    with pytest.raises(RodoviariaException) as ex:
        vexado_api.itinerario(_tc_mock().external_id)
    assert ex.value.message == "Vexado erro: Ocorreu um erro interno"


def test_buscar_itinerario_connection_error(vexado_api, mock_login):
    with pytest.raises(RodoviariaConnectionError) as ex:
        vexado_api.itinerario(_tc_mock().external_id)
    assert (
        ex.value.message
        == "vexado 'GET http://vexado.com.br/itinerario/23123/empresa/5' json=None params=None connection error"
    )


def test_get_token(mocker, mock_login_twice):
    # Dada uma empresa com login
    company = baker.make("rodoviaria.Company", url_base="http://vexado.com.br")
    client_login = baker.make(
        "rodoviaria.VexadoLogin", company=company, password="buser", user="vexado", site="AdminServer.com"
    )
    auth = VexadoAuth.from_client(client_login)
    request = mocker.MagicMock()
    request.headers = {}
    # estado inicial do token é vazio
    assert auth._token is None

    # ao chamar a autenticacao
    auth(request)

    # busca o primeiro login na API da integração, mesmo que force_update seja falso
    assert auth._token == {"auth": mocker_vexado.TOKEN_1, "new_login": False}
    assert request.headers["Authorization"] == f"Bearer {mocker_vexado.TOKEN_1}"
    assert request.headers["site"] == "AdminServer.com"

    # Se o cache não expirou e não forçou atualização, pega do cache
    auth(request)
    assert auth._token == {"auth": mocker_vexado.TOKEN_1, "new_login": False}
    assert request.headers["Authorization"] == f"Bearer {mocker_vexado.TOKEN_1}"
    assert request.headers["site"] == "AdminServer.com"

    # se forçar pegar novo token, busca novamente na API
    auth.get_token(True)
    auth(request)
    assert auth._token == {"auth": mocker_vexado.TOKEN_2, "new_login": True}
    assert request.headers["Authorization"] == f"Bearer {mocker_vexado.TOKEN_2}"
    assert request.headers["site"] == "AdminServer.com"

    # 2 chamadas, uma no primeiro login e outra pra atualizar
    mock_login_twice.assert_call_count("http://vexado.com.br/auth/signin", 2)


def test_utils_fill_duracao(vexado_api):
    assert utils.fill_duracao("00:00") == "00:00"
    assert utils.fill_duracao(None) == "00:00"
    assert utils.fill_duracao("1:3") == "01:03"
    assert utils.fill_duracao("4:30") == "04:30"
    assert utils.fill_duracao("01:0") == "01:00"
    assert utils.fill_duracao("3") == "03:00"


def test_mover_posicao_checkpoint_para_baixo(vexado_api, mock_mover_posicao_checkpoint_para_baixo):
    response = vexado_api.mover_posicao_checkpoint_para_baixo(mock_mover_posicao_checkpoint_para_baixo.trecho_id)
    assert response is True


def test_match_local_de_embarque(vexado_api, mock_login, vexado_company, mock_buscar_cidades_por_nome):
    qtd_locais = LocalEmbarque.objects.count()
    qtd_cidades = Cidade.objects.count()
    local_embarque = vexado_api.match_local_de_embarque(
        111,
        222,
        "America/Sao_Paulo",
        "São José dos Campos",
        "3549904",
        vexado_company,
        vexado_company.company_internal_id,
    )
    qtd_locais_novo = LocalEmbarque.objects.count()
    qtd_cidades_novo = Cidade.objects.count()
    assert qtd_cidades_novo == qtd_cidades + 1
    assert qtd_locais_novo == qtd_locais + 1
    assert local_embarque.id_external == 5843
    assert local_embarque.nickname == "São José dos Campos - SP"
    assert local_embarque.local_embarque_internal_id == 111
    cidade = local_embarque.cidade
    assert cidade.id_external == 5843
    assert cidade.name == "São José dos Campos"
    assert cidade.company == vexado_company
    assert cidade.cidade_internal_id == 222
    assert cidade.timezone == "America/Sao_Paulo"


def test_match_local_de_embarque_ja_foi_criado(vexado_api, vexado_company):
    local_embarque_internal_id = 8392
    cidade = baker.make("rodoviaria.Cidade", company=vexado_company)
    local_embarque_existente = baker.make(
        "rodoviaria.LocalEmbarque",
        cidade=cidade,
        local_embarque_internal_id=local_embarque_internal_id,
    )
    local_embarque = vexado_api.match_local_de_embarque(
        local_embarque_internal_id,
        5342,
        "America/Sao_Paulo",
        "São José dos Campos",
        "3549904",
        vexado_company,
        vexado_company.company_internal_id,
    )
    assert local_embarque == local_embarque_existente


def test_match_local_de_embarque_cidade_ja_foi_criado(
    vexado_api, mock_login, vexado_company, mock_buscar_cidades_por_nome
):
    local_embarque_internal_id = 8392
    cidade = baker.make("rodoviaria.Cidade", company=vexado_company, cidade_internal__id=5342)
    qtd_locais = LocalEmbarque.objects.count()
    qtd_cidades = Cidade.objects.count()

    local_embarque = vexado_api.match_local_de_embarque(
        local_embarque_internal_id,
        5342,
        "America/Sao_Paulo",
        "São José dos Campos",
        "3549904",
        vexado_company,
        vexado_company.company_internal_id,
    )

    qtd_locais_novo = LocalEmbarque.objects.count()
    qtd_cidades_novo = Cidade.objects.count()
    assert qtd_cidades_novo == qtd_cidades
    assert qtd_locais_novo == qtd_locais + 1
    assert local_embarque.cidade == cidade


def test_match_local_de_embarque_mais_de_uma_cidade(
    vexado_api, mock_login, vexado_company, mock_buscar_cidades_por_nome
):
    local_embarque_internal_id = 8392
    cidade = baker.make("rodoviaria.Cidade", company=vexado_company, cidade_internal__id=5342)
    baker.make("rodoviaria.Cidade", company=vexado_company, cidade_internal__id=5342)  # cidade duplicada
    qtd_locais = LocalEmbarque.objects.count()
    qtd_cidades = Cidade.objects.count()

    local_embarque = vexado_api.match_local_de_embarque(
        local_embarque_internal_id,
        5342,
        "America/Sao_Paulo",
        "São José dos Campos",
        "3549904",
        vexado_company,
        vexado_company.company_internal_id,
    )

    qtd_locais_novo = LocalEmbarque.objects.count()
    qtd_cidades_novo = Cidade.objects.count()
    assert qtd_cidades_novo == qtd_cidades
    assert qtd_locais_novo == qtd_locais + 1
    assert local_embarque.cidade == cidade


def test_match_local_de_embarque_desconsidera_local_embarque_com_internal_id_none(
    vexado_api, mock_login, vexado_company, mock_buscar_cidades_por_nome
):
    local_embarque_internal_id = None
    cidade = baker.make("rodoviaria.Cidade", company=vexado_company, cidade_internal__id=5342)
    baker.make(
        "rodoviaria.LocalEmbarque",
        cidade=cidade,
        local_embarque_internal_id=local_embarque_internal_id,
    )
    qtd_locais = LocalEmbarque.objects.count()
    qtd_cidades = Cidade.objects.count()

    local_embarque = vexado_api.match_local_de_embarque(
        local_embarque_internal_id,
        5342,
        "America/Sao_Paulo",
        "São José dos Campos",
        "3549904",
        vexado_company,
        vexado_company.company_internal_id,
    )

    qtd_locais_novo = LocalEmbarque.objects.count()
    qtd_cidades_novo = Cidade.objects.count()
    assert qtd_cidades_novo == qtd_cidades
    assert qtd_locais_novo == qtd_locais
    assert local_embarque is None


def test_match_local_de_embarque_vazio(vexado_api, mock_login, vexado_company, mock_buscar_cidades_por_nome_vazio):
    with pytest.raises(RodoviariaLocalEmbarqueException):
        vexado_api.match_local_de_embarque(
            111,
            222,
            "America/Sao_Paulo",
            "São João do Biriti",
            "3549904",
            vexado_company,
            vexado_company.company_internal_id,
        )


def test_buscar_corridas(vexado_api, mock_login, mock_buscar_servico):
    request_params = {"origem": 1, "destino": 2932, "data": "2021-11-11"}
    corridas = vexado_api.buscar_corridas(request_params).servicos
    for corrida in corridas:
        assert list(corrida.dict(exclude_unset=True).keys()) == [
            "external_id",
            "preco",
            "external_datetime_ida",
            "external_datetime_chegada",
            "vagas",
            "classe",
            "capacidade_classe",
            "distancia",
            "provider_data",
            "linha",
            "rota_external_id",
        ]
    assert corrida == ServicoForm.parse_obj(
        {
            "linha": "Taguatinga à Itacarambi",
            "external_datetime_ida": datetime.fromisoformat("2021-11-11T19:55:00"),
            "external_id": "19553",
            "preco": D("125.75"),
            "vagas": 48,
            "provider_data": corrida.provider_data,
            "external_datetime_chegada": datetime.fromisoformat("2021-11-12T05:10:00"),
            "classe": "SEMI LEITO",
            "capacidade_classe": 48,
            "distancia": None,
            "rota_external_id": 1427,
        }
    )


def test_buscar_corridas_com_rota_external_id(mock_login, vexado_api, mock_buscar_servico):
    request_params = {"origem": 1, "destino": 2932, "data": "2021-11-11"}
    corridas = vexado_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 4


def test_buscar_corridas_com_rota_external_id_diferente(mock_login, vexado_api, mock_buscar_servico):
    request_params = {"origem": 1, "destino": 2932, "data": "2021-11-11"}
    corridas = vexado_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 4


def test_rota_id_from_trecho_classe(vexado_api):
    trecho_classe = baker.make("rodoviaria.TrechoClasse", grupo=baker.make("rodoviaria.Grupo"))
    # sem trecho_classe
    assert vexado_api.rota_id_from_trecho_classe(None) is None
    # grupo sem linha e trecho_classe sem provider_data
    assert vexado_api.rota_id_from_trecho_classe(trecho_classe) is None
    # grupo sem linha e trecho_classe com provider_data sem descRota
    trecho_classe.provider_data = json.dumps({})
    assert vexado_api.rota_id_from_trecho_classe(trecho_classe) is None
    # grupo sem linha e trecho_classe com provider_data com descRota
    trecho_classe.provider_data = json.dumps({"descRota": "Abc a Def"})
    assert vexado_api.rota_id_from_trecho_classe(trecho_classe) == "Abc a Def"
    assert trecho_classe.grupo.linha == "Abc a Def"
    # grupo com linha
    trecho_classe.grupo.linha = "Abc a Def"
    assert vexado_api.rota_id_from_trecho_classe(trecho_classe) == "Abc a Def"


def test_buscar_corridas_sem_corrida(vexado_api, mock_login, mock_nenhum_servico):
    request_params = {"origem": 1, "destino": 2932, "data": "2021-11-11"}
    corridas = vexado_api.buscar_corridas(request_params).servicos
    assert corridas == []


def test_add_multiple_pax_na_lista_passageiros_viagem(
    vexado_api, mock_login, mock_add_multiple_pax, mock_comprar_multiple
):
    response = vexado_api.add_multiple_pax_na_lista_passageiros_viagem(
        CheckPaxMultipleForm.parse_obj(mock_add_multiple_pax)
    )
    travels_ids = [
        mock_add_multiple_pax["travels"][0]["travel_id"],
        mock_add_multiple_pax["travels"][0]["travel_id"],
        mock_add_multiple_pax["travels"][1]["travel_id"],
    ]
    buseiros_ids = [
        mock_add_multiple_pax["travels"][0]["buseiros"][0]["id"],
        mock_add_multiple_pax["travels"][0]["buseiros"][1]["id"],
        mock_add_multiple_pax["travels"][1]["buseiros"][0]["id"],
    ]
    passagens_ids = []
    for index, passagem in enumerate(response["passagens"]):
        assert passagem["buseiro_id"] == buseiros_ids[index]
        assert passagem["travel_id"] == travels_ids[index]
        passagens_ids.append(passagem["id"])
    passagens_models = Passagem.objects.filter(id__in=passagens_ids)
    for p in passagens_models:
        assert p.company_integracao_id == vexado_api.company.id


def test_add_multiple_pax_na_lista_passageiros_viagem_antes_de_tres_horas(
    vexado_api, mock_login, mock_add_multiple_pax
):
    tc = TrechoClasse.objects.get(trechoclasse_internal_id=mock_add_multiple_pax["trechoclasse_id"])
    tc.datetime_ida = tz.now() + timedelta(hours=4)
    tc.save()
    with pytest.raises(
        HibridoEmissaoForaDaData,
        match="Emissão do modelo Hibrido só pode ser feita faltando menos de 3h para o embarque",
    ):
        vexado_api.add_multiple_pax_na_lista_passageiros_viagem(CheckPaxMultipleForm.parse_obj(mock_add_multiple_pax))


def test_add_multiple_pax_na_lista_passageiros_viagem_poltronas_insuficientes(
    vexado_api, mock_login, mock_add_multiple_pax, mock_poltronas_insuficientes
):
    vexado_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_api.company.save()
    params = CheckPaxMultipleForm.parse_obj(mock_add_multiple_pax)
    with pytest.raises(
        RodoviariaOverbookingException,
        match="Apenas 2 poltronas disponíveis para esta viagem",
    ):
        vexado_api.add_multiple_pax_na_lista_passageiros_viagem(params)
    assert TrechoClasse.objects.get(trechoclasse_internal_id=params.trechoclasse_id, tags__name=Tags.OVERBOOKING)


def test_add_multiple_pax_na_lista_passageiros_viagem_num_queries(
    vexado_api,
    mock_get_poltronas_livres,
    mock_add_multiple_pax,
    django_assert_num_queries,
):
    with django_assert_num_queries(3, connection=connections["rodoviaria"]), patch.object(
        VexadoAPI, "_efetuar_compra"
    ) as mock_efetuar_compra:
        vexado_api.add_multiple_pax_na_lista_passageiros_viagem(CheckPaxMultipleForm.parse_obj(mock_add_multiple_pax))
    assert mock_efetuar_compra.call_count == 2


def test_cpf_comprador(vexado_api):
    assert vexado_api._cpf_comprador([]) == "94167878097"
    assert vexado_api._cpf_comprador([SimpleNamespace(cpf="123")]) == "123"
    assert vexado_api._cpf_comprador([SimpleNamespace(cpf=None), SimpleNamespace(cpf="456")]) == "456"


def test_not_empty_cpf():
    pax_dict = {
        "poltronaId": 793227,
        "numeroPoltrona": 12,
        "nome": "Teste",
        "documentoComFoto": "22222",
        "dtNascimento": "17/10/1995",
        "cpfPassageiro": "",
        "valor": "125.25",
        "tipoEmissao": "NORMAL",
        "tipoDocumento": "RG",
        "telefone": "(12) 99160-7865",
    }
    pax_form = PassageiroForm.parse_obj(pax_dict)
    assert pax_form.cpf is None


@pytest.fixture
def motorista():
    return MotoristaForm.parse_obj(
        {
            "user_id": 1234,
            "nome": "Fulano de Tal",
            "email": "<EMAIL>",
            "telefone": "(11) 91234-5678",
            "cpf": "111.111.111-11",
            "cnh": {
                "numero": "12345678901",
                "validade": "2030-10-20",
                "categoria": "C",
                "orgao_emissor": "SSP",
                "uf": "SP",
            },
            "registro_antt": {"numero": "12345678901234", "validade": "2040-12-22"},
        }
    )


@pytest.fixture
def motorista_sem_docs():
    return MotoristaForm.parse_obj(
        {
            "user_id": 1234,
            "nome": "Fulano de Tal",
            "email": "<EMAIL>",
            "telefone": "(11) 91234-5678",
            "cpf": "111.111.111-11",
        }
    )


@pytest.fixture
def mock_cadastrar_usuario(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/usuarios/empresa/{company_external_id}/cadastrar"
    response = {"success": True, "message": "Usuário cadastrado com sucusso !!"}

    requests_mock.add("POST", url, json=response)
    yield requests_mock
    requests_mock.remove("POST", url)


@pytest.fixture
def mock_buscar_usuario(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/usuarios/empresa/{company_external_id}"
    params = {"emailUsuario": "<EMAIL>"}
    response = {
        "usuarios": [
            {
                "id": 111,
                "nome": "Fulano de Tal",
                "pessoa": None,
                "email": "<EMAIL>",
                "cpfCnpj": "111.111.111-11",
                "telefone": "(11) 91234-5678",
                "idEmpresaCadastradora": 5,
                "plataforma": None,
                "nomeComNumero": "111 - Fulano de Tal",
                "rolesName": [
                    {
                        "descricao": "Motorista empresa",
                        "valor": "ROLE_MOTORISTA_EMPRESA",
                        "identificacao": "5",
                    }
                ],
                "comissao": None,
                "impostoRetido": "16.25",
                "nomeAgencia": None,
                "codigoAgencia": None,
                "descTipoDescontoMeiaPassagem": None,
                "valorTipoDescontoMeiaPassagem": None,
                "agencias": None,
                "usuarioEmpresaId": 222,
                "nomeEmpresa": "BUSER - TCB WJ BRASIL TRANSPORTES",
                "bloqueado": None,
                "venderValorANTT": False,
            }
        ],
        "total": 1,
    }

    requests_mock.add("GET", url, match=[query_param_matcher(params)], json=response)
    yield requests_mock
    requests_mock.remove("GET", url)


@pytest.fixture
def mock_cadastrar_motorista(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/motorista/empresa/{company_external_id}/cadastrar"

    requests_mock.add("POST", url)
    yield requests_mock
    requests_mock.remove("POST", url)


@pytest.fixture
def mock_buscar_motorista(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    id_empresa = vexado_api.company.company_external_id

    url = f"{base_url}/motorista/empresa/{id_empresa}/usuario-empresa/222"
    response = {
        "id": 333,
        "numeroRegistroCNH": 12345678901,
        "validadeCNH": "20/10/2030",
        "categoriaCNH": "C",
        "uf": "SP",
        "docIdentidadeEmissor": "SSP",
        "numeroRegistroANTT": 12345678901234,
        "validadeRegistroANTT": "22/12/204",
        "codigoUsuarioEmpresa": 222,
    }

    requests_mock.add("GET", url, json=response)
    yield requests_mock
    requests_mock.remove("GET", url)


def test_cria_motorista(
    vexado_api,
    motorista,
    mock_login,
    mock_cadastrar_usuario,
    mock_buscar_usuario,
    mock_cadastrar_motorista,
    mock_buscar_motorista,
):
    result = vexado_api.cria_motorista(motorista)  # assert de requests no mock
    assert result == {"id_usuario": 111, "id_usuario_empresa": 222, "id_motorista": 333}


def test_cria_motorista_sem_docs(
    vexado_api,
    motorista_sem_docs,
    mock_login,
    mock_cadastrar_usuario,
    mock_buscar_usuario,
):
    result = vexado_api.cria_motorista(motorista_sem_docs)  # assert de requests no mock
    assert result == {
        "id_usuario": 111,
        "id_usuario_empresa": 222,
        "id_motorista": None,
    }


@pytest.fixture
def mock_editar_usuario(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/usuarios/empresa/{company_external_id}/alterar"

    requests_mock.add("POST", url)
    yield requests_mock
    requests_mock.remove("POST", url)


def test_edita_dados_motorista(vexado_api, motorista, mock_login, mock_editar_usuario):
    vexado_api.edita_dados_motorista(111, motorista)  # assert de requests no mock


@pytest.fixture
def mock_editar_motorista(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/motorista/empresa/{company_external_id}/alterar"

    requests_mock.add("POST", url)
    yield requests_mock
    requests_mock.remove("POST", url)


def test_edita_documentos_motorista(vexado_api, motorista, mock_login, mock_editar_motorista):
    vexado_api.edita_documentos_motorista(333, 222, motorista)  # assert de requests no mock


@pytest.fixture
def mock_escalar_motorista(requests_mock, vexado_api):
    base_url = vexado_api.base_url
    company_external_id = vexado_api.company.company_external_id

    url = f"{base_url}/itinerario/999/empresa/{company_external_id}/incluir-motoristas?idsUsuarioEmpresaMotoristas=222"

    requests_mock.add("POST", url)
    yield requests_mock
    requests_mock.remove("POST", url)


def test_escala_motorista(vexado_api, motorista, mock_login, mock_escalar_motorista):
    vexado_api.escala_motorista(222, 999)  # assert de requests no mock


def test_verifica_classe(vexado_api):
    servico = {"tipoVeiculo": "LEITO_ESPECIAL", "descricaoTipoVeiculo": "Leito Cama"}
    buser_class = "leito cama"
    assert vexado_api.verifica_classe(servico, buser_class)
    buser_class = "leito individual"
    assert vexado_api.verifica_classe(servico, buser_class)
    buser_class = "executivo"
    assert not vexado_api.verifica_classe(servico, buser_class)


def test_cadastrar_grupo_dois_andares(vexado_api, vexado_login, mock_cadastrar_grupo_dois_andares):
    response = vexado_api.cadastrar_grupo(
        CadastrarGrupoTaskParams(
            data_partida="2022-03-22",
            hora_saida="08:00",
            id_rota=321,
            id_veiculo=555,
            classe_primeiro_andar="EXECUTIVO",
            classe_segundo_andar="SEMI_LEITO",
        )
    )
    assert response == {"executivo": 43809, "semi leito": 43810}


def test_cadastrar_grupo_um_andar(vexado_api, vexado_login, mock_cadastrar_grupo_um_andar):
    response = vexado_api.cadastrar_grupo(
        CadastrarGrupoTaskParams(
            data_partida="2022-03-22",
            hora_saida="08:00",
            id_rota=321,
            id_veiculo=555,
            classe_primeiro_andar="LEITO_CAMA_INDIVIDUAL",
            classe_segundo_andar=None,
        )
    )
    assert response == {"leito cama individual": 43809}


def test_cadastrar_trechos_novo(vexado_api, vexado_login, mock_cadastrar_preco):
    assert vexado_api.cadastrar_trechos(
        [
            SimpleNamespace(
                cidade_origem_id=1,
                cidade_destino_id=2,
                classe="executivo",
                max_split_value=Decimal(100),
            )
        ]
    ) == [None]


def test_cadastrar_trechos_atualiza(vexado_api, mock_cadastrar_preco_ja_cadastrado):
    assert vexado_api.cadastrar_trechos(
        [
            SimpleNamespace(
                cidade_origem_id=1,
                cidade_destino_id=2,
                classe="executivo",
                max_split_value=Decimal(100),
            )
        ]
    ) == [None]


def test_cadastrar_rota(vexado_api, vexado_login, mock_cadastrar_rota):
    assert vexado_api.cadastrar_rota(origem_id=1, destino_id=2, prefixo="Teste 1", rota_internal_id=312) is None


def test_editar_rota(vexado_api, vexado_login, mock_cadastrar_rota):
    assert (
        vexado_api.editar_rota(
            rota_external_id=12,
            delimitacao=42,
            origem_id=1,
            destino_id=2,
            prefixo="Teste 1",
        )
        is None
    )


def test_inativar_rota(vexado_api, vexado_login, mock_cadastrar_rota):
    assert vexado_api.inativar_rota(
        rota_external_id=12,
        origem_id=1,
        destino_id=2,
        prefixo="Teste 1",
    ) == {"Sucesso": True}


def test_atualizar_checkpoint(vexado_api, vexado_login, mock_cadastrar_checkpoint):
    payload = {
        "cidadeDestino": {"id": 1},
        "duracao": "04:30",
        "tempo_embarque": "00:10",
        "tempo_total": "04:40",
        "idEmpresa": 1,
        "id": 1,
        "ordem": 1,
        "pontoEmbarque": "Gruta",
        "quilometragem": 123,
        "rotaDto": {"id": 1},
    }
    assert vexado_api.atualizar_checkpoint(payload, "Grutx", "01:30", 130) is None


def test_get_mapas_veiculos_api(vexado_api, mock_listar_mapas_veiculos):
    mapas_veiculos = vexado_api.get_mapas_veiculos_api()
    mapas_veiculos_mocker = mocker_vexado.MockMapasVeiculos.response()["mapasVeiculos"]
    assert mapas_veiculos == [
        vexado_models.MapaVeiculoForm(
            id_external=9,
            has_dois_andares=False,
            quantidade_poltronas_primeiro_andar=2,
            quantidade_poltronas_segundo_andar=0,
            provider_data=mapas_veiculos_mocker[0],
        ),
        vexado_models.MapaVeiculoForm(
            id_external=103,
            has_dois_andares=True,
            quantidade_poltronas_primeiro_andar=2,
            quantidade_poltronas_segundo_andar=1,
            provider_data=mapas_veiculos_mocker[1],
        ),
    ]


def test_get_mapas_veiculos_api_nao_encontra(vexado_api, mock_login, requests_mock):
    url_mapas_veiculos = f"{vexado_api.base_url}/{endpoints.MapasVeiculos.path}?size=300"
    requests_mock.add("GET", url_mapas_veiculos, json={})
    with pytest.raises(RodoviariaException, match="Algo deu errado na busca pelos mapas de veiculos"):
        vexado_api.get_mapas_veiculos_api()


def test_get_lista_veiculos_api(vexado_api, mock_listar_veiculos):
    lista_onibus = vexado_api.get_lista_veiculos_api()
    assert lista_onibus == [
        vexado_models.VeiculoForm(descricao="APJ8F14", external_mapa_veiculo_id=111, id_external=775),
        vexado_models.VeiculoForm(descricao="JVT3603", external_mapa_veiculo_id=104, id_external=721),
    ]


def test_get_lista_veiculos_api_sem_veiculos(vexado_api, mock_listar_veiculos_sem_veiculos):
    lista_onibus = vexado_api.get_lista_veiculos_api()
    assert lista_onibus == []


def test_create_veiculos_api(vexado_api, mock_create_veiculos_api):
    veiculos = [
        baker.make(
            "rodoviaria.Veiculo",
            descricao="APJ8F14",  # valor retirado do mock
            mapa_veiculo=baker.make("rodoviaria.MapaVeiculo", id_external=5323),
        ),
        baker.make(
            "rodoviaria.Veiculo",
            descricao="JVT3603",  # valor retirado do mock
            mapa_veiculo=baker.make("rodoviaria.MapaVeiculo", id_external=5323),
        ),
    ]
    response = vexado_api.create_veiculos_api(veiculos)
    assert response[0].id_external == 775  # valor retirado do mock
    assert response[1].id_external == 721  # valor retirado do mock


def test_escala_veiculo(vexado_api, mock_alterar_veiculo):
    params = {"id": 38735, "idVeiculo": 816, "idEmpresa": 321, "andar": 1}
    response = vexado_api.escala_veiculo(params)
    assert response is None


def test_buscar_rotas(vexado_api, mock_buscar_rotas):
    rota_api = mocker_vexado.MockBuscarRotas.response()["rotas"][0]
    response = vexado_api.buscar_rotas()
    assert response == [
        {
            "id": rota_api["id"],
            "descricao": rota_api["descricao"],
            "cidade_origem": rota_api["cidadeOrigem"]["nome"],
            "cidade_origem_id": rota_api["cidadeOrigem"]["id"],
            "cidade_destino": rota_api["cidadeDestino"]["nome"],
            "cidade_destino_id": rota_api["cidadeDestino"]["id"],
            "prefixo": rota_api["prefixo"],
            "delimitacao": rota_api["delimitacao"],
            "itinerario": rota_api["trechosDto"],
        }
    ]


def test_buscar_rotas_vazio(vexado_api, mock_buscar_rotas_vazio):
    with pytest.raises(RodoviariaException, match="Algo deu errado na busca pelas rotas"):
        vexado_api.buscar_rotas()


def test_altera_veiculo_viagem_error(vexado_api, mock_escalar_veiculo_error, requests_mock):
    itinerario_id = 43213
    veiculo_external_id = 9482
    andar = 1
    with pytest.raises(VexadoAPIError, match="Vexado erro: Ocorreu um erro interno"):
        vexado_api.altera_veiculo_viagem(veiculo_external_id, andar, itinerario_id)


def test_altera_veiculo_viagem(vexado_api, mock_escalar_veiculo, requests_mock):
    itinerario_id = 43213
    veiculo_external_id = 9482
    andar = 1
    vexado_api.altera_veiculo_viagem(veiculo_external_id, andar, itinerario_id)


def test_viagens_por_rota(vexado_api, mock_listar_viagens_rota):
    rota_external_id = 7799  # valor do mock
    response = vexado_api.viagens_por_rota(rota_external_id)
    assert response[0].id_external == 289141
    assert response[0].datetime_ida == to_default_tz(datetime(2024, 8, 12, 19, 0))
    assert response[0].rota_external_id == 4592
    assert response[0].tipo_assento == "leito"
    assert response[0].veiculo_id == 883
    assert response[0].veiculo_andar == 1


def test_cadastrar_checkpoint(vexado_api, mock_cadastrar_checkpoint):
    params = SimpleNamespace(
        cidade_destino_id=5428,
        duracao="01:30",
        tempo_embarque="00:20",
        tempo_total="01:50",
        ponto_embarque="posto da gruta",
        km=10,
        id_rota_external=1,
    )
    vexado_api.cadastrar_checkpoint(params)


def test_listar_trechos(vexado_api, mock_listar_trechos):
    response = vexado_api.listar_trechos(7799)
    assert len(response) == 5


def test_match_datetime_ida_servico(vexado_api):
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 18, 40))
    datetime_ida_servico = datetime(2022, 2, 2, 18, 30)
    timezone = "America/Sao_Paulo"
    # hibrido sem match
    assert not vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
    # hibrido com match
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 18, 30))
    assert vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
    # marketplace sem match
    vexado_api.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 17, 30))
    assert not vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
    # marketplace com match na tolerancia
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 18, 10))
    assert vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 18, 30))
    # marketplace com match exato
    assert vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)
    # marketplace com match -1
    datetime_ida = to_default_tz(datetime(2022, 2, 2, 18, 31))
    assert vexado_api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico)


def test_cpf_passageiro(vexado_api):
    assert vexado_api._cpf_passageiro(SimpleNamespace(cpf="10129238347")) == "10129238347"
    assert vexado_api._cpf_passageiro(SimpleNamespace(cpf=None)) == vexado_api.CPF_GENERICO
    assert vexado_api._cpf_passageiro(SimpleNamespace(cpf="None")) == vexado_api.CPF_GENERICO


def test_lista_reservas_viagem(vexado_api, mock_lista_reservas_viagem):
    reservas = vexado_api.lista_reservas_viagem(21136)
    assert reservas == [638842, 639607]


def test_lista_reservas_viagem_sem_reservas(vexado_api, mock_lista_reservas_viagem_nenhuma_reserva):
    reservas = vexado_api.lista_reservas_viagem(21136)
    assert reservas == []


def test_lista_reservas_viagem_itinerario_vazio(vexado_api, mock_lista_reservas_viagem_itinerario_vazio):
    reservas = vexado_api.lista_reservas_viagem(21136)
    assert reservas == []


def test_lista_reservas_viagem_itinerario_erro(vexado_api, mock_lista_reservas_viagem_erro):
    with pytest.raises(VexadoAPIError):
        vexado_api.lista_reservas_viagem(21136)


def test_inativa_grupo_classe(vexado_api, mock_inativar_itinerario):
    response = vexado_api.inativar_grupo_classe(mock_inativar_itinerario.itinerario_id)
    assert response is None


def test_cancelar_reserva_por_localizador_sucesso(vexado_api_marketplace, mock_cancela_venda):
    vexado_api_marketplace.cancelar_reserva_por_localizador(3233)
    assert json.loads(mock_cancela_venda.calls[1].request.body) == {
        "empresaId": str(vexado_api_marketplace.company.company_external_id),
        "motivo": "Viagem Alterada",
        "reservas": [3233],
    }


def test_cancelar_venda(mock_cancela_venda, vexado_company, vexado_api_marketplace):
    travel_id, trechoclasse_id = 22, 41
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company)
    trechoclasse = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trechoclasse_id, grupo=grupo)
    passagens = [
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
            localizador=localizador,
        )
        for localizador in [500, 501]
    ]
    response = vexado_api_marketplace.cancela_venda(
        CancelaVendaForm(travel_id=travel_id, trechoclasse_id=trechoclasse_id)
    )
    assert response == {"Sucesso": True}
    mock_cancela_venda.assert_call_count(f"{vexado_api_marketplace.base_url}/{endpoints.CancelarReservas.path}", 2)
    for i in range(2):
        passagens[i].refresh_from_db()
        assert passagens[i].status == Passagem.Status.CANCELADA


@pytest.mark.parametrize("mock_recuperar_pedidos_cancelados", [[(12345, 5), (54321, 5)]], indirect=True)
def test_cancelar_venda_passagens_ja_cancelada(
    mock_cancela_venda_nao_pode_cancelar,
    mock_recuperar_pedidos_cancelados,
    vexado_company,
    vexado_api_marketplace,
):
    travel_id, trechoclasse_id = 22, 41
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company)
    trechoclasse = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trechoclasse_id, grupo=grupo)
    localizadores = [r["id"] for r in mocker_vexado.MockRecuperarPedido.response()["reservas"]]
    passagens = [
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
            localizador=localizador,
            pedido_external_id=pedido_id,
        )
        for localizador, pedido_id in [
            (localizadores[0], 12345),
            (localizadores[1], 54321),
        ]
    ]
    response = vexado_api_marketplace.cancela_venda(
        CancelaVendaForm(travel_id=travel_id, trechoclasse_id=trechoclasse_id)
    )
    assert response == {"Sucesso": True}
    mock_cancela_venda_nao_pode_cancelar.assert_call_count(
        f"{vexado_api_marketplace.base_url}/{endpoints.CancelarReservas.path}", 2
    )
    for i in range(2):
        passagens[i].refresh_from_db()
        assert passagens[i].status == Passagem.Status.CANCELADA


def test_cancelar_venda_pedido_nao_encontrado(
    mock_cancela_venda_pedido_nao_encontrado,
    vexado_company,
    vexado_api_marketplace,
):
    vexado_company.company_external_id = 534
    travel_id, trechoclasse_id = 22, 41
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company)
    trechoclasse = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trechoclasse_id, grupo=grupo)
    passagem = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=travel_id,
        trechoclasse_integracao=trechoclasse,
        localizador=500,
    )
    response = vexado_api_marketplace.cancela_venda(
        CancelaVendaForm(travel_id=travel_id, trechoclasse_id=trechoclasse_id)
    )
    assert response == {"Sucesso": True}
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CANCELADA
    assert "passagem_nao_encontra_na_api" in passagem.tags_set()


def test_cancelar_venda_hibrido(vexado_company, vexado_api):
    vexado_api.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_api.company.save()
    travel_id, trechoclasse_id = 22, 41
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company)
    trechoclasse = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trechoclasse_id, grupo=grupo)
    passagens = [
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
            localizador=localizador,
        )
        for localizador in [500, 501]
    ]
    response = vexado_api.cancela_venda(CancelaVendaForm(travel_id=travel_id, trechoclasse_id=trechoclasse_id))
    assert response == {"Sucesso": True}
    for i in range(2):
        passagens[i].refresh_from_db()
        assert passagens[i].status == Passagem.Status.CANCELADA


def test_cancelar_venda_error(mock_cancela_venda_error, vexado_api):
    vexado_api.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_api.company.save()
    travel_id, trechoclasse_id = 22, 41
    trechoclasse = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=trechoclasse_id,
        grupo__company_integracao=vexado_api.company,
    )
    passagens = [
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
            localizador=localizador,
        )
        for localizador in [500, 501]
    ]
    with pytest.raises(RodoviariaException) as exc:
        vexado_api.cancela_venda(CancelaVendaForm(travel_id=travel_id, trechoclasse_id=trechoclasse_id))
    expected_error_msg = (
        "A reserva só pode ser cancelada até 3 horas antes do embarque.Verifique se há alguma reserva que não atende a"
        " este requisito."
    )
    assert error_str(exc.value) == expected_error_msg
    for i in range(2):
        passagens[i].refresh_from_db()
        assert passagens[i].status == Passagem.Status.CONFIRMADA
        assert passagens[i].datetime_cancelamento is not None
        assert passagens[i].erro_cancelamento == expected_error_msg


@pytest.mark.parametrize("mock_recuperar_pedido", [(12345, 5)], indirect=True)
def test_update_bpe_passagem(vexado_api, mock_recuperar_pedido):
    pedido_id = 12345
    passagem = baker.make(
        Passagem,
        pedido_external_id=pedido_id,
        bpe_em_contingencia=True,
        localizador="170886",
    )
    vexado_api.update_bpe_passagem(passagem)
    passagem.refresh_from_db()
    assert passagem.bpe_em_contingencia is False


@pytest.mark.parametrize("mock_recuperar_pedido", [(54321, 5)], indirect=True)
def test_cancelar_reservas_por_pedido_id(vexado_api_marketplace, mock_recuperar_pedido, mock_cancela_venda):
    pedido_id = 54321
    response = vexado_api_marketplace.cancelar_reservas_por_pedido_id(pedido_id)
    assert response == [
        {"poltrona": 55, "localizador": 170887},
        {"poltrona": 52, "localizador": 170886},
    ]


@pytest.mark.parametrize("mock_recuperar_pedidos_cancelados", [[(54321, 5)]], indirect=True)
def test_cancelar_reservas_por_pedido_id_reservas_ja_canceladas(
    vexado_api_marketplace, mock_recuperar_pedidos_cancelados
):
    pedido_id = 54321
    response = vexado_api_marketplace.cancelar_reservas_por_pedido_id(pedido_id)
    assert response == [
        {"poltrona": 55, "localizador": 170887},
        {"poltrona": 52, "localizador": 170886},
    ]


@pytest.mark.parametrize("mock_recuperar_pedido", [(54321, 5)], indirect=True)
def test_cancelar_reservas_por_pedido_id_cancelamento_nao_pode_cancelar(
    vexado_api_marketplace, mock_recuperar_pedido, mock_cancela_venda_error
):
    pedido_id = 54321
    response = vexado_api_marketplace.cancelar_reservas_por_pedido_id(pedido_id)
    assert response == [
        {
            "poltrona": 55,
            "localizador": 170887,
            "error_type": "nao_autorizado",
            "error": (
                "A reserva só pode ser cancelada até 3 horas antes do embarque.Verifique se há alguma reserva que não"
                " atende a este requisito."
            ),
        },
        {
            "poltrona": 52,
            "localizador": 170886,
            "error_type": "nao_autorizado",
            "error": (
                "A reserva só pode ser cancelada até 3 horas antes do embarque.Verifique se há alguma reserva que não"
                " atende a este requisito."
            ),
        },
    ]


@pytest.mark.parametrize("mock_recuperar_pedido", [(54321, 5)], indirect=True)
def test_cancelar_reservas_por_pedido_id_cancelamento_nao_autorizado(
    vexado_api_marketplace, mock_recuperar_pedido, mock_cancela_venda_nao_pode_cancelar
):
    pedido_id = 54321
    response = vexado_api_marketplace.cancelar_reservas_por_pedido_id(pedido_id)
    assert response == [
        {
            "poltrona": 55,
            "localizador": 170887,
            "error_type": "nao_autorizado",
            "error": ("As seguintes reservas não atendem aos requisitos para cancelamento: Kakaroto"),
        },
        {
            "poltrona": 52,
            "localizador": 170886,
            "error_type": "nao_autorizado",
            "error": ("As seguintes reservas não atendem aos requisitos para cancelamento: Kakaroto"),
        },
    ]


@pytest.mark.parametrize("mock_recuperar_pedido", [(54321, 5)], indirect=True)
def test_cancelar_reservas_por_pedido_id_connection_error(vexado_api_marketplace, mock_recuperar_pedido):
    pedido_id = 54321
    response = vexado_api_marketplace.cancelar_reservas_por_pedido_id(pedido_id)
    assert response == [
        {
            "poltrona": 55,
            "localizador": 170887,
            "error_type": "erro_conexao",
            "error": (
                "vexado 'POST http://vexado.com.br/reserva/cancelar-reservas' json={'empresaId': '5', 'motivo': "
                "'Passagem com erro', 'reservas': [170887]} params=None connection error"
            ),
        },
        {
            "poltrona": 52,
            "localizador": 170886,
            "error_type": "erro_conexao",
            "error": (
                "vexado 'POST http://vexado.com.br/reserva/cancelar-reservas' json={'empresaId': '5', 'motivo': "
                "'Passagem com erro', 'reservas': [170886]} params=None connection error"
            ),
        },
    ]


def test_salva_provider_da_compra(
    mock_comprar,
    vexado_api_marketplace,
    mock_bloquear_poltrona,
    mock_get_poltronas_livres,
):
    trechoclasse_id = 41
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=2932)
    baker.make(
        "rodoviaria.TrechoClasse",
        external_id=21136,
        trechoclasse_internal_id=trechoclasse_id,
        origem=origem,
        destino=destino,
        preco_rodoviaria=125.25,
        datetime_ida=tz.now() + timedelta(hours=2),
        provider_data=json.dumps({"trechoOrigemId": 9123, "trechoDestinoId": 1236}),
    )
    params_comprar = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    vexado_api_marketplace.comprar(params_comprar)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    mock_response_conftest = mocker_vexado.MockRecuperarPedido.response()
    dict_info_passagem = {}

    # Verifica se comprou duas passagens
    assert len(passagens) == 2
    passagem_um = Passagem.objects.get(localizador=passagens[0].localizador)
    passagem_dois = Passagem.objects.get(localizador=passagens[1].localizador)

    # Verifica se os dados específicos da passagens constam apenas na passagem do pax
    assert passagem_um.provider_data != passagem_dois.provider_data
    dict_info_passagem[mock_response_conftest["reservas"][0]["id"]] = mock_response_conftest["reservas"][0]
    dict_info_passagem[mock_response_conftest["reservas"][1]["id"]] = mock_response_conftest["reservas"][1]
    assert passagem_um.provider_data["reservas"] == dict_info_passagem[passagem_um.provider_data["reservas"]["id"]]
    assert passagem_dois.provider_data["reservas"] == dict_info_passagem[passagem_dois.provider_data["reservas"]["id"]]

    # Verifica se os dados gerais do provider_data são comuns a ambas as passagens
    passagem_um.provider_data.pop("reservas")
    passagem_dois.provider_data.pop("reservas")
    mock_response_conftest.pop("reservas")
    assert passagem_um.provider_data == mock_response_conftest
    assert passagem_dois.provider_data == mock_response_conftest
    assert passagem_um.provider_data == passagem_dois.provider_data


@pytest.mark.parametrize("mock_recuperar_pedido", [(6666, 5)], indirect=True)
def test_get_atualizacao_passagem_api_parceiro_vexado(vexado_api, mock_recuperar_pedido):
    recuperar_pedido_response = mocker_vexado.MockRecuperarPedido.response()["reservas"][1]
    mock_pedido_external_id = str(mocker_vexado.MockRecuperarPedido.response()["id"])
    passagemOriginalParaConsultaVexado = baker.make("rodoviaria.Passagem", pedido_external_id=6666, localizador=170886)
    resp = vexado_api.get_atualizacao_passagem_api_parceiro(passagemOriginalParaConsultaVexado)
    assert resp.integracao == "Vexado"
    assert resp.localizador == str(passagemOriginalParaConsultaVexado.localizador)
    assert resp.status == "confirmada"
    assert resp.pedido_external_id == mock_pedido_external_id
    assert resp.numero_assento == recuperar_pedido_response["numeroPoltrona"]
    assert resp.primeiro_nome_pax == recuperar_pedido_response["passageiroDto"]["nome"]
    assert resp.tipo_documento == "CPF"
    assert resp.numero_documento == recuperar_pedido_response["passageiroDto"]["cpfPassageiro"]
    assert resp.bpe_public_url == recuperar_pedido_response["transmissaoBpe"]["qrCod"]
    assert resp.data_partida == recuperar_pedido_response["dataHoraPartida"]
    assert resp.origem == recuperar_pedido_response["descCidadeOrigem"]
    assert resp.destino == recuperar_pedido_response["descCidadeDestino"]
    assert resp.data_chegada == recuperar_pedido_response["dataHoraDesembarque"]
    assert resp.empresa_name == recuperar_pedido_response["nomeEmpresa"]
    assert resp.valor_passagem == recuperar_pedido_response["valor"]
    assert resp.taxa_embarque == recuperar_pedido_response["taxaServico"]


@pytest.mark.parametrize("mock_recuperar_pedido", [(6666, 5)], indirect=True)
def test_get_atualizacao_passagem_error_passagemNaoEncontrada(vexado_api, mock_recuperar_pedido):
    passagemOriginalParaConsultaVexado = baker.make("rodoviaria.Passagem", pedido_external_id=6666)
    with pytest.raises(
        PassengerNotRegistered, match="Vexado - Não foi possível encontrar a passagem com id_pedido=6666"
    ):
        vexado_api.get_atualizacao_passagem_api_parceiro(passagemOriginalParaConsultaVexado)


def test_atualiza_origens(mock_buscar_cidades_por_empresa, vexado_api):
    resp = vexado_api.atualiza_origens()
    assert isinstance(resp, list)
    assert all(isinstance(o, vexado_models.Localidade) for o in resp)
    assert resp[0].descricao == "Brasília - DF"


def test_get_desenho_mapa_poltronas_leito(vexado_api, vexado_trechoclasses, mock_get_poltronas):
    baker.make(
        TipoAssento,
        company=vexado_api.company,
        tipo_assento_parceiro="LEITO_ESPECIAL_1",
        tipo_assento_buser_preferencial="leito",
    )
    trecho_classe_id = vexado_trechoclasses.ida.trechoclasse_internal_id
    vexado_trechoclasses.ida.provider_data = json.dumps({"tipoVeiculo": "LEITO_ESPECIAL_1"})
    vexado_trechoclasses.ida.save()
    mapa_poltronas = vexado_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito"
    normal = [Passagem.CategoriaEspecial.NORMAL]
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": True,
                        "x": 5,
                        "y": 17,
                        "numero": 51,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 18,
                        "numero": 55,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 19,
                        "numero": 59,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 20,
                        "numero": 63,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 17,
                        "numero": 50,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 18,
                        "numero": 54,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 19,
                        "numero": 58,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 20,
                        "numero": 62,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 17,
                        "numero": 49,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 18,
                        "numero": 53,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 19,
                        "numero": 57,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 20,
                        "numero": 61,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_get_desenho_mapa_poltronas(vexado_api, vexado_trechoclasses, mock_get_poltronas):
    trecho_classe_id = vexado_trechoclasses.ida.trechoclasse_internal_id
    vexado_trechoclasses.ida.provider_data = json.dumps({"tipoVeiculo": "SEMI_LEITO"})
    vexado_trechoclasses.ida.save()
    mapa_poltronas = vexado_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "semi leito"
    normal = [Passagem.CategoriaEspecial.NORMAL]
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": True,
                        "x": 5,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 2,
                        "numero": 7,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 5,
                        "numero": 15,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 6,
                        "numero": 19,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 7,
                        "numero": 23,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 8,
                        "numero": 27,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 9,
                        "numero": 31,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 10,
                        "numero": 35,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 11,
                        "numero": 39,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 12,
                        "numero": 43,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 13,
                        "numero": 47,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 1,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 2,
                        "numero": 8,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 5,
                        "numero": 16,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 6,
                        "numero": 20,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 7,
                        "numero": 24,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 8,
                        "numero": 28,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 9,
                        "numero": 32,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 10,
                        "numero": 36,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 11,
                        "numero": 40,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 12,
                        "numero": 44,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 13,
                        "numero": 48,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 1,
                        "numero": 2,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 2,
                        "numero": 6,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 3,
                        "numero": 10,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 4,
                        "numero": 12,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 5,
                        "numero": 14,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 6,
                        "numero": 18,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 7,
                        "numero": 22,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 8,
                        "numero": 26,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 9,
                        "numero": 30,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 10,
                        "numero": 34,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 11,
                        "numero": 38,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 12,
                        "numero": 42,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 13,
                        "numero": 46,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 1,
                        "numero": 1,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 2,
                        "numero": 5,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 3,
                        "numero": 9,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 4,
                        "numero": 11,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 5,
                        "numero": 13,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 6,
                        "numero": 17,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 7,
                        "numero": 21,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 8,
                        "numero": 25,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 9,
                        "numero": 29,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 10,
                        "numero": 33,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 11,
                        "numero": 37,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 12,
                        "numero": 41,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 13,
                        "numero": 45,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_desbloquear_poltronas(vexado_api, vexado_trechoclasses, mock_desbloquear_poltrona):
    tcid = vexado_trechoclasses.ida.trechoclasse_internal_id
    vexado_api.cache.set_poltrona_key_cache(tcid, 2, {"id": 100, "numero_poltrona": 2})
    vexado_api.desbloquear_poltronas(tcid, [2])


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_bloquear_poltronas_v2(mock_bloquear_poltrona, mock_get_poltronas, vexado_api, vexado_trechoclasses):
    trecho_classe_id = vexado_trechoclasses.ida.trechoclasse_internal_id
    expected_best_before = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")) + timedelta(
        minutes=10
    )
    poltrona = 18
    result = vexado_api.bloquear_poltronas_v2(trecho_classe_id, poltrona, Passagem.CategoriaEspecial.NORMAL)
    expected_cache = parse_obj_as(
        dict,
        {
            "id": 570555,
            "poltronaId": 7373852,
            "uuid": "6c5729b1-db8a-4a46-a397-2e942d1a8ecf",
            "preco": "620.89",
            "precoANTT": "1241.78",
            "precoMeia": "310.44",
            "precoANTTMeia": "620.89",
            "taxaEmbarque": "0",
            "pedagio": "0",
            "tarifaSeguro": "0",
            "garantiaPrecoId": 597934,
        },
    )
    assert vexado_api.cache.get_poltrona_key_cache(trecho_classe_id, poltrona) == expected_cache
    assert result == BloquearPoltronasResponse(
        seat=poltrona,
        best_before=expected_best_before,
        external_payload=expected_cache,
    )


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_bloquear_poltronas_v2_ja_bloqueada(
    mock_bloquear_poltrona_ja_bloqueada, mock_get_poltronas, vexado_api, vexado_trechoclasses
):
    trecho_classe_id = vexado_trechoclasses.ida.trechoclasse_internal_id
    poltrona = 18
    with pytest.raises(PoltronaJaSelecionadaException):
        vexado_api.bloquear_poltronas_v2(trecho_classe_id, poltrona, Passagem.CategoriaEspecial.NORMAL)
