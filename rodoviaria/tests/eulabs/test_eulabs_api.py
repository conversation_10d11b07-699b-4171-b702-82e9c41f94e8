import copy
import json
from datetime import date, datetime, timedelta
from decimal import Decimal
from types import SimpleNamespace
from unittest import mock

import pytest
import responses
import time_machine
from django.utils import timezone
from model_bakery import baker
from simple_token_bucket import SimpleTokenBucket
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from commons.memcache import getmc_pymemcache
from rodoviaria.api.eulabs import endpoints as eulabs_endpoints
from rodoviaria.api.eulabs.api import EulabsAPI, async_desbloquear_poltronas_by_travel_key
from rodoviaria.api.eulabs.auth import EulabsAuth
from rodoviaria.api.eulabs.exceptions import (
    EulabsAPIError,
    EulabsNaoPodeCancelar,
    EulabsPoltronaIndisponivel,
)
from rodoviaria.api.eulabs.memcache import EulabsMC
from rodoviaria.api.eulabs.models import CorridasForm
from rodoviaria.api.forms import Localidade, ServicoForm
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasResponse,
    DadosBeneficioForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.models.core import Cidade, Company, LocalEmbarque, Passagem, TrechoClasse
from rodoviaria.service import reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    PassengerNotRegistered,
    PassengerTicketAlreadyPrintedException,
    PassengerTicketAlreadyReturnedException,
    PoltronaTrocadaException,
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaLoginNotFoundException,
    RodoviariaOverbookingException,
    RodoviariaTooManyRequestsError,
)
from rodoviaria.service.novos_modelos_svc import VIACAO_ADAMANTINA_INTERNAL_ID
from rodoviaria.tests.eulabs.mock_data_response import (
    mock_cancel_conditions,
    mock_cancel_sale,
    mock_chooseseat,
    mock_findsale,
    mock_sales,
    mock_summary,
    mock_summary_list,
    mock_travels,
)
from rodoviaria.tests.middleware import request_with_middleware
from rodoviaria.tests.utils_testes import _comprar_params


@pytest.fixture(autouse=True)
def infinite_token_bucket():
    # TODO: tech-debt testes deveriam usar o NullTokenBucket
    with mock.patch.object(SimpleTokenBucket, "try_get_token", return_value=True):
        yield


def test_atualiza_origens(eulabs_api, mock_buscar_origens):
    resp = eulabs_api.atualiza_origens()
    assert isinstance(resp, list)
    assert isinstance(resp[0], Localidade)
    assert resp[0] == Localidade(
        nome_cidade="ALTA FLORESTA",
        external_cidade_id=428,
        uf="MT",
        complemento="RODOVIARIA",
        external_local_id=428,
    )


def test_connection_error(eulabs_api, requests_mock):
    with pytest.raises(RodoviariaConnectionError):
        eulabs_api.atualiza_origens()


def test_too_many_requests(eulabs_api, mock_atualiza_origens_too_many_requests):
    with pytest.raises(RodoviariaTooManyRequestsError, match="429 too many requests"):
        eulabs_api.atualiza_origens()


def test_bad_request(eulabs_api, mock_atualiza_origens_bad_request):
    with pytest.raises(RodoviariaException):
        eulabs_api.atualiza_origens()


def test_get_client_and_auth(eulabs_api):
    request = mock.MagicMock()
    request.headers = {}
    login = eulabs_api.login
    auth = EulabsAuth.from_client(login)
    client_auth = auth(request)
    assert client_auth.headers["X-Eucatur-Api-Id"] == eulabs_api.login.api_id
    assert client_auth.headers["X-Eucatur-Api-Key"] == eulabs_api.login.api_key


def test_non_existent_login():
    integracao = baker.make("rodoviaria.Integracao", name="eucatur")
    company = baker.make("rodoviaria.Company", integracao=integracao, modelo_venda="Marketplace")
    with pytest.raises(RodoviariaLoginNotFoundException) as exc:
        EulabsAuth.from_company(company)

    assert (
        str(exc.value)
        == f"Login {integracao} não encontrado para (company_pk={company.pk}, modelo_venda={company.modelo_venda})"
    )


@pytest.mark.parametrize(
    "mock_cancelar_venda",
    [{"cancel_type": "Cancel", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
@pytest.mark.parametrize("mock_condicoes_cancelamento_cancelar", [{"item_keys": ["item_key"]}], indirect=True)
def test_cancela_venda_cancelar(eulabs_api, mock_condicoes_cancelamento_cancelar, mock_cancelar_venda):
    travel_id = 4558441
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem="item_key",
        status=Passagem.Status.CONFIRMADA,
    )
    response = eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    assert response == [mock_cancel_sale.sucesso]
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CANCELADA


@pytest.mark.parametrize(
    "mock_cancelar_venda",
    [{"cancel_type": "Devolution", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
@pytest.mark.parametrize("mock_condicoes_cancelamento_devolver", [{"item_keys": ["item_key"]}], indirect=True)
def test_cancela_venda_devolver(eulabs_api, mock_condicoes_cancelamento_devolver, mock_cancelar_venda):
    travel_id = 542353
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem="item_key",
        status=Passagem.Status.CONFIRMADA,
    )
    response = eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    assert response == [mock_cancel_sale.sucesso]
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CANCELADA


@pytest.mark.parametrize("mock_condicoes_cancelamento_nao_autorizado", [{"item_keys": ["item_key"]}], indirect=True)
def test_cancela_venda_nao_autorizado(eulabs_api, mock_condicoes_cancelamento_nao_autorizado):
    travel_id = 542353
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem="item_key",
        status=Passagem.Status.CONFIRMADA,
    )
    with pytest.raises(RodoviariaException, match="Cancelamento não autorizado"):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA


@pytest.mark.parametrize("mock_consultar_reserva_cancelada", [{"sale_ids": [392412]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": [mock_findsale.sale["items"][0]["key"], mock_findsale.sale["items"][1]["key"]]}],
    indirect=True,
)
def test_cancela_venda_passagens_ja_canceladas(
    eulabs_api,
    mock_condicoes_cancelamento_erro_nao_permitido,
    mock_consultar_reserva_cancelada,
):
    travel_id = 542353
    pedido_id = 392412
    passagem_1 = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )
    passagem_2 = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][1]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )
    eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem_1.refresh_from_db()
    passagem_2.refresh_from_db()
    assert passagem_1.status == Passagem.Status.CANCELADA
    assert passagem_2.status == Passagem.Status.CANCELADA
    mock_consultar_reserva_cancelada.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, pedido_id).url,
        1,
    )


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22, 23]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": [mock_findsale.sale["items"][0]["key"]]}],
    indirect=True,
)
def test_cancela_venda_passagens_ticket_already_printed(
    eulabs_api,
    mock_condicoes_cancelamento_erro_nao_permitido,
    mock_consultar_reserva,
):
    travel_id = 542353
    pedido_id = 392412
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )
    with pytest.raises(PassengerTicketAlreadyPrintedException):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA
    mock_consultar_reserva.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, pedido_id).url,
        1,
    )


@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": [mock_findsale.sale["items"][0]["key"]]}],
    indirect=True,
)
def test_cancela_venda_passagens_erro_ao_consultar_reservas(eulabs_api, mock_condicoes_cancelamento_erro_nao_permitido):
    travel_id = 542353
    pedido_id = 392412
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )
    with pytest.raises(EulabsNaoPodeCancelar):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert (
        passagem.erro_cancelamento
        == "status_code=422 response_json=Não é possível cancelar ou devolver uma passagem que esteja com"
        " situação diferente de Normal (N) ou BPE"
    )


@pytest.mark.parametrize("mock_condicoes_cancelamento_cancelar", [{"item_keys": ["item_key"]}], indirect=True)
@pytest.mark.parametrize(
    "mock_cancelar_venda_ja_cancelada",
    [{"cancel_type": "Cancel", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
def test_cancela_venda_erro_passagem_ja_cancelada(
    eulabs_api, mock_condicoes_cancelamento_cancelar, mock_cancelar_venda_ja_cancelada
):
    travel_id = 542353
    pedido_id = 392412
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem="item_key",
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )

    with pytest.raises(PassengerTicketAlreadyReturnedException):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))

    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA


@pytest.mark.parametrize("mock_consultar_reserva_cancelada", [{"sale_ids": [392412, 594231]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": [mock_findsale.sale["items"][0]["key"], mock_findsale.sale["items"][1]["key"]]}],
    indirect=True,
)
def test_cancela_venda_passagens_ja_canceladas_com_pedidos_diferentes(
    eulabs_api,
    mock_condicoes_cancelamento_erro_nao_permitido,
    mock_consultar_reserva_cancelada,
):
    travel_id = 542353
    pedido_id_1 = 392412
    pedido_id_2 = 594231
    passagem_1 = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id_1,
    )
    passagem_2 = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][1]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id_2,
    )
    eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem_1.refresh_from_db()
    passagem_2.refresh_from_db()
    assert passagem_1.status == Passagem.Status.CANCELADA
    assert passagem_2.status == Passagem.Status.CANCELADA
    mock_consultar_reserva_cancelada.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, pedido_id_1).url,
        1,
    )
    mock_consultar_reserva_cancelada.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, pedido_id_2).url,
        1,
    )


@pytest.mark.parametrize("mock_consultar_reserva_devolvida", [{"sale_ids": [392412]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": [mock_findsale.sale["items"][0]["key"]]}],
    indirect=True,
)
def test_cancela_passagem_devolvida(
    eulabs_api,
    mock_condicoes_cancelamento_erro_nao_permitido,
    mock_consultar_reserva_devolvida,
):
    travel_id = 542353
    pedido_id_1 = 392412
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id_1,
    )

    with pytest.raises(PoltronaTrocadaException):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))

    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.DEVOLVIDA
    mock_consultar_reserva_devolvida.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, pedido_id_1).url,
        1,
    )


@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_502", [{"item_keys": [mock_findsale.sale["items"][0]["key"]]}], indirect=True
)
def test_cancela_venda_passagens_com_erro_502(eulabs_api, mock_condicoes_cancelamento_502):
    travel_id = 542353
    pedido_id = 392412
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id=pedido_id,
    )
    with pytest.raises(EulabsNaoPodeCancelar):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA
    err_msg_p1 = (
        "status_code=502 response_json=eulabs "
        "'GET https://prod-url-eucatur.com.br/sales/cancel_conditions/VIB-9890206' "
        "json=None params=None status_code=502"
    )
    assert passagem.erro_cancelamento == err_msg_p1


@pytest.mark.parametrize("mock_condicoes_cancelamento_sem_opcoes", [{"item_keys": ["item_key"]}], indirect=True)
def test_cancela_venda_sem_opcoes(eulabs_api, mock_condicoes_cancelamento_sem_opcoes):
    travel_id = 542353
    passagem = baker.make(
        Passagem,
        travel_internal_id=travel_id,
        numero_passagem="item_key",
        status=Passagem.Status.CONFIRMADA,
    )
    with pytest.raises(RodoviariaException, match="Cancelamento não autorizado"):
        eulabs_api.cancela_venda(CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": 43223}))
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA


def test_buscar_corridas(eulabs_api, mock_buscar_corridas):
    eulabs_api.company.company_external_id = mock_travels.corridas[0]["items"][0]["company"]["id"]
    request_params = {"origem": 5, "destino": 10, "data": "2022-04-20"}
    corridas = eulabs_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 2
    servico = mock_travels.corridas[0]["items"][0]
    classe = servico["tariffs"][0]
    provider_data = servico.copy()
    provider_data["tariffs"] = [classe]
    provider_data["key"] = mock_travels.corridas[0]["key"]
    provider_data["schedule_id"] = mock_travels.corridas[0]["id"]
    assert corridas[0] == ServicoForm.parse_obj(
        {
            "linha": None,
            "external_datetime_ida": servico["datetime_departure"].replace(" ", "T"),
            "external_id": f'{servico["id"]}/{classe["category"]["vehicle_type_id"]}',
            "preco": Decimal(str(classe["amount"])),
            "provider_data": provider_data,
            "external_datetime_chegada": servico["datetime_arrival"].replace(" ", "T"),
            "classe": classe["category"]["description"],
            "classe_reduzida": classe["category"]["short_description"],
            "capacidade_classe": classe["category"]["final_seat"] - classe["category"]["initial_seat"] + 1,
            "distancia": str(classe["total_km"]),
            "rota_external_id": mock_travels.corridas[0]["id"],
            "external_company_id": mock_travels.corridas[0]["items"][0]["company"]["id"],
            "key": mock_travels.corridas[0]["key"],
            "vagas": classe["category"]["free_seats"],
        }
    )


def test_buscar_corridas_beneficios_none(eulabs_api, mock_buscar_corridas_com_beneficios_none):
    eulabs_api.company.company_external_id = mock_travels.corridas[0]["items"][0]["company"]["id"]
    request_params = {"origem": 5, "destino": 10, "data": "2022-04-20"}
    corridas = eulabs_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 2
    servico = mock_travels.corridas[0]["items"][0]
    classe = servico["tariffs"][0]
    provider_data = servico.copy()
    provider_data["tariffs"] = [classe]
    provider_data["key"] = mock_travels.corridas[0]["key"]
    provider_data["schedule_id"] = mock_travels.corridas[0]["id"]
    assert corridas[0] == ServicoForm.parse_obj(
        {
            "linha": None,
            "external_datetime_ida": servico["datetime_departure"].replace(" ", "T"),
            "external_id": f'{servico["id"]}/{classe["category"]["vehicle_type_id"]}',
            "preco": Decimal(str(classe["amount"])),
            "provider_data": provider_data,
            "external_datetime_chegada": servico["datetime_arrival"].replace(" ", "T"),
            "classe": classe["category"]["description"],
            "classe_reduzida": classe["category"]["short_description"],
            "capacidade_classe": classe["category"]["final_seat"] - classe["category"]["initial_seat"] + 1,
            "distancia": str(classe["total_km"]),
            "rota_external_id": mock_travels.corridas[0]["id"],
            "external_company_id": mock_travels.corridas[0]["items"][0]["company"]["id"],
            "key": mock_travels.corridas[0]["key"],
            "vagas": classe["category"]["free_seats"],
        }
    )


def test_buscar_servico(eulabs_api, mock_buscar_corridas):
    expected_servico = mock_travels.corridas[0]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    resp = eulabs_api.buscar_corridas(request_params, match_params)

    assert resp.found
    serv = resp.servicos[0]
    assert serv.preco == Decimal("62.41")
    assert serv.vagas == 47
    assert serv.provider_data["company_external_id"] == expected_servico["items"][0]["company"]["id"]
    assert len(resp.servicos) == 1  # se deu match, essa lista tem que ter apenas 1


def test_buscar_servico_poltronas_marcacao_impossivel(
    eulabs_api, mock_retorna_poltronas_erro_400_marcacao_impossivel, mocker
):
    trecho_classe_id = 102030
    travel_key, classe_api = (123, 456)
    baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trecho_classe_id, active=True)
    mock_get_viagem = mocker.patch.object(EulabsAPI, "_get_viagem_key_and_class")

    mock_get_viagem.return_value = (travel_key, classe_api, classe_api)
    # Dado um mock com uma response_status==400 com response_json específico para o endpoint
    # e um segundo mock com response_status==200
    # Quanto realizar a chamada pro endpoint
    poltronas, _, _ = eulabs_api._get_poltronas_classe_and_trechoclasse(trecho_classe_id)
    # Espero que a política de retry seja aplicada para o endpoint, realizando as duas chamadas e retornando
    # a resposta saudável
    sorted_poltronas = sorted([(p.numero, p.ocupada) for p in poltronas])
    expected = [(1, False), (48, True), (49, True), (50, True)]
    assert sorted_poltronas == expected


def test_buscar_servico_timeout_522(eulabs_api, requests_mock):
    endpoint = eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login).url
    requests_mock.add(responses.GET, endpoint, status=522)
    timezone = "America/Sao_Paulo"
    datetime_ida = datetime(2022, 1, 13, 14, 45)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    with pytest.raises(RodoviariaConnectionError):
        eulabs_api.buscar_corridas(request_params, match_params)


def test_buscar_servico_readtimeout(eulabs_api, requests_mock):
    endpoint = eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login).url
    requests_mock.get(endpoint, status=504)
    timezone = "America/Sao_Paulo"
    datetime_ida = datetime(2022, 1, 13, 14, 45)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    with pytest.raises(RodoviariaConnectionError):
        eulabs_api.buscar_corridas(request_params, match_params)


def test_buscar_servico_match_por_empresa(eulabs_api, mock_buscar_corridas_duas_corridas_empresas_diferentes):
    expected_servico = mock_travels.duas_corridas_empresas_diferentes[1]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    servicos = eulabs_api.buscar_corridas(request_params, match_params)

    assert servicos.found
    serv = servicos.servicos[0]
    assert serv.provider_data["company_external_id"] == expected_servico["items"][0]["company"]["id"]
    assert serv.preco == Decimal("62.41")
    assert serv.vagas == 47
    assert serv.classe == "SEMI LEITO COM AR"
    assert len(servicos.servicos) == 1  # se deu match, essa lista tem que ter apenas 1


def test_buscar_servico_match_por_empresa_preco_por_poltrona_zerado(
    eulabs_api,
    mock_buscar_corridas_duas_corridas_empresas_diferentes,
):
    expected_servico = mock_travels.duas_corridas_empresas_diferentes[1]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    servicos = eulabs_api.buscar_corridas(request_params, match_params)

    assert servicos.found
    serv = servicos.servicos[0]
    assert serv.provider_data["company_external_id"] == expected_servico["items"][0]["company"]["id"]
    assert serv.preco == Decimal("62.41")
    assert serv.vagas == 47
    assert len(servicos.servicos) == 1  # se deu match, essa lista tem que ter apenas 1


def test_buscar_servico_sem_match(eulabs_api, mock_buscar_corridas):
    eulabs_api.company.company_external_id = mock_travels.corridas[0]["items"][0]["company"]["id"]
    eulabs_api.company.save()
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2022, 1, 10, 10, 40), timezone)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    servicos = eulabs_api.buscar_corridas(request_params, match_params)
    expected_servico_proximo = mock_travels.corridas[0]["items"][0]
    expected_provider_data = mock_travels.corridas[0]["items"][0].copy()
    expected_provider_data["tariffs"] = [expected_provider_data["tariffs"][0]]
    expected_provider_data["key"] = mock_travels.corridas[0]["key"]
    expected_provider_data["schedule_id"] = mock_travels.corridas[0]["id"]
    assert not servicos.found
    assert len(servicos.servicos) == 2
    assert servicos.servicos[0] == ServicoForm.parse_obj(
        {
            "external_id": (
                f"{expected_servico_proximo['id']}/{expected_servico_proximo['tariffs'][0]['category']['vehicle_type_id']}"
            ),
            "preco": Decimal(str(expected_servico_proximo["price"])),
            "external_datetime_ida": to_tz(
                datetime.strptime(expected_servico_proximo["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
                timezone,
            ),
            "classe": expected_servico_proximo["class"]["long_name"],
            "external_company_id": str(eulabs_api.company.company_external_id),
            "vagas": expected_servico_proximo["tariffs"][0]["category"]["free_seats"],
            "provider_data": expected_provider_data,
        }
    )


def test_buscar_servico_dois_matchs(eulabs_api, mock_buscar_corridas_duas_corridas):
    expected_servico = mock_travels.duas_corridas_mesma_empresa[1]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    ) - timedelta(minutes=10)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    resp = eulabs_api.buscar_corridas(request_params, match_params)

    servico = resp.servicos[0]
    assert resp.found
    assert servico.provider_data["company_external_id"] == expected_servico["items"][0]["company"]["id"]
    assert servico.preco == Decimal("62.41")
    assert servico.vagas == 47


def test_buscar_corridas_com_items_none(eulabs_api, mock_buscar_corridas_com_items_none):
    expected_servico = mock_travels.corridas[0]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    resp = eulabs_api.buscar_corridas(request_params, match_params)

    assert resp.found is False
    assert resp.servicos == []


def test_buscar_corridas_find_match_corridas_prioriza_com_mais_vagas(eulabs_api):
    expected_servico = mock_travels.duas_corridas_vagas_diferentes[1]
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"), timezone)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    corridas = CorridasForm.parse_obj(mock_travels.duas_corridas_vagas_diferentes)

    match = eulabs_api._find_match_corridas(corridas, request_params, **match_params)

    assert match["servico_encontrado"].vagas == 47


def test_buscar_corridas_find_match_corridas_viacao_adamantina(eulabs_api):
    expected_servico = mock_travels.duas_corridas_vagas_diferentes[1]
    eulabs_api.company.company_internal_id = VIACAO_ADAMANTINA_INTERNAL_ID
    eulabs_api.company.company_external_id = expected_servico["items"][0]["company"]["id"]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"), timezone)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    corridas = CorridasForm.parse_obj(mock_travels.duas_corridas_vagas_diferentes)

    match = eulabs_api._find_match_corridas(corridas, request_params, **match_params)

    assert match["servico_encontrado"].vagas == 47
    assert match["vagas"] == 94


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_verifica_poltrona(eulabs_api, mock_buscar_corridas, mock_retorna_poltronas, mock_bloquear_poltrona):
    trecho_classe_id = 173942
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=mock_travels.corridas[0]["items"][0]["price"],
    )
    assert eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1)) == [10]
    assert eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)) == [
        24,
        25,
    ]
    assert eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=3)) == [
        6,
        7,
        8,
    ]
    assert eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=4)) == [
        27,
        28,
        15,
        18,
    ]
    with pytest.raises(RodoviariaOverbookingException, match="Apenas 14 poltronas disponíveis"):
        eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=15))


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_verifica_poltrona_categoria_especial(
    eulabs_api, mock_buscar_corridas, mock_retorna_poltronas, mock_bloquear_poltrona
):
    trecho_classe_id = 173942
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=mock_travels.corridas[0]["items"][0]["price"],
    )
    assert eulabs_api.verifica_poltrona(
        VerificarPoltronaForm(
            trechoclasse_id=trecho_classe_id, passageiros=2, categoria_especial=Passagem.CategoriaEspecial.IDOSO_50
        )
    ) == [15, 18]
    with pytest.raises(RodoviariaOverbookingException, match="Apenas 2 poltronas disponíveis"):
        eulabs_api.verifica_poltrona(
            VerificarPoltronaForm(
                trechoclasse_id=trecho_classe_id, passageiros=3, categoria_especial=Passagem.CategoriaEspecial.JOVEM_50
            )
        )


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_get_map_poltronas(eulabs_api, mock_buscar_corridas, mock_retorna_poltronas):
    trecho_classe_id = 173942
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    tc = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=mock_travels.corridas[0]["items"][0]["price"],
    )
    baker.make(
        Passagem,
        poltrona_external_id=24,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=tc,
    )
    baker.make(
        Passagem,
        poltrona_external_id=25,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=tc,
    )
    map_poltronas = eulabs_api.get_map_poltronas(trecho_classe_id)
    assert len(map_poltronas) == 46
    assert map_poltronas["24"] == "ocupada"
    assert map_poltronas["25"] == "ocupada"
    assert map_poltronas["18"] == "livre"
    assert map_poltronas["27"] == "livre"
    assert map_poltronas["26"] == "ocupada"
    assert map_poltronas["15"] == "livre"
    assert map_poltronas["06"] == "livre"


def test_get_viagem_key_and_class(eulabs_api, mock_buscar_corridas_duas_corridas):
    cidade = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem, destino = baker.make(LocalEmbarque, id_external=3242, cidade=cidade, _quantity=2)
    trecho_classe = baker.make(
        TrechoClasse,
        external_id="1979102/4",
        origem=origem,
        destino=destino,
        datetime_ida=to_default_tz(datetime(2022, 1, 10, 13, 30)),
    )
    travel_key, classe_reduzida_api, classe_api = eulabs_api._get_viagem_key_and_class(trecho_classe)
    assert travel_key == "travel_key"
    assert classe_reduzida_api == "SEMI"
    assert classe_api == "SEMI LEITO COM AR"


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_verifica_poltrona_cache(eulabs_api, mock_buscar_corridas, mock_retorna_poltronas, mock_bloquear_poltrona):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 849239
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
    )
    expected_poltrona = 18
    assert eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1)) == [
        expected_poltrona
    ]
    cache = getmc_pymemcache()
    assert EulabsMC(company_id).get_travel_key_cache(trecho_classe_id) == cache.get(
        f"EULABS_COMPANY_{company_id}_TRECHO_{trecho_classe_id}"
    )
    assert EulabsMC(company_id).get_travel_key_cache(trecho_classe_id) == mock_travels.corridas[0]["key"]
    assert EulabsMC(company_id).get_poltrona_key_cache(trecho_classe_id, expected_poltrona) == cache.get(
        f"EULABS_COMPANY_{company_id}_TRECHO_{trecho_classe_id}_POLTRONA_{expected_poltrona}"
    )
    assert (
        EulabsMC(company_id).get_poltrona_key_cache(trecho_classe_id, expected_poltrona)
        == mock_chooseseat.sucesso["selected_seat_Key"]
    )


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize(
    "mock_bloquear_poltrona_tres_vezes", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True
)
def test_verifica_poltrona_reseleciona_poltronas(
    eulabs_api,
    mock_buscar_corridas,
    mock_retorna_poltronas,
    mock_bloquear_poltrona_tres_vezes,
):
    trecho_classe_id = 173942
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=mock_travels.corridas[0]["items"][0]["price"],
    )
    with mock.patch.object(EulabsAPI, "desbloquear_poltronas") as mock_desbloquear_poltronas:
        poltronas = eulabs_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2))
    assert poltronas == [27, 28]
    mock_desbloquear_poltronas.assert_called_once_with(trecho_classe_id, [24])


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona",
    [{"travel_key": "travel_key", "selected_seat_keys": ["keyseat1", "keyseat2"]}],
    indirect=True,
)
def test_desbloquear_poltronas(eulabs_api, mock_desbloquear_poltrona):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    EulabsMC(company_id).set_travel_key_cache(trecho_classe_id, "travel_key")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 31, "keyseat1")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 32, "keyseat2")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 33, "keyseat3")
    eulabs_api.desbloquear_poltronas(trecho_classe_id, [31, 32])
    assert EulabsMC(company_id).get_poltrona_key_cache(trecho_classe_id, 31) is None
    assert EulabsMC(company_id).get_poltrona_key_cache(trecho_classe_id, 32) is None
    assert EulabsMC(company_id).get_poltrona_key_cache(trecho_classe_id, 33) == "keyseat3"


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [22]}], indirect=True)
def test_comprar_extra_poltronas(eulabs_api, mocker, mock_efetuar_reserva, mock_consultar_reserva, mock_consultar_bpe):
    mock_get_cache = mocker.patch.object(EulabsMC, "get_poltrona_key_cache")
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    eulabs_cache = EulabsMC(company_id)
    eulabs_cache.set_travel_key_cache(trecho_classe_id, "travel_key")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=1)
    params.poltronas = [22]
    params.extra_poltronas = "extra_poltronas"
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id).order_by(
        "poltrona_external_id"
    )
    assert len(response["passagens"]) == passagens.count() == 1
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    assert passagens[0].id in passagens_emitidas_ids
    assert passagens[0].preco_rodoviaria == Decimal("56.00")
    assert passagens[0].preco_poltrona == Decimal(str(mock_sales.pedido["items"][0]["amount"]))
    assert passagens[0].poltrona_external_id == 22
    assert (
        passagens[0].bpe_qrcode
        == "https://dfe-portal.svrs.rs.gov.br/BPE/qrcode?chBPe=29250710771628000144630020000006151000026682&tpAmb=1"
    )
    assert passagens[0].tags_set() == set()
    mock_get_cache.assert_not_called()


@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "travel_key"}], indirect=True)
@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [22, 23]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22, 23]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [22]}], indirect=True)
def test_comprar_bloqueia_poltronas(
    eulabs_api, mock_bloquear_poltrona, mock_efetuar_reserva, mock_consultar_reserva, mock_consultar_bpe
):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    eulabs_cache = EulabsMC(company_id)
    eulabs_cache.set_travel_key_cache(trecho_classe_id, "travel_key")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = [22, 23]
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id).order_by(
        "poltrona_external_id"
    )
    assert len(response["passagens"]) == passagens.count() == 2
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    assert passagens[0].id in passagens_emitidas_ids
    assert passagens[0].preco_rodoviaria == Decimal("56.00")
    assert passagens[0].preco_poltrona == Decimal(str(mock_sales.pedido["items"][0]["amount"]))
    assert passagens[0].poltrona_external_id == 22
    assert passagens[0].tags_set() == set()
    assert passagens[1].poltrona_external_id == 23
    assert passagens[1].id in passagens_emitidas_ids
    assert passagens[1].tags_set() == set()
    assert eulabs_cache.delete_poltrona_key_cache(trecho_classe_id, 22)
    assert eulabs_cache.delete_poltrona_key_cache(trecho_classe_id, 23)


@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "travel_key"}], indirect=True)
@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [22, 23]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22, 23]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [22]}], indirect=True)
def test_comprar_buscar_dados_bpe(
    eulabs_api, mock_bloquear_poltrona, mock_efetuar_reserva, mock_consultar_reserva, mock_consultar_bpe
):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    eulabs_cache = EulabsMC(company_id)
    eulabs_cache.set_travel_key_cache(trecho_classe_id, "travel_key")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = [22, 23]
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id).order_by(
        "poltrona_external_id"
    )
    assert len(response["passagens"]) == passagens.count() == 2
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    for ticket in passagens:
        assert ticket.id in passagens_emitidas_ids
        assert ticket.linha == "PORTO VELHO x VILHENA"
        assert ticket.prefixo == ""
        assert ticket.preco_base == Decimal("60")
        assert ticket.taxa_embarque == Decimal("1.91")
        assert ticket.seguro == Decimal("0.5")
        assert ticket.pedagio == Decimal("0")
        assert ticket.outros_tributos == "ICMS 4.2 (17.5%) OUTROS TRIB: 15.27 (25.45%)"
        assert ticket.embarque_eletronico == (
            "eucaturmobile/boarding?type=manual"
            "&identification=89125963"
            "&code=0234"
            "&line=00001"
            "&departure_travel=11/06/2022 02:30:00"
            "&departure=11/06/2022 02:30:00"
            "&direction=Volta"
            "&msg=GERAR BPE ATE O EMBARQUE, ANTES DO INICIO DA PRESTAÇAO DO SERVIÇO "
            "(SEÇAO V, ANEXO XIII, ART 23 $1º, RICMS/RO-DECR.22721/18). APÓS EMBARQUE VER BPE NO APP C/MOTORISTA"
        )
        assert ticket.preco_rodoviaria == Decimal("56.00")
        assert ticket.preco_poltrona == Decimal(str(mock_sales.pedido["items"][0]["amount"]))
        assert ticket.tags_set() == set()

    assert passagens[0].poltrona_external_id == 22
    assert passagens[1].poltrona_external_id == 23
    assert eulabs_cache.delete_poltrona_key_cache(trecho_classe_id, 22)
    assert eulabs_cache.delete_poltrona_key_cache(trecho_classe_id, 23)


@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "travel_key"}], indirect=True)
@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [22, 23]}], indirect=True)
def test_comprar_bloqueia_poltronas_erro_de_conexao_ao_buscar_dados_bpe(
    eulabs_api, mock_bloquear_poltrona, mock_efetuar_reserva
):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    eulabs_cache = EulabsMC(company_id)
    eulabs_cache.set_travel_key_cache(trecho_classe_id, "travel_key")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = [22, 23]
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id).order_by(
        "poltrona_external_id"
    )
    assert len(response["passagens"]) == passagens.count() == 2
    # assert passagens ficaram com a tag de dados pendentes
    assert passagens[0].tags_set() == {"dados_bpe_pendente"}
    assert passagens[1].tags_set() == {"dados_bpe_pendente"}


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [29, 18]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [29, 18]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [29, 18]}], indirect=True)
@pytest.mark.parametrize(
    "mock_bloquear_poltrona_tres_vezes", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True
)
def test_comprar_bloqueia_poltronas_ja_bloqueada(
    eulabs_api,
    mock_buscar_corridas,
    mock_bloquear_poltrona_tres_vezes,
    mock_retorna_poltronas,
    mock_efetuar_reserva,
    mock_consultar_reserva,
    mock_consultar_bpe,
):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    api_item = mock_travels.corridas[0]["items"][0]
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        preco_rodoviaria=Decimal("65.00"),
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
    )
    eulabs_cache = EulabsMC(company_id)
    eulabs_cache.set_travel_key_cache(trecho_classe_id, "travel_key")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = [29, 30]
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert len(response["passagens"]) == passagens.count() == 2
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    assert passagens[0].id in passagens_emitidas_ids
    assert passagens[1].id in passagens_emitidas_ids
    assert eulabs_cache.delete_poltrona_key_cache(trecho_classe_id, 29)
    assert eulabs_cache.delete_poltrona_key_cache(
        trecho_classe_id, 18
    )  # selecionou outra poltrona, pois a 30 estava bloqueada


@pytest.fixture
def cached_values(eulabs_company):
    company_id = eulabs_company.company_internal_id
    trecho_classe_id = 172391
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    EulabsMC(company_id).set_travel_key_cache(trecho_classe_id, "travel_key")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 24, "keyseat1")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 25, "keyseat2")
    return SimpleNamespace(poltronas=[24, 25], trecho_classe_id=trecho_classe_id)


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [24, 25]}], indirect=True)
def test_comprar_cached_poltronas(
    eulabs_api, mock_efetuar_reserva, mock_consultar_reserva, mock_consultar_bpe, cached_values
):
    params = _comprar_params(cached_values.trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = cached_values.poltronas
    response = eulabs_api.comprar(params)
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=cached_values.trecho_classe_id
    )
    assert len(response["passagens"]) == passagens.count() == 2
    assert passagens[0].id in passagens_emitidas_ids
    assert passagens[1].id in passagens_emitidas_ids


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [24, 25]}], indirect=True)
def test_comprar_busca_travel_key(
    eulabs_api, mock_efetuar_reserva, mock_buscar_corridas, mock_consultar_reserva, mock_consultar_bpe
):
    company_id = eulabs_api.company.company_internal_id
    trecho_classe_id = 172391
    api_item = mock_travels.corridas[0]["items"][0]
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        preco_rodoviaria=Decimal("56.00"),
        origem=baker.make(LocalEmbarque, id_external=123, cidade__timezone="America/Sao_Paulo"),
        destino=baker.make(LocalEmbarque, id_external=321),
        datetime_ida=to_default_tz(datetime(2023, 1, 13, 15, 43)),
    )
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 24, "keyseat1")
    EulabsMC(company_id).set_poltrona_key_cache(trecho_classe_id, 25, "keyseat2")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = [24, 25]
    response = eulabs_api.comprar(params)
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert len(response["passagens"]) == passagens.count() == 2
    assert passagens[0].id in passagens_emitidas_ids
    assert passagens[1].id in passagens_emitidas_ids


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [24]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [24]}], indirect=True)
def test_comprar_busca_travel_key_extra_poltronas(
    eulabs_api, mocker, mock_efetuar_reserva, mock_buscar_corridas, mock_consultar_reserva, mock_consultar_bpe
):
    trecho_classe_id = 172391
    api_item = mock_travels.corridas[0]["items"][0]
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id=f"{api_item['id']}/{api_item['tariffs'][0]['category']['vehicle_type_id']}",
        preco_rodoviaria=Decimal("56.00"),
        origem=baker.make(LocalEmbarque, id_external=123, cidade__timezone="America/Sao_Paulo"),
        destino=baker.make(LocalEmbarque, id_external=321),
        datetime_ida=to_default_tz(datetime(2023, 1, 13, 15, 43)),
    )
    mock_get_cache = mocker.patch.object(EulabsMC, "get_poltrona_key_cache")
    params = _comprar_params(trecho_classe_id, quantidade_passageiros=1)
    params.poltronas = [24]
    params.extra_poltronas = "asdfasd"
    response = eulabs_api.comprar(params)
    passagens_emitidas_ids = [p["id"] for p in response["passagens"]]
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert len(response["passagens"]) == passagens.count() == 1
    assert passagens[0].id in passagens_emitidas_ids
    mock_get_cache.assert_not_called()


def test_comprar_connection_error(requests_mock, eulabs_api, cached_values):
    params = _comprar_params(cached_values.trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = cached_values.poltronas
    with pytest.raises(RodoviariaConnectionError):
        eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=cached_values.trecho_classe_id
    )
    for passagem in passagens:
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro == f"eulabs {eulabs_endpoints.EfetuarReservaConfig(eulabs_api.login).url} connection error"
        assert passagem.pedido_external_id is None


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona",
    [{"travel_key": "travel_key", "selected_seat_keys": ["keyseat1", "keyseat2"]}],
    indirect=True,
)
def test_comprar_rodoviaria_exception(
    requests_mock, eulabs_api, mock_desbloquear_poltrona, mock_venda_falhou_400, cached_values
):
    params = _comprar_params(cached_values.trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = cached_values.poltronas
    with pytest.raises(RodoviariaBaseException):
        eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=cached_values.trecho_classe_id
    )
    for passagem in passagens:
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro
        assert passagem.pedido_external_id is None


def test_comprar_rodoviaria_exception_extra_poltronas(requests_mock, eulabs_api, mock_venda_falhou_400, cached_values):
    params = _comprar_params(cached_values.trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = cached_values.poltronas
    params.extra_poltronas = "sdf"
    with pytest.raises(RodoviariaBaseException):
        eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=cached_values.trecho_classe_id
    )
    for passagem in passagens:
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro
        assert passagem.pedido_external_id is None


@pytest.fixture
def mock_fluxo_compra(eulabs_api, eulabs_grupos_mockado):
    grupo_buser_django = eulabs_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = eulabs_api.company.company_internal_id
    trecho_classe_buser_django_infos = eulabs_grupos_mockado.ida.trecho_classe_infos
    trecho_classe_id = 482739
    expected_servico = mock_travels.corridas[0]
    eulabs_api.company.company_external_id = mock_travels.corridas[0]["items"][0]["company"]["id"]
    eulabs_api.company.save()
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    trecho_classe_buser_django_infos.trechoclasse_id = trecho_classe_id
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_tz(
        datetime.strptime(expected_servico["datetime_departure"], "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    trecho_classe_buser_django_infos.tipo_assento = "semi leito"

    cidade_origem, cidade_destino = baker.make(Cidade, company=eulabs_api.company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )

    yield grupo_buser_django, trecho_classe_buser_django_infos, trecho_classe_id


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "travel_key"}], indirect=True)
@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_devolver",
    [
        {
            "item_keys": ["VIB-17416588", "VIB-17416589"],
        }
    ],
    indirect=True,
)
@pytest.mark.parametrize(
    "mock_cancelar_venda",
    [{"cancel_type": "Devolution", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
def test_compra_fluxo_completo(
    eulabs_api,
    mock_buscar_corridas,
    mock_retorna_poltronas,
    mock_bloquear_poltrona,
    mock_efetuar_reserva,
    mock_consultar_reserva,
    mock_consultar_bpe,
    mock_condicoes_cancelamento_devolver,
    mock_cancelar_venda,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    baker.make(
        Company,
        company_internal_id=eulabs_api.company.company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    with mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ):
        params_verifica_poltrona = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(params_verifica_poltrona)
    assert poltronas == [24, 25]
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert passagens_compradas.count() == 2
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == eulabs_api.company.id
    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CONFIRMADA
    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA


@pytest.mark.parametrize("mock_buscar_itinerario", [{"item_id": "123456"}], indirect=True)
def test_itinerario_external_id_from_trechoclasse(eulabs_api, mock_buscar_itinerario):
    baker.make(
        TrechoClasse,
        grupo__company_integracao_id=eulabs_api.company_id,
        external_id="123456/4",
        provider_data=json.dumps({"rota_external_id": 6573}),
    )
    response = eulabs_api.itinerario("123456/4", "datetime_ida")
    response = response.parsed
    assert response.hash == "3541579146deb2da094619d8ff1d36f6a3"
    assert response[0].local.nome_cidade == mock_summary.itinerario[2]["seccional_name"]
    assert str(response[0].local.external_local_id) == str(mock_summary.itinerario[2]["seccional_id"])
    assert str(response[0].local.external_cidade_id) == str(mock_summary.itinerario[2]["seccional_id"])
    assert (
        response[0].local.descricao
        == f"{mock_summary.itinerario[2]['seccional_name']} - {mock_summary.itinerario[2]['uf_acronym']}"
    )
    assert response[0].local.id_cidade_ibge is None
    assert response[0].distancia == 0
    assert response[0].duracao == 0
    assert response[0].tempo_embarque == 0
    assert response[1].tempo_embarque == int(mock_summary.itinerario[1]["stop_time"].split(":")[1]) * 60
    assert response[-1].distancia == mock_summary.itinerario[-1]["total_km"] - mock_summary.itinerario[-2]["total_km"]


@pytest.mark.parametrize("mock_buscar_itinerario", [{"item_id": "123456"}], indirect=True)
def test_itinerario_garante_provider_data_com_tc_mais_recente(eulabs_api, mock_buscar_itinerario):
    baker.make(
        TrechoClasse,
        external_id="123456/4",
        datetime_ida=timezone.now() - timedelta(days=30),
        grupo__company_integracao_id=eulabs_api.company_id,
    )
    baker.make(
        TrechoClasse,
        grupo__company_integracao_id=eulabs_api.company_id,
        external_id="123456/4",
        provider_data=json.dumps({"rota_external_id": 6573}),
        datetime_ida=timezone.now(),
    )
    response = eulabs_api.itinerario("123456/4", None).parsed
    assert response.hash == "3541579146deb2da094619d8ff1d36f6a3"


def test_itinerario_external_id_from_api(eulabs_api, mock_buscar_itinerario):
    datetime_ida = datetime(2022, 4, 19)
    response = eulabs_api.itinerario(f"{mock_summary_list.viagens[0]['id']}/4", datetime_ida)
    response = response.parsed
    assert response.hash == "3541579146deb2da094619d8ff1d36f6a3"


def test_descobrir_rotas_async(eulabs_api):
    next_days = 14
    shift_days = 1
    queue_name = "queue_name"
    return_task_object = False
    with mock.patch("rodoviaria.service.descobrir_rotas_eulabs_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        eulabs_api.descobrir_rotas_async(
            next_days, shift_days, queue_name, return_task_object, Company.ModeloVenda.MARKETPLACE
        )
    mock_descobrir_rotas.assert_called_once_with(
        eulabs_api.login,
        eulabs_api.company.company_internal_id,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )


def test_queue_name(eulabs_api):
    with mock.patch("commons.celery_utils.is_deployed_queue", return_value=True):
        queue_name = eulabs_api.queue_name
    assert queue_name == "bp_eucatur"


def test_buscar_servicos_por_data_erro_mais_30_dias(eulabs_api):
    data_inicio = datetime(2022, 4, 19)
    data_fim = datetime(2022, 6, 19)
    with pytest.raises(ValueError):
        eulabs_api.buscar_servicos_por_data(data_inicio, data_fim)


def test_buscar_servicos_por_data(eulabs_api, mock_viagens_por_periodo):
    data_inicio = datetime(2022, 4, 19)
    data_fim = datetime(2022, 5, 10)
    response = eulabs_api.buscar_servicos_por_data(data_inicio, data_fim)
    assert len(response) == 2
    assert response[1988028] == datetime(2022, 7, 14, 19)


def test_buscar_servicos_por_data_pega_max_data(eulabs_api, mock_tres_viagens_periodo_mesmo_external_id):
    # dado um período de busca
    data_inicio = datetime(2022, 6, 19)
    data_fim = datetime(2022, 7, 19)
    # e 3 datas (14/7 19:00 | 15/7 19:00 | 15/7 19:01) da mesma viagem (rota_external_id)
    response = eulabs_api.buscar_servicos_por_data(data_inicio, data_fim)
    assert len(response) == 1
    # garantir que apenas a última data é retornada para essa viagem
    assert response[1988030] == datetime(2022, 7, 15, 19, 1)


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize("mock_condicoes_cancelamento_cancelar", [{"item_keys": ["VIB-17416588"]}], indirect=True)
@pytest.mark.parametrize(
    "mock_cancelar_venda",
    [{"cancel_type": "Cancel", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id(
    eulabs_api, mock_consultar_reserva, mock_condicoes_cancelamento_cancelar, mock_cancelar_venda
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "cancel_response": mock_cancel_sale.sucesso,
            "poltrona": 22,
        }
    ]


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_devolver",
    [{"item_keys": ["VIB-17416588"]}],
    indirect=True,
)
@pytest.mark.parametrize(
    "mock_cancelar_venda",
    [{"cancel_type": "Devolution", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id_devolucao(
    eulabs_api, mock_consultar_reserva, mock_condicoes_cancelamento_devolver, mock_cancelar_venda
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "cancel_response": mock_cancel_sale.sucesso,
            "poltrona": 22,
        }
    ]


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_nao_autorizado",
    [{"item_keys": ["VIB-17416588"]}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id_nao_autorizada(
    eulabs_api, mock_consultar_reserva, mock_condicoes_cancelamento_nao_autorizado
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "poltrona": 22,
            "error": "Cancelamento não autorizado",
            "error_type": "nao_autorizado",
        }
    ]


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_sem_opcoes",
    [{"item_keys": ["VIB-17416588"]}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id_tipo_cancelamento_nao_encontrado(
    eulabs_api, mock_consultar_reserva, mock_condicoes_cancelamento_sem_opcoes
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "poltrona": 22,
            "error": "Tipo de cancelamento não encontrado",
            "error_type": "nao_autorizado",
        }
    ]


@pytest.mark.parametrize(
    "mock_consultar_reserva_nao_encontrada", [{"sale_id": mock_sales.pedido["sale_id"]}], indirect=True
)
def test_cancelar_reservas_por_pedido_id_reserva_nao_encontrada(eulabs_api, mock_consultar_reserva_nao_encontrada):
    with pytest.raises(EulabsAPIError):
        eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_cancelar",
    [{"item_keys": ["VIB-17416588"]}],
    indirect=True,
)
@pytest.mark.parametrize(
    "mock_cancelar_venda_timeout",
    [{"cancel_type": "Cancel", "cancel_key": mock_cancel_conditions.cancel_key_hash}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id_erro_cancelar_venda(
    eulabs_api,
    mock_consultar_reserva,
    mock_condicoes_cancelamento_cancelar,
    mock_cancelar_venda_timeout,
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    error = "524 Cloudflare successfully connected to the origin but did not provide an HTTP response"
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "poltrona": 22,
            "error": error,
            "error_type": None,
        }
    ]


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
@pytest.mark.parametrize(
    "mock_condicoes_cancelamento_erro_nao_permitido",
    [{"item_keys": ["VIB-17416588"]}],
    indirect=True,
)
def test_cancelar_reservas_por_pedido_id_erro_cancelamento_nao_permitido(
    eulabs_api, mock_consultar_reserva, mock_condicoes_cancelamento_erro_nao_permitido
):
    response = eulabs_api.cancelar_reservas_por_pedido_id(mock_sales.pedido["sale_id"])
    assert response == [
        {
            "numero_passagem": "VIB-17416588",
            "poltrona": 22,
            "error": mock.ANY,
            "error_type": "nao_autorizado",
        }
    ]


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [24, 25]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [24, 25]}], indirect=True)
def test_salva_provider_da_compra(
    eulabs_api, mock_efetuar_reserva, cached_values, mock_consultar_reserva, mock_consultar_bpe
):
    params = _comprar_params(cached_values.trecho_classe_id, quantidade_passageiros=2)
    params.poltronas = cached_values.poltronas
    response = eulabs_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=cached_values.trecho_classe_id
    ).order_by("numero_passagem")
    mock_response_conftest = copy.deepcopy(mock_efetuar_reserva)
    dict_info_passagens = {}

    # Verifica se comprou duas passagens
    assert len(response["passagens"]) == passagens.count() == 2
    passagem_um = passagens[0]
    passagem_dois = passagens[1]

    # Verifica se os dados específicos da passagens constam apenas na passagem do pax
    assert passagem_um.provider_data != passagem_dois.provider_data
    dict_info_passagens[mock_response_conftest["items"][0]["key"]] = mock_response_conftest["items"][0]
    dict_info_passagens[mock_response_conftest["items"][1]["key"]] = mock_response_conftest["items"][1]
    assert passagem_um.provider_data["item"] == dict_info_passagens[passagem_um.provider_data["item"]["key"]]
    assert passagem_dois.provider_data["item"] == dict_info_passagens[passagem_dois.provider_data["item"]["key"]]

    # Verifica se os dados gerais do provider_data são comuns a ambas as passagens
    passagem_um.provider_data.pop("item")
    passagem_dois.provider_data.pop("item")
    mock_response_conftest.pop("items")
    assert passagem_um.provider_data == mock_response_conftest
    assert passagem_dois.provider_data == mock_response_conftest


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
def test_get_atualizacao_passagem_api_parceiro_eulabs(eulabs_api, mock_consultar_reserva):
    passagem = baker.make(
        "rodoviaria.Passagem",
        pedido_external_id=84313618,
        poltrona_external_id=24,
        numero_passagem="VIB-17416588",
    )
    response = eulabs_api.get_atualizacao_passagem_api_parceiro(passagem)
    assert response.integracao == "Eulabs"
    assert response.numero_passagem == passagem.numero_passagem
    assert response.sale_id == 84313618
    assert response.numero_assento == 22
    assert response.taxa_embarque == "1.91"
    assert response.duracao == "150"
    assert response.status == "confirmada"


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [22]}], indirect=True)
def test_get_atualizacao_passagem_api_parceiro_eulabs_error(eulabs_api, mock_consultar_reserva):
    passagem = baker.make(
        "rodoviaria.Passagem",
        pedido_external_id=84313618,
        poltrona_external_id=24,
        numero_passagem=222,
    )
    with pytest.raises(
        PassengerNotRegistered, match="Eulabs - Não foi possível encontrar a passagem com numero_passagem="
    ):
        eulabs_api.get_atualizacao_passagem_api_parceiro(passagem)


@pytest.mark.parametrize("mock_consultar_reserva_components_ausente", [{"sale_id": 84313618}], indirect=True)
def test_get_atualizacao_passagem_api_parceiro_eulabs_components_null(
    eulabs_api, mock_consultar_reserva_components_ausente
):
    passagem = baker.make(
        "rodoviaria.Passagem",
        pedido_external_id=84313618,
        poltrona_external_id=24,
        numero_passagem=mock_findsale.sale["items"][0]["key"],
    )
    response = eulabs_api.get_atualizacao_passagem_api_parceiro(passagem)
    assert response.integracao == "Eulabs"
    assert response.numero_passagem == passagem.numero_passagem
    assert response.sale_id == 84313618
    assert response.taxa_embarque == "0"
    assert response.status == "confirmada"


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona", [{"travel_key": "travel_key", "selected_seat_keys": ["seat_key"]}], indirect=True
)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "travel_key"}], indirect=True)
def test_eulabs_erro_compra_documento_invalido(
    rf,
    eulabs_trechoclasses,
    mock_venda_falhou_documento_invalido,
    requests_mock,
    eulabs_api,
    mock_bloquear_poltrona,
    mock_desbloquear_poltrona,
):
    requests_mock.add(
        responses.GET,
        eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login).url,
        json=mock_travels.corridas,
    )
    # dada uma compra qualquer com o numero de documento invalido nos parametros.
    params = {
        "trechoclasse_id": eulabs_trechoclasses.ida.trechoclasse_internal_id,
        "travel_id": 217,
        "poltronas": [10],
        "valor_cheio": 305.42,
        "passageiros": [
            {
                "id": 31,
                "name": "Buseiro Marketplace",
                "cpf": "62287847065",
                "rg_number": "",
                "rg_orgao": None,
                "tipo_documento": "RG",
                "phone": "52975156354",
            },
        ],
    }
    # o fluxo de sucesso compra é: bloquear poltrona, reservar
    # o fluxo de falha é: bloquear poltrona, reserva com erro, desbloquear poltrona.
    # as requisicoes mockadas neste teste sao: bloqueio bem sucedido, reserva com falha, desbloqueio bem sucedido.
    request = rf.post(
        "/rodoviaria/v1/compra/comprar",
        data=json.dumps(params),
        content_type="application/json",
    )

    response = request_with_middleware(request)

    # em caso de falha de documento invalido. Espero receber uma response com status code compatível
    # e uma mensagem de erro clara
    assert response.status_code == 422
    data = json.loads(response.content)
    assert data["error_type"] == "invalid_document"
    assert data["error"] == "Documento inválido. Verifique os dados cadastrados."
    # assim como a passagem precisa estar com o status da falha
    passagem_status = Passagem.objects.get(travel_internal_id=217).status
    assert passagem_status == Passagem.Status.ERRO


def test_vagas_por_categoria_especial(eulabs_api, mock_buscar_corridas):
    eulabs_api.company.company_external_id = 88126
    eulabs_api.company.save()
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=1515,
        external_id="1020/2",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime(2024, 7, 10, 10, 0)),
    )
    vagas_por_categoria = eulabs_api.vagas_por_categoria_especial(trecho_classe.trechoclasse_internal_id)
    assert vagas_por_categoria == {
        Passagem.CategoriaEspecial.NORMAL: 47,
        Passagem.CategoriaEspecial.IDOSO_50: 15,
        Passagem.CategoriaEspecial.IDOSO_100: 0,
        Passagem.CategoriaEspecial.JOVEM_50: 2,
        Passagem.CategoriaEspecial.JOVEM_100: 2,
        Passagem.CategoriaEspecial.PCD: 10,
    }


def test_verifica_poltronas_sem_gratuidade(eulabs_api, mock_buscar_corridas_duas_corridas_empresas_diferentes):
    eulabs_api.company.company_external_id = 88126
    eulabs_api.company.save()
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=1515,
        external_id="1979105/4",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime(2024, 7, 10, 10, 0)),
    )
    vagas_por_categoria = eulabs_api.vagas_por_categoria_especial(trecho_classe.trechoclasse_internal_id)
    assert vagas_por_categoria == {
        Passagem.CategoriaEspecial.NORMAL: 47,
    }


def test_reserva_dict_to_comprar_params_idoso(eulabs_api):
    trecho_classe_id = 123
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    params = _comprar_params(trecho_classe_id, categoria_especial=Passagem.CategoriaEspecial.IDOSO_100)
    params.passageiros[0].dados_beneficio = DadosBeneficioForm(renda=2000, data_expiracao=date(2030, 1, 1))
    comprar_params = eulabs_api._reserva_dict_to_comprar_params(params, trecho_classe)
    assert comprar_params[0]["passagem"].categoria_especial == Passagem.CategoriaEspecial.IDOSO_100
    assert comprar_params[0]["confirmar_venda_params"]["road"]["utilizer"]["benefit"] == {
        "type": "elderly",
        "income": 2000,
        "benefit_expiration": "2030-01-01",
    }


def test_reserva_dict_to_comprar_params_jovem(eulabs_api):
    trecho_classe_id = 123
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    params = _comprar_params(trecho_classe_id, categoria_especial=Passagem.CategoriaEspecial.JOVEM_50)
    params.passageiros[0].dados_beneficio = DadosBeneficioForm(
        renda=2000, data_expiracao=date(2030, 1, 1), data_expedicao=date(2020, 1, 1), numero_beneficio="PASSE123"
    )
    comprar_params = eulabs_api._reserva_dict_to_comprar_params(params, trecho_classe)
    assert comprar_params[0]["passagem"].categoria_especial == Passagem.CategoriaEspecial.JOVEM_50
    assert comprar_params[0]["confirmar_venda_params"]["road"]["utilizer"]["benefit"] == {
        "type": "partial_young",
        "benefit_number": "PASSE123",
        "benefit_issue_date": "2020-01-01",
        "benefit_expiration": "2030-01-01",
        "enabled": True,
    }


def test_reserva_dict_to_comprar_params_pcd(eulabs_api):
    trecho_classe_id = 123
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="123456/4t",
        preco_rodoviaria=Decimal("56.00"),
    )
    params = _comprar_params(trecho_classe_id, categoria_especial=Passagem.CategoriaEspecial.PCD)
    params.passageiros[0].dados_beneficio = DadosBeneficioForm(
        renda=2000,
        data_expiracao=date(2030, 1, 1),
        data_expedicao=date(2020, 1, 1),
        numero_beneficio="PASSE123",
        auxilio_embarque=True,
        tipo_passe_livre="estadual",
    )
    comprar_params = eulabs_api._reserva_dict_to_comprar_params(params, trecho_classe)
    assert comprar_params[0]["passagem"].categoria_especial == Passagem.CategoriaEspecial.PCD
    assert comprar_params[0]["confirmar_venda_params"]["road"]["utilizer"]["benefit"] == {
        "type": "deficient",
        "benefit_number": "PASSE123",
        "benefit_expiration": "2030-01-01",
        "boarding_aid": True,
        "boarding_aid_type": "cadeirante",
        "required_companion": False,
        "validity_begin": "2020-01-01",
        "jurisdiction": "estadual",
    }


def test_buscar_corridas_menor_preco(eulabs_api, mock_buscar_corridas, override_config):
    eulabs_api.company.features = [Company.Feature.USE_PRICE_PROMOTIONAL]
    eulabs_api.company.company_external_id = mock_travels.corridas[0]["items"][0]["company"]["id"]

    corridas = eulabs_api.buscar_corridas({"origem": 5, "destino": 10, "data": "2022-04-20"}).servicos

    assert len(corridas) == 2
    assert corridas[0].preco == Decimal(str(mock_travels.corridas[0]["items"][0]["tariffs"][0]["price_promotional"]))


@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_bloquear_poltronas(eulabs_api, mocker, mock_bloquear_poltrona):
    trecho_classe_id = 123
    mocker.patch.object(
        EulabsAPI,
        "get_active_trecho_classe",
        return_value=baker.prepare(
            TrechoClasse,
            trechoclasse_internal_id=trecho_classe_id,
        ),
    )
    mocker.patch.object(EulabsAPI, "get_or_update_travel_key", return_value="travel_key")
    assert eulabs_api.bloquear_poltronas(trecho_classe_id, [1, 2]) == [1, 2]


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_bloquear_poltronas_v2(eulabs_api, mocker, mock_bloquear_poltrona):
    expected_best_before = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")) + timedelta(
        minutes=15
    )
    trecho_classe_id = 123
    mocker.patch.object(
        EulabsAPI,
        "get_active_trecho_classe",
        return_value=baker.prepare(
            TrechoClasse,
            trechoclasse_internal_id=trecho_classe_id,
        ),
    )
    mocker.patch.object(EulabsAPI, "get_or_update_travel_key", return_value="travel_key")
    result = eulabs_api.bloquear_poltronas_v2(trecho_classe_id, 1, Passagem.CategoriaEspecial.NORMAL)
    assert result == BloquearPoltronasResponse(seat=1, best_before=expected_best_before, external_payload="seat_key")


@pytest.mark.parametrize(
    "mock_bloquear_poltrona_error", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True
)
def test_bloquear_poltronas_v2_poltrona_indisponivel(eulabs_api, mocker, mock_bloquear_poltrona_error):
    trecho_classe_id = 123
    mocker.patch.object(
        EulabsAPI,
        "get_active_trecho_classe",
        return_value=baker.prepare(
            TrechoClasse,
            trechoclasse_internal_id=trecho_classe_id,
        ),
    )
    mocker.patch.object(EulabsAPI, "get_or_update_travel_key", return_value="travel_key")
    with pytest.raises(EulabsPoltronaIndisponivel):
        eulabs_api.bloquear_poltronas_v2(trecho_classe_id, 10, Passagem.CategoriaEspecial.NORMAL)


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona",
    [{"travel_key": "travel_key", "selected_seat_keys": ["seat_key"]}],
    indirect=True,
)
def test_async_desbloquear_poltronas_by_travel_key(eulabs_api, mock_desbloquear_poltrona):
    trecho_classe_id = 123123
    eulabs_api.cache.set_poltrona_key_cache(trecho_classe_id, 10, "seat_key")
    seat_keys_map = {"10": "seat_key"}
    async_desbloquear_poltronas_by_travel_key(eulabs_api.company.id, "travel_key", seat_keys_map, trecho_classe_id)
    assert eulabs_api.cache.get_poltrona_key_cache(trecho_classe_id, 10) is None


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": mock_travels.corridas[0]["key"]}], indirect=True)
def test_get_desenho_mapa_poltronas(mock_buscar_corridas, mock_retorna_poltronas, eulabs_api, eulabs_trechoclasses):
    trecho_classe_id = eulabs_trechoclasses.ida.trechoclasse_internal_id
    baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=eulabs_trechoclasses.ida,
        poltrona_external_id="25",
    )
    mapa_poltronas = eulabs_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "semi leito"
    categoria = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {"andar": 1, "assentos": []},
            {
                "andar": 2,
                "assentos": [
                    {
                        "livre": True,
                        "x": 2,
                        "y": 6,
                        "numero": 18,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("60"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 1,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 2,
                        "numero": 5,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 3,
                        "numero": 10,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 3,
                        "numero": 11,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 3,
                        "numero": 12,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 4,
                        "numero": 13,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 4,
                        "numero": 14,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 5,
                        "numero": 15,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 5,
                        "numero": 16,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 6,
                        "numero": 17,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 6,
                        "numero": 19,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 6,
                        "numero": 20,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 7,
                        "numero": 21,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 7,
                        "numero": 22,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 7,
                        "numero": 23,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 7,
                        "numero": 24,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 8,
                        "numero": 25,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 8,
                        "numero": 26,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 8,
                        "numero": 27,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 8,
                        "numero": 28,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 9,
                        "numero": 29,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 9,
                        "numero": 30,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 9,
                        "numero": 31,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 9,
                        "numero": 32,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 10,
                        "numero": 33,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 10,
                        "numero": 34,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 10,
                        "numero": 35,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 10,
                        "numero": 36,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 11,
                        "numero": 37,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 11,
                        "numero": 38,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 11,
                        "numero": 39,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 11,
                        "numero": 40,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 12,
                        "numero": 41,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 12,
                        "numero": 42,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 12,
                        "numero": 43,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 12,
                        "numero": 44,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 13,
                        "numero": 45,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 13,
                        "numero": 46,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 13,
                        "numero": 47,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 13,
                        "numero": 48,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("62.41"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 2,
                        "numero": 6,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("68.3"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 2,
                        "numero": 7,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("68.3"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 2,
                        "numero": 8,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("68.3"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 3,
                        "numero": 9,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("68.3"),
                        "categoria_especial": categoria,
                    },
                ],
            },
        ]
    }


def test_get_desenho_mapa_poltronas_viacao_adamantina(
    requests_mock, mock_buscar_corridas, eulabs_api, eulabs_trechoclasses
):
    requests_mock.add(
        responses.GET,
        eulabs_endpoints.RetornaPoltronasConfig(eulabs_api.login, travel_key=mock_travels.corridas[0]["key"]).url,
        json=[
            {
                "floor_1": [
                    {
                        "number": 49,
                        "line": 1,
                        "column": 1,
                        "busy": False,
                        "category": "SEMILEITO",
                        "tariff": 249.9,
                        "amount": 252.71,
                        "price_discount": 0,
                        "woman_space": False,
                        "benefits_values": [],
                    },
                    {
                        "number": 50,
                        "line": 1,
                        "column": 2,
                        "busy": False,
                        "category": "CONVENCIONAL",
                        "tariff": 249.9,
                        "amount": 252.71,
                        "price_discount": 0,
                        "woman_space": False,
                        "benefits_values": [],
                    },
                    {
                        "number": 51,
                        "line": 1,
                        "column": 3,
                        "busy": False,
                        "category": "SEMILEITO MASTER",
                        "tariff": 249.9,
                        "amount": 252.71,
                        "price_discount": 0,
                        "woman_space": False,
                        "benefits_values": [],
                    },
                ]
            }
        ],
    )
    eulabs_api.company.company_internal_id = VIACAO_ADAMANTINA_INTERNAL_ID
    trecho_classe_id = eulabs_trechoclasses.ida.trechoclasse_internal_id
    baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=eulabs_trechoclasses.ida,
        poltrona_external_id="25",
    )
    mapa_poltronas = eulabs_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "semi leito"
    categoria = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": True,
                        "x": 1,
                        "y": 1,
                        "numero": 49,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("252.71"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 1,
                        "numero": 50,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("252.71"),
                        "categoria_especial": categoria,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 1,
                        "numero": 51,
                        "tipo_assento": tipo_assento,
                        "preco": Decimal("252.71"),
                        "categoria_especial": categoria,
                    },
                ],
            },
            {"andar": 2, "assentos": []},
        ]
    }
