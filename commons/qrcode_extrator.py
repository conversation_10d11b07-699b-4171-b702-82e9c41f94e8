import base64
import cv2
import numpy as np


def qrcode_data_extractor(base64_str):
    img = _decode_base64_image(base64_str)
    return _extract_qrcode_data(img)

def _extract_qrcode_data(img):
    data, bbox, _ = cv2.QRCodeDetector().detectAndDecode(img)

    return data if bbox is not None else None

def _decode_base64_image(base64_str):
    image_data = base64.b64decode(base64_str)
    jpg_as_np = np.frombuffer(image_data, dtype=np.uint8)
    img = cv2.imdecode(jpg_as_np, flags=1)
    return img